import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, Zap, Brain, TrendingUp, Target, BarChart } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

const Benefits: React.FC = () => {
  const { t } = useLanguage();

  const benefits = t('profesionales_ia.benefits.items') as Array<{title: string, description: string}>;

  const benefitIcons = [
    <Clock size={32} className="text-white" />,
    <Zap size={32} className="text-white" />,
    <Brain size={32} className="text-white" />,
    <TrendingUp size={32} className="text-white" />,
    <Target size={32} className="text-white" />,
    <BarChart size={32} className="text-white" />
  ];

  return (
    <section id="beneficios" className="py-24 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl sm:text-5xl font-black mb-6">
            {t('profesionales_ia.benefits.title')}
          </h2>
          <p className="text-xl text-gray-600">
            {t('profesionales_ia.benefits.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              className="bg-white/20 backdrop-blur-md rounded-3xl border border-white/30 shadow-2xl p-6 hover:bg-white/30 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] w-16 h-16 rounded-3xl flex items-center justify-center text-white shadow-xl mb-4">
                {benefitIcons[index]}
              </div>
              <h3 className="text-2xl font-bold mb-3 text-gray-900">{benefit.title}</h3>
              <p className="text-gray-700">{benefit.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Benefits;
