"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Target, TrendingUp, Star, CheckCircle, AlertCircle } from "lucide-react";

interface LeadScoringProps {
  leadData: {
    overall_score?: number;
    scoring_factors?: {
      budget_fit?: number;
      authority_level?: number;
      need_urgency?: number;
      solution_fit?: number;
      timing_alignment?: number;
    };
    qualification_status?: string;
    next_best_action?: string;
  };
  purchaseProbability?: {
    score?: number;
    timeline?: string;
    confidence_level?: string;
  };
  delay?: number;
}

export function LeadScoring({ leadData, purchaseProbability, delay = 0.4 }: LeadScoringProps) {
  if (!leadData || Object.keys(leadData).length === 0) {
    return null;
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-100 text-green-800";
    if (score >= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const getScoreGradient = (score: number) => {
    if (score >= 80) return "bg-gradient-to-r from-green-400 to-green-600";
    if (score >= 60) return "bg-gradient-to-r from-yellow-400 to-yellow-600";
    return "bg-gradient-to-r from-red-400 to-red-600";
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'hot':
        return <Star className="h-4 w-4 text-red-500" />;
      case 'warm':
        return <TrendingUp className="h-4 w-4 text-orange-500" />;
      case 'cold':
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'hot':
        return "bg-red-100 text-red-800";
      case 'warm':
        return "bg-orange-100 text-orange-800";
      case 'cold':
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay }}
    >
      <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-purple-800">
            <Target className="h-5 w-5" />
            Scoring de Lead & Predicción
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Lead Quality Score */}
          {leadData.overall_score !== undefined && (
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="font-semibold text-gray-700">Calidad de Lead</span>
                <Badge className={getScoreColor(leadData.overall_score)}>
                  {leadData.overall_score}/100
                </Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3 mb-3">
                <div
                  className={`h-3 rounded-full ${getScoreGradient(leadData.overall_score)}`}
                  style={{ width: `${leadData.overall_score}%` }}
                ></div>
              </div>
              
              {leadData.qualification_status && (
                <div className="flex items-center gap-2">
                  {getStatusIcon(leadData.qualification_status)}
                  <Badge className={getStatusColor(leadData.qualification_status)}>
                    Status: {leadData.qualification_status.toUpperCase()}
                  </Badge>
                </div>
              )}
            </div>
          )}

          {/* Factores de Scoring Detallados */}
          {leadData.scoring_factors && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Desglose de Factores
              </h4>
              <div className="space-y-3">
                {Object.entries(leadData.scoring_factors).map(([factor, score]: [string, any]) => {
                  const factorNames: { [key: string]: string } = {
                    budget_fit: "💰 Ajuste de Presupuesto",
                    authority_level: "👑 Nivel de Autoridad",
                    need_urgency: "⚡ Urgencia de Necesidad",
                    solution_fit: "🎯 Ajuste de Solución",
                    timing_alignment: "⏰ Alineación de Timing"
                  };

                  return (
                    <div key={factor} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{factorNames[factor] || factor}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${getScoreGradient(score)}`}
                            style={{ width: `${score}%` }}
                          ></div>
                        </div>
                        <Badge variant="outline" className="text-xs min-w-[40px]">
                          {score}%
                        </Badge>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Probabilidad de Compra */}
          {purchaseProbability && purchaseProbability.score !== undefined && (
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="font-semibold text-gray-700">Probabilidad de Compra</span>
                <Badge className="bg-green-100 text-green-800">
                  {purchaseProbability.score}%
                </Badge>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                <div
                  className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full"
                  style={{ width: `${purchaseProbability.score}%` }}
                ></div>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {purchaseProbability.timeline && (
                  <Badge variant="outline" className="text-xs">
                    📅 Timeline: {purchaseProbability.timeline}
                  </Badge>
                )}
                {purchaseProbability.confidence_level && (
                  <Badge variant="outline" className="text-xs">
                    🎯 Confianza: {purchaseProbability.confidence_level}
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Próxima Mejor Acción */}
          {leadData.next_best_action && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-2 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-500" />
                Próxima Mejor Acción
              </h4>
              <div className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded">
                <p className="text-sm text-blue-800 font-medium">
                  💡 {leadData.next_best_action}
                </p>
              </div>
            </div>
          )}

          {/* Recomendaciones Basadas en Score */}
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              Recomendaciones
            </h4>
            <div className="space-y-2">
              {leadData.overall_score && leadData.overall_score >= 80 && (
                <div className="flex items-start gap-2 p-2 bg-green-50 rounded">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-green-800">Lead de Alta Calidad</p>
                    <p className="text-xs text-green-600">Priorizar contacto inmediato y asignar a vendedor senior</p>
                  </div>
                </div>
              )}
              
              {leadData.overall_score && leadData.overall_score >= 60 && leadData.overall_score < 80 && (
                <div className="flex items-start gap-2 p-2 bg-yellow-50 rounded">
                  <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">Lead Prometedor</p>
                    <p className="text-xs text-yellow-600">Nutrir con contenido relevante y hacer seguimiento regular</p>
                  </div>
                </div>
              )}
              
              {leadData.overall_score && leadData.overall_score < 60 && (
                <div className="flex items-start gap-2 p-2 bg-blue-50 rounded">
                  <AlertCircle className="h-4 w-4 text-blue-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-800">Lead para Nutrir</p>
                    <p className="text-xs text-blue-600">Incluir en campañas de nurturing y reevaluar en 3 meses</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
