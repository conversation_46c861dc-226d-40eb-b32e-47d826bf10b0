import { SEOAnalysisResult, SEOChecks } from "../types/seo";

/**
 * Calculate SEO score based on passed checks
 */
export const calculateSEOScore = (data: SEOAnalysisResult | null): number => {
  if (!data?.seo_checks) return 0;

  const checks = data.seo_checks;
  const totalChecks = Object.keys(checks).length;

  if (totalChecks === 0) return 0;

  // Count the number of passed checks (true values)
  const passedChecks = Object.values(checks).filter(Boolean).length;
  
  return Math.round((passedChecks / totalChecks) * 100);
};

/**
 * Get SEO score color based on score value
 */
export const getSEOScoreColor = (score: number): string => {
  if (score >= 80) return "text-green-600";
  if (score >= 60) return "text-yellow-600";
  return "text-red-600";
};

/**
 * Get SEO score background color for progress indicators
 */
export const getSEOScoreBackground = (score: number): string => {
  if (score >= 80) return "bg-green-100";
  if (score >= 60) return "bg-yellow-100";
  return "bg-red-100";
};

/**
 * Format SEO check key for display
 */
export const formatSEOCheckKey = (key: string): string => {
  return key
    .replace(/_/g, " ")
    .replace(/\b\w/g, (l) => l.toUpperCase());
};

/**
 * Get SEO check description
 */
export const getSEOCheckDescription = (key: string): string => {
  const descriptions: Record<string, string> = {
    has_title: "La página tiene un título definido",
    title_length_ok: "El título tiene una longitud adecuada (50-60 caracteres)",
    has_meta_description: "La página tiene una meta descripción",
    meta_description_length_ok: "La meta descripción tiene una longitud adecuada (140-160 caracteres)",
    has_h1: "La página tiene al menos una etiqueta H1",
    single_h1: "La página tiene solo una etiqueta H1",
    has_images: "La página contiene imágenes",
    images_have_alt: "Todas las imágenes tienen texto alternativo",
    has_internal_links: "La página tiene enlaces internos",
    has_external_links: "La página tiene enlaces externos",
    is_https: "La página usa HTTPS",
    has_canonical: "La página tiene URL canónica definida",
    has_meta_robots: "La página tiene directivas para robots",
    has_open_graph: "La página tiene metadatos Open Graph",
    has_twitter_card: "La página tiene metadatos Twitter Card",
  };

  return descriptions[key] || formatSEOCheckKey(key);
};

/**
 * Copy text to clipboard with error handling
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    console.error("Failed to copy text: ", err);
    return false;
  }
};

/**
 * Format time duration for display
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.round(seconds % 60);
  
  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

/**
 * Validate URL format
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Clean and format URL for display
 */
export const formatUrlForDisplay = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname + urlObj.pathname;
  } catch {
    return url;
  }
};

/**
 * Get priority color for recommendations
 */
export const getPriorityColor = (priority: string): string => {
  switch (priority.toLowerCase()) {
    case "high":
    case "alta":
      return "text-red-600 bg-red-50";
    case "medium":
    case "media":
      return "text-yellow-600 bg-yellow-50";
    case "low":
    case "baja":
      return "text-green-600 bg-green-50";
    default:
      return "text-gray-600 bg-gray-50";
  }
};

/**
 * Get impact color for recommendations
 */
export const getImpactColor = (impact: string): string => {
  switch (impact.toLowerCase()) {
    case "high":
    case "alto":
      return "text-red-600";
    case "medium":
    case "medio":
      return "text-yellow-600";
    case "low":
    case "bajo":
      return "text-green-600";
    default:
      return "text-gray-600";
  }
};
