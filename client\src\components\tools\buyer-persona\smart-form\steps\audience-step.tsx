"use client";

import { Label } from "@/components/ui/label";
import { SmartFormData } from '../types';

interface AudienceStepProps {
  formData: SmartFormData;
  updateFormData: (field: keyof SmartFormData, value: string) => void;
}

export function AudienceStep({ formData, updateFormData }: AudienceStepProps) {
  return (
    <div className="space-y-4">
      <Label htmlFor="target_audience" className="text-base font-medium text-gray-700">
        Describe tu audiencia objetivo
      </Label>
      <textarea
        id="target_audience"
        placeholder="Ej: Emprendedores de 25-40 años que buscan automatizar sus procesos de negocio..."
        value={formData.target_audience}
        onChange={(e) => updateFormData("target_audience", e.target.value)}
        rows={4}
        className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:outline-none text-lg resize-none"
      />
      <div className="text-sm text-gray-500">
        💡 Incluye edad, profesión, tamaño de empresa, ubicación, etc.
      </div>
    </div>
  );
}
