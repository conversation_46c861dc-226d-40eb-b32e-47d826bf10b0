/**
 * SEO & GPT Optimizer™ - Research Modal Header
 * Header component for the research modal
 */

import React from 'react';

interface ResearchModalHeaderProps {
  topic: string;
  savedAt: string;
  onClose: () => void;
}

const ResearchModalHeader: React.FC<ResearchModalHeaderProps> = ({
  topic,
  savedAt,
  onClose
}) => {
  return (
    <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{topic}</h2>
          <p className="text-blue-100 mt-1">
            Investigación guardada el {savedAt}
          </p>
        </div>
        <button
          onClick={onClose}
          className="text-white hover:text-gray-200 text-2xl font-bold"
        >
          ×
        </button>
      </div>
    </div>
  );
};

export default ResearchModalHeader;
