"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, Heart, Clock, Target, TrendingUp } from "lucide-react";

interface DecisionInfluencersProps {
  influencers: Array<{
    influencer: string;
    impact_level: string;
    approach?: string;
    emotional_connection?: string;
    influence_timing?: string;
  }>;
  delay?: number;
}

export function DecisionInfluencers({ influencers, delay = 0.6 }: DecisionInfluencersProps) {
  if (!influencers || influencers.length === 0) {
    return null;
  }

  const getImpactColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'alto':
      case 'high':
        return "bg-red-100 text-red-800";
      case 'medio':
      case 'medium':
        return "bg-yellow-100 text-yellow-800";
      case 'bajo':
      case 'low':
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getImpactIcon = (level: string) => {
    switch (level.toLowerCase()) {
      case 'alto':
      case 'high':
        return "🔥";
      case 'medio':
      case 'medium':
        return "⚡";
      case 'bajo':
      case 'low':
        return "💡";
      default:
        return "👤";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay }}
    >
      <Card className="bg-gradient-to-br from-purple-50 to-indigo-100 border-purple-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-purple-800">
            <Users className="h-5 w-5" />
            Influenciadores de Decisión
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {influencers.map((influencer, index) => (
            <div key={index} className="bg-white rounded-lg p-4 border-l-4 border-purple-400">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{getImpactIcon(influencer.impact_level)}</span>
                  <h4 className="font-semibold text-gray-800">
                    {influencer.influencer}
                  </h4>
                </div>
                <Badge className={getImpactColor(influencer.impact_level)}>
                  Impacto {influencer.impact_level}
                </Badge>
              </div>

              <div className="space-y-3">
                {/* Enfoque Recomendado */}
                {influencer.approach && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Target className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">Enfoque Recomendado:</span>
                    </div>
                    <p className="text-sm text-blue-700">{influencer.approach}</p>
                  </div>
                )}

                {/* Conexión Emocional */}
                {influencer.emotional_connection && (
                  <div className="p-3 bg-pink-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Heart className="h-4 w-4 text-pink-600" />
                      <span className="text-sm font-medium text-pink-800">Conexión Emocional:</span>
                    </div>
                    <p className="text-sm text-pink-700">{influencer.emotional_connection}</p>
                  </div>
                )}

                {/* Timing de Influencia */}
                {influencer.influence_timing && (
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Clock className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">Momento Óptimo:</span>
                    </div>
                    <p className="text-sm text-green-700">{influencer.influence_timing}</p>
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Estrategia de Stakeholders */}
          <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-purple-600" />
              <span className="font-semibold text-purple-800">Estrategia de Stakeholders:</span>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-purple-700">
                <strong>Prioridad Alta:</strong> {influencers.filter(i => i.impact_level.toLowerCase() === 'alto').length} influenciadores críticos
              </p>
              <p className="text-sm text-purple-700">
                <strong>Enfoque:</strong> Involucra a los influenciadores de alto impacto temprano en el proceso de venta
              </p>
              <p className="text-sm text-purple-700">
                <strong>Táctica:</strong> Personaliza el mensaje según la conexión emocional de cada stakeholder
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
