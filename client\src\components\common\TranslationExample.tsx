import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import LanguageSelector from './LanguageSelector';

const TranslationExample: React.FC = () => {
  const { t, currentLanguage } = useLanguage();

  console.log('TranslationExample render - Current language:', currentLanguage); // Debug log

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-md mx-auto">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold text-gray-800">
          {t('emma.brand_name')}
        </h2>
        <LanguageSelector variant="toggle" />
      </div>
      
      <p className="text-gray-600 mb-4">
        {t('emma.tagline')}
      </p>
      
      <div className="space-y-2 mb-4">
        <p><strong>{t('navigation.dashboard')}:</strong> {t('dashboard.welcome')}</p>
        <p><strong>{t('navigation.tools')}:</strong> {t('tools.image_generator')}</p>
        <p><strong>{t('common.loading')}:</strong> {t('common.loading')}</p>
      </div>
      
      <div className="flex gap-2">
        <button className="px-4 py-2 bg-[#3018ef] text-white rounded-lg hover:bg-[#2516d6] transition-colors">
          {t('emma.get_started')}
        </button>
        <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
          {t('emma.learn_more')}
        </button>
      </div>
      
      <div className="mt-4 text-xs text-gray-500">
        Current language: {currentLanguage.toUpperCase()}
      </div>
    </div>
  );
};

export default TranslationExample;
