"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MessageCircle, TrendingUp, <PERSON><PERSON><PERSON>riangle, Target, Brain, Lightbulb } from "lucide-react";

interface ConversationInsightsProps {
  insights: {
    persona_motivation?: string;
    communication_style_match?: string;
    trust_building_progress?: string;
    recommended_approach?: string;
  };
  analytics?: {
    emotional_indicators?: string[];
    conversation_momentum?: string;
    risk_factors?: string[];
    next_best_action?: string;
  };
  delay?: number;
}

export function ConversationInsights({ insights, analytics, delay = 0.8 }: ConversationInsightsProps) {
  if (!insights && !analytics) {
    return null;
  }

  const getMomentumColor = (momentum: string) => {
    switch (momentum?.toLowerCase()) {
      case 'creciente':
      case 'growing':
        return "bg-green-100 text-green-800";
      case 'estable':
      case 'stable':
        return "bg-blue-100 text-blue-800";
      case 'decreciente':
      case 'declining':
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getMomentumIcon = (momentum: string) => {
    switch (momentum?.toLowerCase()) {
      case 'creciente':
      case 'growing':
        return "📈";
      case 'estable':
      case 'stable':
        return "📊";
      case 'decreciente':
      case 'declining':
        return "📉";
      default:
        return "📊";
    }
  };

  const getStyleMatchColor = (match: string) => {
    switch (match?.toLowerCase()) {
      case 'excelente':
      case 'excellent':
        return "bg-green-100 text-green-800";
      case 'bueno':
      case 'good':
        return "bg-blue-100 text-blue-800";
      case 'regular':
      case 'fair':
        return "bg-yellow-100 text-yellow-800";
      case 'pobre':
      case 'poor':
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay }}
    >
      <Card className="bg-gradient-to-br from-cyan-50 to-blue-100 border-cyan-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-cyan-800">
            <Brain className="h-5 w-5" />
            Insights de Conversación
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          
          {/* Motivación Principal */}
          {insights?.persona_motivation && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Target className="h-4 w-4" />
                Motivación Principal Detectada
              </h4>
              <div className="p-3 bg-purple-50 rounded-lg border-l-4 border-purple-400">
                <p className="text-sm text-purple-700 font-medium">{insights.persona_motivation}</p>
              </div>
            </div>
          )}

          {/* Análisis de Comunicación */}
          {insights?.communication_style_match && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <MessageCircle className="h-4 w-4" />
                Compatibilidad de Estilo
              </h4>
              <Badge className={getStyleMatchColor(insights.communication_style_match)}>
                {insights.communication_style_match}
              </Badge>
              {insights.trust_building_progress && (
                <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-700">
                    <strong>Progreso de Confianza:</strong> {insights.trust_building_progress}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Momentum de Conversación */}
          {analytics?.conversation_momentum && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Momentum de Conversación
              </h4>
              <div className="flex items-center gap-2">
                <span className="text-lg">{getMomentumIcon(analytics.conversation_momentum)}</span>
                <Badge className={getMomentumColor(analytics.conversation_momentum)}>
                  {analytics.conversation_momentum}
                </Badge>
              </div>
            </div>
          )}

          {/* Indicadores Emocionales */}
          {analytics?.emotional_indicators && analytics.emotional_indicators.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Brain className="h-4 w-4" />
                Indicadores Emocionales
              </h4>
              <div className="space-y-2">
                {analytics.emotional_indicators.map((indicator, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">{indicator}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Factores de Riesgo */}
          {analytics?.risk_factors && analytics.risk_factors.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                Factores de Riesgo
              </h4>
              <div className="space-y-2">
                {analytics.risk_factors.map((risk, index) => (
                  <div key={index} className="p-2 bg-red-50 rounded border-l-4 border-red-400">
                    <span className="text-sm text-red-700">⚠️ {risk}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Próxima Acción Recomendada */}
          {(analytics?.next_best_action || insights?.recommended_approach) && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Lightbulb className="h-4 w-4" />
                Próxima Acción Recomendada
              </h4>
              <div className="space-y-3">
                {analytics?.next_best_action && (
                  <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-400">
                    <p className="text-sm text-green-700 font-medium">
                      🎯 {analytics.next_best_action}
                    </p>
                  </div>
                )}
                {insights?.recommended_approach && (
                  <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                    <p className="text-sm text-blue-700">
                      <strong>Enfoque Recomendado:</strong> {insights.recommended_approach}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Resumen Estratégico */}
          <div className="bg-gradient-to-r from-cyan-50 to-blue-50 rounded-lg p-4 border border-cyan-200">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-cyan-600" />
              <span className="font-semibold text-cyan-800">Resumen Estratégico:</span>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-cyan-700">
                <strong>Estado:</strong> {analytics?.conversation_momentum || 'En progreso'}
              </p>
              <p className="text-sm text-cyan-700">
                <strong>Riesgos:</strong> {analytics?.risk_factors?.length || 0} factores identificados
              </p>
              <p className="text-sm text-cyan-700">
                <strong>Compatibilidad:</strong> {insights?.communication_style_match || 'Evaluando'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
