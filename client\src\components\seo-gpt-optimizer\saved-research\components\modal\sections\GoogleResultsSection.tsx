/**
 * SEO & GPT Optimizer™ - Google Results Section
 * Component for displaying Google search results data
 */

import React from 'react';

interface GoogleResultsSectionProps {
  data?: any;
}

const GoogleResultsSection: React.FC<GoogleResultsSectionProps> = ({ data }) => {
  const results = data?.results || [];

  return (
    <div className="mb-6">
      <h3 className="text-lg font-bold text-gray-900 mb-3">Resultados de Google</h3>
      {results.length > 0 ? (
        <div className="bg-blue-50 rounded-xl p-4">
          <div className="mb-3">
            <span className="text-sm text-blue-600 font-medium">
              {data.total_results?.toLocaleString()} resultados encontrados
            </span>
          </div>
          <div className="space-y-3">
            {results.slice(0, 3).map((result: any, index: number) => (
              <div key={index} className="bg-white rounded-lg p-3 border border-blue-200">
                <div className="flex items-start gap-2 mb-2">
                  <span className="bg-blue-100 text-blue-600 text-xs font-bold px-2 py-1 rounded">
                    #{result.position}
                  </span>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 text-sm">{result.title}</h4>
                    <p className="text-xs text-green-600">{result.domain}</p>
                  </div>
                </div>
                <p className="text-sm text-gray-600">{result.snippet}</p>
                <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                  <span>{result.word_count} palabras</span>
                  <span>{result.reading_time}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="bg-gray-50 rounded-xl p-4 text-center">
          <p className="text-gray-500 text-sm">No hay resultados de Google disponibles</p>
        </div>
      )}
    </div>
  );
};

export default GoogleResultsSection;
