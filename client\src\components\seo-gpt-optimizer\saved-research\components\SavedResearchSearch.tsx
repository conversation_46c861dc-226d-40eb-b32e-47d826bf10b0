/**
 * SEO & GPT Optimizer™ - Saved Research Search
 * Search component for filtering saved research
 */

import React from 'react';
import { Search } from 'lucide-react';
import { useSavedResearchContext } from '../context/SavedResearchContext';

const SavedResearchSearch: React.FC = () => {
  const { searchQuery, setSearchQuery } = useSavedResearchContext();

  return (
    <div className="mb-8">
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Buscar investigaciones..."
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>
    </div>
  );
};

export default SavedResearchSearch;
