"""
Free Generation Service - Core logic for the free generation feature
"""

import time
import logging
from typing import Dict, Any, Optional
from fastapi import UploadFile, HTTPException

from app.services.ideogram_service import ideogram_service
from app.utils.prompt_utils import (
    translate_prompt_to_english,
    optimize_text_for_ideogram,
    create_enhanced_ideogram_prompt,
    generate_ad_content_for_ideogram
)

logger = logging.getLogger(__name__)


class FreeGenerationService:
    """Service for handling free generation requests with optimized Ideogram API calls."""
    
    def __init__(self):
        self.ideogram_service = ideogram_service
    
    async def generate_free_content(
        self,
        prompt: str,
        platform: str = "instagram",
        size: str = "1024x1024",
        num_images: int = 3,
        reference_images: Optional[list[UploadFile]] = None
    ) -> Dict[str, Any]:
        """
        Generate free content using optimized Ideogram API calls.
        
        Args:
            prompt: User's product/service description
            platform: Target platform
            size: Image dimensions
            num_images: Number of images to generate (1-4)
            reference_images: Optional reference images
            
        Returns:
            Dictionary with generated content and metadata
        """
        try:
            logger.info(f"🎲 Free generation request: {prompt[:100]}... (generating {num_images} versions)")

            # Validate number of images
            if num_images < 1 or num_images > 4:
                num_images = 3
                logger.warning(f"Invalid num_images, using default: {num_images}")

            # Translate prompt to English (Ideogram works better in English)
            english_prompt = translate_prompt_to_english(prompt)

            # Generate content specific to the ad using Emma AI (respecting original language)
            logger.info("🧠 Generating ad content with Emma AI...")
            ad_content = await generate_ad_content_for_ideogram(prompt, platform)

            # Optimize the generated content for better Ideogram rendering
            optimized_headline = optimize_text_for_ideogram(ad_content['headline'])
            optimized_punchline = optimize_text_for_ideogram(ad_content['punchline'])
            optimized_cta = optimize_text_for_ideogram(ad_content['cta'])

            # Create enhanced prompt following Ideogram best practices
            enhanced_prompt = create_enhanced_ideogram_prompt(
                optimized_headline,
                optimized_punchline,
                optimized_cta,
                prompt
            )

            # Validate size
            valid_sizes = ["1024x1024", "1024x1792", "1792x1024"]
            if size not in valid_sizes:
                size = "1024x1024"
                logger.warning(f"Invalid size provided, using default: {size}")

            # Handle reference images vs batch generation
            if reference_images and len(reference_images) > 0:
                # Use first reference image
                reference_image = reference_images[0]

                if reference_image and reference_image.filename:
                    logger.info("📸 Using uploaded image as style reference")

                    # Validate file type
                    if not reference_image.content_type or not reference_image.content_type.startswith("image/"):
                        raise HTTPException(
                            status_code=400,
                            detail="El archivo debe ser una imagen válida"
                        )

                    # Generate with reference image using batch generation (more efficient)
                    result = await self._generate_batch_images_with_reference(
                        enhanced_prompt, num_images, size, reference_image
                    )
                else:
                    # No valid reference image, use batch generation
                    result = await self._generate_batch_images(
                        enhanced_prompt, num_images, size
                    )
            else:
                # No reference images, use optimized batch generation
                result = await self._generate_batch_images(
                    enhanced_prompt, num_images, size
                )

            if result.get("success"):
                # Create response with metadata
                return self._create_response(
                    result, prompt, english_prompt, enhanced_prompt, platform, size, num_images
                )
            else:
                error_message = result.get("error", "Error desconocido en Ideogram")
                logger.error(f"Ideogram generation failed: {error_message}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error generando imagen: {error_message}"
                )

        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Error in free generation: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error interno en generación libre: {str(e)}"
            )

    async def _generate_with_reference_images(
        self,
        enhanced_prompt: str,
        reference_image: UploadFile,
        size: str,
        num_images: int
    ) -> Dict[str, Any]:
        """Generate multiple images with reference image (individual calls)."""
        results = []
        for i in range(num_images):
            logger.info(f"🎨 Generating version {i+1}/{num_images} with reference...")
            single_result = await self.ideogram_service.generate_with_reference(
                prompt=enhanced_prompt,
                reference_image=reference_image,
                size=size
            )
            if single_result.get("success"):
                results.append(single_result)
            else:
                logger.warning(f"Failed to generate version {i+1}")

        # Use the first result as primary
        result = results[0] if results else {"success": False, "error": "No images generated"}

        # Add all URLs generated
        if results:
            result["all_images"] = [r.get("image_url") for r in results if r.get("image_url")]
            result["num_generated"] = len(results)
            
        return result

    async def _generate_batch_images(
        self,
        enhanced_prompt: str,
        num_images: int,
        size: str
    ) -> Dict[str, Any]:
        """Generate multiple images using optimized batch API call."""
        logger.info(f"🚀 Generating {num_images} versions with Ideogram batch generation...")
        return await self.ideogram_service.generate_multiple_ads(
            prompt=enhanced_prompt,
            num_images=num_images,
            size=size
        )

    async def _generate_batch_images_with_reference(
        self,
        enhanced_prompt: str,
        num_images: int,
        size: str,
        reference_image: UploadFile
    ) -> Dict[str, Any]:
        """Generate multiple images with reference using optimized batch API call."""
        logger.info(f"🎨 Generating {num_images} versions with style reference using batch generation...")
        return await self.ideogram_service.generate_multiple_ads(
            prompt=enhanced_prompt,
            num_images=num_images,
            size=size,
            reference_image=reference_image
        )

    def _create_response(
        self,
        result: Dict[str, Any],
        original_prompt: str,
        english_prompt: str,
        enhanced_prompt: str,
        platform: str,
        size: str,
        num_requested: int
    ) -> Dict[str, Any]:
        """Create standardized response with metadata."""
        result_id = f"free_gen_{int(time.time())}"
        
        return {
            "id": result_id,
            "status": "generated",
            "message": f"🚀 ¡{result.get('num_generated', num_requested)} versiones generadas con Emma Enterprise!",
            "image_url": result.get("image_url"),  # First image as primary
            "all_images": result.get("all_images", [result.get("image_url")] if result.get("image_url") else []),
            "num_generated": result.get("num_generated", 1),
            "revised_prompt": result.get("revised_prompt"),
            "prompt_used": enhanced_prompt,
            "original_prompt": original_prompt,
            "translated_prompt": english_prompt,
            "platform": platform,
            "size": size,
            "metadata": {
                "mode": "free_generation_premium",
                "service": "ideogram",
                "model": "ideogram-3.0-quality",
                "timestamp": time.time(),
                "has_reference_image": "reference_used" in result.get("metadata", {}),
                "num_requested": num_requested,
                "num_generated": result.get("num_generated", 1),
                "batch_generation": result.get("metadata", {}).get("type") == "premium_batch_generation",
                "seed": result.get("metadata", {}).get("seed") if result.get("metadata") else None,
                "style_type": result.get("metadata", {}).get("style_type", "DESIGN") if result.get("metadata") else "DESIGN",
                "is_image_safe": result.get("metadata", {}).get("is_image_safe", True) if result.get("metadata") else True,
                "rendering_speed": "QUALITY",
                "magic_prompt": "AUTO"
            }
        }


# Global service instance
free_generation_service = FreeGenerationService()
