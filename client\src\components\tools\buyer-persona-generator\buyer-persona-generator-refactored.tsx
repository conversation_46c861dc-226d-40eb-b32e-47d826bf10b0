/**
 * Refactored Buyer Persona Generator - Main Component
 * 
 * This is the new, modular version of the buyer persona generator
 * following best practices with proper separation of concerns.
 */

import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

// Components
import { PremiumBackground } from "../buyer-persona/premium-background";
import { PremiumHeader } from "../buyer-persona/premium-header";
import { PersonaFormContainer } from "./components/persona-form-container";
import { GenerationLoading } from "../buyer-persona/generation-loading";
import { ResultsContainer } from "./components/results-container";
import { PersonaErrorBoundary } from "./components/error-boundary";
import { HistoryDashboard } from "./components/history-dashboard";

// Hooks and utilities
import { usePersonaGenerator } from "./hooks/use-persona-generator";

export default function BuyerPersonaGeneratorRefactored() {
  const {
    // State
    viewMode,
    result,
    selectedPersonaIndex,
    error,
    loadingState,
    premiumData,
    loadingPremium,
    progressMessages,
    showHistory,

    // Form
    form,

    // Refs
    resultsRef,

    // Actions
    generatePersonas,
    loadPremiumFeature,
    loadAllPremiumFeatures,
    cancelGeneration,
    backToForm,
    openConversationSimulator,
    setSelectedPersonaIndex,
    setError,
    loadFromHistory,
    toggleHistory,
  } = usePersonaGenerator();

  return (
    <PersonaErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Buyer Persona Generator Error:', error, errorInfo);
        // You can add error reporting here
      }}
    >
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50 relative overflow-hidden">
        <PremiumBackground />

        <div className="relative z-10 container mx-auto px-6 py-12 max-w-7xl">
          <PremiumHeader />

          {/* Form View */}
          {viewMode === "form" && !showHistory && (
            <PersonaFormContainer
              form={form}
              onSubmit={generatePersonas}
              isLoading={loadingState.isLoading}
              onShowHistory={toggleHistory}
            />
          )}

          {/* History Dashboard */}
          {showHistory && (
            <HistoryDashboard
              onLoadHistory={loadFromHistory}
              onClose={toggleHistory}
            />
          )}

          {/* Generation Loading View */}
          {viewMode === "generating" && (
            <GenerationLoading
              currentStage={loadingState.currentStage}
              progressValue={loadingState.progressValue}
              progressMessages={progressMessages}
              onCancel={cancelGeneration}
            />
          )}

          {/* Results View */}
          {viewMode === "results" && result && (
            <div ref={resultsRef} className="space-y-8">
              <ResultsContainer
                result={result}
                selectedPersonaIndex={selectedPersonaIndex}
                premiumData={premiumData}
                loadingPremium={loadingPremium}
                onPersonaSelect={setSelectedPersonaIndex}
                onLoadFeature={loadPremiumFeature}
                onLoadAll={loadAllPremiumFeatures}
                onOpenConversationSimulator={openConversationSimulator}
              />
            </div>
          )}

          {/* Back Button */}
          {viewMode !== "form" && (
            <div className="fixed top-6 left-6">
              <motion.button
                onClick={backToForm}
                className="flex items-center gap-2 bg-purple-100 backdrop-blur-sm border border-purple-200 rounded-full px-4 py-2 text-purple-800 hover:bg-purple-200 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ArrowLeft className="h-4 w-4" />
                Volver al Formulario
              </motion.button>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md"
            >
              <Alert variant="destructive" className="shadow-lg">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
                <button
                  onClick={() => setError(null)}
                  className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                >
                  ×
                </button>
              </Alert>
            </motion.div>
          )}
        </div>
      </div>
    </PersonaErrorBoundary>
  );
}
