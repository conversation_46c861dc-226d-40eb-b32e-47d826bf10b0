"use client"

import { motion } from "framer-motion"
import { useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"

export function InteractiveExperienceSection() {
  const { t } = useLanguage()
  const [activeStep, setActiveStep] = useState(0)

  const steps = [
    {
      icon: "💬",
      title: "Habla con Emma",
      description: "Como chatear con un amigo experto en marketing",
      emotion: "Natural y fácil",
      visual: "chat"
    },
    {
      icon: "🎨",
      title: "Ve la magia suceder",
      description: "Contenido, anuncios y estrategias creándose en vivo",
      emotion: "Fascinante y adictivo",
      visual: "creation"
    },
    {
      icon: "🎯",
      title: "Ajusta con un clic",
      description: "Cambia colores, textos, audiencias al instante",
      emotion: "Control total",
      visual: "control"
    },
    {
      icon: "📈",
      title: "Resultados en tiempo real",
      description: "Métricas, optimizaciones y mejoras automáticas",
      emotion: "Confianza absoluta",
      visual: "results"
    }
  ]

  const fears = [
    {
      fear: "¿Y si no funciona para mi negocio?",
      solution: "Emma se adapta a cualquier industria",
      icon: "🛡️"
    },
    {
      fear: "¿Y si es muy complicado de usar?",
      solution: "Más fácil que enviar un WhatsApp",
      icon: "😌"
    },
    {
      fear: "¿Y si pierdo el control de mi marketing?",
      solution: "Tú decides todo, Emma solo ejecuta",
      icon: "🎯"
    },
    {
      fear: "¿Y si no veo resultados rápido?",
      solution: "Resultados visibles desde el primer día",
      icon: "⚡"
    }
  ]

  return (
    <section className="py-12 bg-gradient-to-br from-white to-gray-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-[#3018ef] rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-[#dd3a5a] rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-6xl mx-auto">
          
          {/* Main Title */}
          <motion.div
            className="text-center mb-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 px-6 py-3 rounded-full border border-[#3018ef]/20 mb-8">
              <span className="text-2xl">🎮</span>
              <span className="text-lg font-bold text-[#3018ef]">EXPERIENCIA INTERACTIVA</span>
            </div>

            <h2 className="text-4xl sm:text-5xl md:text-6xl font-black text-gray-900 mb-8 leading-tight">
              Emma es <span className="bg-gradient-to-r from-[#3018ef] via-[#8b5cf6] to-[#dd3a5a] bg-clip-text text-transparent">
                Software
              </span>,<br />
              no un servicio
            </h2>

            <div className="max-w-5xl mx-auto space-y-6">
              <p className="text-2xl sm:text-3xl font-bold text-gray-800 leading-relaxed">
                Olvídate de formularios eternos, reuniones sin fin y esperas de semanas.
              </p>
              <p className="text-xl sm:text-2xl text-gray-600 leading-relaxed">
                Con Emma <strong className="text-[#3018ef]">hablas, clicas, ves resultados</strong>.
                Como usar Netflix, pero para hacer crecer tu negocio.
              </p>
            </div>

            {/* Visual Comparison */}
            <div className="grid md:grid-cols-2 gap-8 mt-12 max-w-4xl mx-auto">
              <motion.div
                className="bg-red-50 border-2 border-red-200 rounded-2xl p-6 text-left"
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-2xl">😤</span>
                  <h3 className="text-lg font-bold text-red-700">Agencias Tradicionales</h3>
                </div>
                <ul className="space-y-2 text-gray-700">
                  <li>📝 Llenas formularios largos</li>
                  <li>📞 Programas reuniones</li>
                  <li>⏳ Esperas propuestas</li>
                  <li>💸 Pagas por adelantado</li>
                  <li>🤞 Rezas que funcione</li>
                </ul>
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-[#3018ef]/30 rounded-2xl p-6 text-left"
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-2xl">⚡</span>
                  <h3 className="text-lg font-bold text-[#3018ef]">Emma Studio</h3>
                </div>
                <ul className="space-y-2 text-gray-700">
                  <li>💬 Hablas con Emma</li>
                  <li>🖱️ Clicas y ajustas</li>
                  <li>👀 Ves resultados live</li>
                  <li>🎯 Pagas solo lo que usas</li>
                  <li>📈 Mejoras en tiempo real</li>
                </ul>
              </motion.div>
            </div>
          </motion.div>

          {/* Interactive Steps - Redesigned */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <h3 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                ✨ Así funciona la <span className="text-[#3018ef]">experiencia Emma</span>
              </h3>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Cuatro pasos que cambiarán para siempre cómo haces marketing
              </p>
            </div>

            {/* Steps Grid */}
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              {steps.map((step, index) => (
                <motion.div
                  key={index}
                  className={`relative p-6 rounded-3xl border-2 cursor-pointer transition-all duration-500 group ${
                    activeStep === index
                      ? 'bg-gradient-to-br from-[#3018ef]/10 via-[#8b5cf6]/5 to-[#dd3a5a]/10 border-[#3018ef]/40 shadow-2xl scale-105'
                      : 'bg-white/80 backdrop-blur-sm border-gray-200 hover:border-[#3018ef]/30 hover:shadow-xl'
                  }`}
                  onClick={() => setActiveStep(index)}
                  whileHover={{ y: -5 }}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.15 }}
                >
                  {/* Step Number */}
                  <div className="absolute -top-3 -left-3 w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>

                  {/* Icon */}
                  <div className="text-4xl mb-4 text-center">
                    {step.icon}
                  </div>

                  {/* Content */}
                  <div className="text-center">
                    <h4 className="text-lg font-bold text-gray-900 mb-3">
                      {step.title}
                    </h4>
                    <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                      {step.description}
                    </p>
                    <p className="text-xs font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                      {step.emotion}
                    </p>
                  </div>

                  {/* Active Indicator */}
                  {activeStep === index && (
                    <motion.div
                      className="absolute inset-0 rounded-3xl bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 pointer-events-none"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                    />
                  )}
                </motion.div>
              ))}
            </div>

            {/* Central Visual Demo */}
            <motion.div
              className="bg-gradient-to-br from-white to-gray-50 rounded-3xl p-8 shadow-2xl border border-gray-200/50 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              {/* Header */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center gap-3 bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 px-4 py-2 rounded-full border border-[#3018ef]/20 mb-4">
                  <span className="text-2xl">{steps[activeStep].icon}</span>
                  <span className="font-bold text-[#3018ef]">{steps[activeStep].title}</span>
                </div>
                <p className="text-gray-600 text-lg">
                  {steps[activeStep].description}
                </p>
              </div>

              {/* Dynamic Interface Mockup */}
              <div className="bg-gray-900 rounded-2xl p-6 shadow-inner">
                {/* Browser Bar */}
                <div className="flex items-center gap-2 mb-4 pb-3 border-b border-gray-700">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div className="ml-4 bg-gray-800 rounded px-3 py-1 text-gray-400 text-sm">
                    emma-studio.com
                  </div>
                </div>

                {/* Dynamic Content Based on Active Step */}
                <div className="space-y-4">
                  {activeStep === 0 && (
                    <div className="space-y-3">
                      <div className="bg-[#3018ef] text-white p-3 rounded-lg text-sm">
                        "Necesito una campaña para mi tienda online de ropa"
                      </div>
                      <div className="bg-gray-800 text-gray-300 p-3 rounded-lg text-sm">
                        ✨ Perfecto! Voy a crear una estrategia completa...
                      </div>
                    </div>
                  )}

                  {activeStep === 1 && (
                    <div className="space-y-2">
                      <div className="h-3 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded animate-pulse"></div>
                      <div className="h-3 bg-gray-700 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-700 rounded w-1/2"></div>
                      <div className="text-green-400 text-xs">✅ Anuncios creados • ✅ Copy optimizado</div>
                    </div>
                  )}

                  {activeStep === 2 && (
                    <div className="grid grid-cols-3 gap-2">
                      <div className="bg-[#3018ef] h-8 rounded cursor-pointer hover:bg-[#2614d4] transition-colors"></div>
                      <div className="bg-[#dd3a5a] h-8 rounded cursor-pointer hover:bg-[#c73351] transition-colors"></div>
                      <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] h-8 rounded cursor-pointer hover:opacity-80 transition-opacity"></div>
                    </div>
                  )}

                  {activeStep === 3 && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">CTR</span>
                        <span className="text-green-400">+127% ↗</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Conversiones</span>
                        <span className="text-green-400">+89% ↗</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">ROI</span>
                        <span className="text-green-400">+234% ↗</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Bottom Status */}
                <div className="mt-6 pt-4 border-t border-gray-700 text-center">
                  <span className="text-xs font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                    {steps[activeStep].emotion}
                  </span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Fears Section */}
          <motion.div
            className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 sm:p-12 shadow-xl border border-gray-200/50"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <div className="text-center mb-12">
              <h3 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
                💭 Sabemos lo que estás pensando...
              </h3>
              <p className="text-lg text-gray-600">
                Estas son las dudas que todos tienen (y por qué no deberías preocuparte):
              </p>
            </div>

            <div className="grid sm:grid-cols-2 gap-6">
              {fears.map((item, index) => (
                <motion.div
                  key={index}
                  className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-2xl border border-gray-200"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-start gap-4">
                    <span className="text-2xl">{item.icon}</span>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-800 mb-2">
                        {item.fear}
                      </h4>
                      <p className="text-[#3018ef] font-bold">
                        ✅ {item.solution}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Final CTA */}
            <div className="text-center mt-12">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <button className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white font-bold py-4 px-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300">
                  🚀 Quiero Sentir Esta Tranquilidad
                </button>
              </motion.div>
              <p className="text-sm text-gray-500 mt-3">
                Sin compromisos • Sin contratos • Sin riesgos
              </p>
            </div>
          </motion.div>

        </div>
      </div>
    </section>
  )
}
