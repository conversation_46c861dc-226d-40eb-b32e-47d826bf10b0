import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, RefreshCw } from "lucide-react";
import { BrandAnalysis } from "../hooks/useBrandData";

// Importar imagen de Emma (la real del dashboard)
import EmmaProfile from "@/assets/emma-profile.png";

interface IntelligentEmmaMessageProps {
  step: number;
  brandData?: {
    businessName: string;
    brandColor: string;
    voice: string;
    topics: string[];
    ctas: string[];
    brandUrl?: string;
    brandDescription?: string;
  };
  brandAnalysis?: BrandAnalysis | null;
  analysisComplete?: boolean;
  color?: "blue" | "purple" | "green";
  fallbackMessage?: string;
}

const IntelligentEmmaMessage: React.FC<IntelligentEmmaMessageProps> = ({
  step,
  brandData,
  brandAnalysis,
  analysisComplete,
  color = "blue",
  fallbackMessage = "¡Hola! Estoy aquí para ayudarte con tu marca."
}) => {
  const [message, setMessage] = useState<string>(fallbackMessage);
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);

  const colorClasses = {
    blue: "bg-blue-50 border-[#3018ef] text-[#3018ef]",
    purple: "bg-purple-50 border-purple-500 text-purple-600",
    green: "bg-green-50 border-green-500 text-green-600",
  };

  // Generar mensaje inteligente con IA
  const generateIntelligentMessage = async () => {
    if (!analysisComplete && !brandData?.businessName) {
      setMessage(fallbackMessage);
      return;
    }

    setIsGenerating(true);

    try {
      const prompt = buildContextualPrompt();
      
      const response = await fetch('/api/v1/content/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          type: 'emma_message',
          tone: 'friendly',
          audience: 'user'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        const aiMessage = data.result || data.content || data.text || fallbackMessage;
        setMessage(aiMessage);
        setHasGenerated(true);
      } else {
        // Usar mensaje contextual inteligente como fallback
        const contextualMessage = generateContextualFallback();
        setMessage(contextualMessage);
        setHasGenerated(true);
      }
    } catch (error) {
      console.error('Error generating Emma message:', error);
      const contextualMessage = generateContextualFallback();
      setMessage(contextualMessage);
      setHasGenerated(true);
    } finally {
      setIsGenerating(false);
    }
  };

  // Construir prompt contextual basado en el paso y datos
  const buildContextualPrompt = (): string => {
    const baseContext = `
Eres Emma, una asistente de marketing AI amigable y experta. Genera un mensaje corto (máximo 2 oraciones) y personalizado para el usuario.

CONTEXTO DEL PASO ACTUAL: ${getStepContext()}

${brandAnalysis ? `
ANÁLISIS DE MARCA DISPONIBLE:
- Empresa: ${brandAnalysis.business_name}
- Industria: ${brandAnalysis.industry}
- Propuesta de valor: ${brandAnalysis.value_proposition}
- Audiencia: ${brandAnalysis.target_audience}
- Confianza del análisis: ${(brandAnalysis.confidence_score * 100).toFixed(0)}%
` : ''}

${brandData ? `
DATOS ACTUALES:
- Nombre: ${brandData.businessName}
- Color: ${brandData.brandColor}
- Voz: ${brandData.voice ? 'Definida' : 'No definida'}
- Topics: ${brandData.topics.length} configurados
- CTAs: ${brandData.ctas.length} configurados
` : ''}

INSTRUCCIONES:
- Sé específica sobre lo que has hecho o sugerido
- Menciona aspectos concretos de su marca si están disponibles
- Usa un tono amigable pero profesional
- Máximo 2 oraciones
- Responde SOLO el mensaje, sin comillas ni formato adicional
`;

    return baseContext;
  };

  // Obtener contexto específico del paso
  const getStepContext = (): string => {
    switch (step) {
      case 1:
        return "Paso 1 - Análisis de marca: El usuario acaba de proporcionar información de su marca.";
      case 2:
        return "Paso 2 - Detalles de marca: Configurando nombre, color y voz de la marca.";
      case 3:
        return "Paso 3 - Contenido: Configurando temas y llamadas a la acción para las publicaciones.";
      case 4:
        return "Paso 4 - Diseño: Seleccionando el tema visual para las publicaciones.";
      default:
        return "Asistiendo en la configuración del generador de posts profesional.";
    }
  };

  // Generar mensaje contextual inteligente como fallback
  const generateContextualFallback = (): string => {
    if (!analysisComplete && !brandData?.businessName) {
      return fallbackMessage;
    }

    const businessName = brandData?.businessName || brandAnalysis?.business_name || "tu marca";
    const industry = brandAnalysis?.industry || "tu industria";

    switch (step) {
      case 1:
        return `¡Perfecto! He analizado la información de ${businessName} y estoy lista para ayudarte a crear contenido increíble.`;
      
      case 2:
        if (analysisComplete && brandAnalysis) {
          return `He preparado algunos detalles basándome en ${businessName} y su enfoque en ${industry}. ¡Puedes ajustar todo como prefieras!`;
        }
        return `Excelente, ${businessName} tiene un gran potencial. Vamos a definir los detalles que harán brillar tu marca.`;
      
      case 3:
        const topicsCount = brandData?.topics.length || 0;
        const ctasCount = brandData?.ctas.length || 0;
        return `He sugerido ${topicsCount} temas y ${ctasCount} CTAs perfectos para ${businessName} en ${industry}. ¡Personalízalos como gustes!`;
      
      case 4:
        return `Basándome en el análisis de ${businessName}, he preseleccionado el tema que mejor se adapta a ${industry}. ¡Elige el que más te guste!`;
      
      default:
        return `¡Estoy aquí para ayudarte a crear contenido increíble para ${businessName}!`;
    }
  };

  // Generar mensaje automáticamente cuando cambian los datos
  useEffect(() => {
    if (!hasGenerated && (analysisComplete || brandData?.businessName)) {
      generateIntelligentMessage();
    }
  }, [analysisComplete, brandData?.businessName, step, hasGenerated]);

  // No mostrar nada si no hay datos suficientes
  if (!analysisComplete && !brandData?.businessName) {
    return null;
  }

  return (
    <div className={`${colorClasses[color].split(' ')[0]} border-l-4 ${colorClasses[color].split(' ')[1]} p-4 mb-6 relative`}>
      <div className="flex items-start gap-3">
        {/* Foto de perfil de Emma */}
        <div className="flex-shrink-0">
          <div className="w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] p-0.5 shadow-lg">
            <div className="w-full h-full rounded-full overflow-hidden bg-white flex items-center justify-center">
              <img
                src={EmmaProfile}
                alt="Emma AI"
                className="w-8 h-8 object-cover rounded-full"
                onError={(e) => {
                  // Fallback a emoji si la imagen no carga
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.parentElement!.innerHTML = '<span class="text-lg">🤖</span>';
                }}
              />
            </div>
          </div>
        </div>

        {/* Contenido del mensaje */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className={`font-medium ${colorClasses[color].split(' ')[2]} flex items-center mb-1`}>
                <Sparkles className="w-4 h-4 mr-1" />
                Emma
              </div>
              <p className="text-sm text-gray-700 leading-relaxed">
                {isGenerating ? (
                  <span className="flex items-center text-gray-500">
                    <RefreshCw className="w-3 h-3 mr-2 animate-spin" />
                    Analizando tu marca...
                  </span>
                ) : (
                  message
                )}
              </p>
            </div>

            {/* Botón para regenerar mensaje */}
            {hasGenerated && !isGenerating && (
              <button
                onClick={generateIntelligentMessage}
                className={`ml-3 p-1.5 rounded-full hover:bg-gray-100 transition-colors ${colorClasses[color].split(' ')[2]} opacity-60 hover:opacity-100 flex-shrink-0`}
                title="Regenerar mensaje"
              >
                <RefreshCw className="w-3 h-3" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntelligentEmmaMessage;
