
import { CheckCir<PERSON>, Sparkles } from 'lucide-react'
import { preciosEmma } from './CalculadoraLogic'
import { useLanguage } from '@/contexts/LanguageContext'

interface ServiciosIncluidosProps {
  moneda: 'MXN' | 'USD' | 'EUR'
}

export function ServiciosIncluidos({ moneda }: ServiciosIncluidosProps) {
  const { t } = useLanguage()

  // Obtener servicios traducidos dinámicamente
  const getServiciosEmma = () => {
    return t('calculadora.services.emma_services') as string[]
  }

  const serviciosEmma = getServiciosEmma()

  const formatCurrency = (amount: number) => {
    const validAmount = isNaN(amount) || amount === null || amount === undefined ? 0 : Math.round(amount)

    if (moneda === 'MXN') {
      return `$${validAmount.toLocaleString('es-MX')} MXN`
    } else if (moneda === 'USD') {
      return `$${validAmount.toLocaleString('en-US')} USD`
    } else if (moneda === 'EUR') {
      return `€${validAmount.toLocaleString('es-ES')} EUR`
    }

    return `${validAmount.toLocaleString()}`
  }

  return (
    <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-2xl flex items-center justify-center mx-auto mb-4">
          <Sparkles className="text-white" size={32} />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          {t('calculadora.services.title')}
        </h3>
        <p className="text-gray-600">
          {t('calculadora.services.subtitle')}{' '}
          <span className="font-bold text-[#3018ef]">
            {formatCurrency(preciosEmma[moneda].mensual)}{t('calculadora.services.per_month')}
          </span>
        </p>
      </div>

      <div className="space-y-6">
        {/* Servicios destacados en grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
          {serviciosEmma.slice(0, 12).map((servicio, index) => (
            <div
              key={index}
              className="flex items-center gap-3 p-4 bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 rounded-xl border border-[#3018ef]/10 hover:border-[#3018ef]/20 transition-all"
            >
              <CheckCircle className="text-[#3018ef] flex-shrink-0" size={16} />
              <span className="text-sm font-medium text-gray-800">{servicio}</span>
            </div>
          ))}
        </div>

        {/* Lista compacta del resto */}
        <div className="bg-gray-50 rounded-2xl p-6">
          <h4 className="text-lg font-bold text-gray-900 mb-4 text-center">
            {t('calculadora.services.more_services')}
          </h4>
          <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-2">
            {serviciosEmma.slice(12).map((servicio, index) => (
              <div key={index + 12} className="flex items-center gap-2 text-sm text-gray-700">
                <CheckCircle className="text-green-600 flex-shrink-0" size={14} />
                <span>{servicio}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-8 p-6 bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 rounded-2xl">
        <div className="text-center">
          <h4 className="text-lg font-bold text-gray-900 mb-2">
            {t('calculadora.services.why_different_title')}
          </h4>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-[#3018ef] mb-1">{t('calculadora.services.ai_advanced')}</div>
              <div className="text-gray-600">{t('calculadora.services.ai_advanced_desc')}</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-[#dd3a5a] mb-1">{t('calculadora.services.available_24_7')}</div>
              <div className="text-gray-600">{t('calculadora.services.available_24_7_desc')}</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-[#3018ef] mb-1">{t('calculadora.services.scalable')}</div>
              <div className="text-gray-600">{t('calculadora.services.scalable_desc')}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
