"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON>, Brain, MessageCircle, Globe, Sparkles, Zap } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface PremiumFeaturesProps {
  personaName: string;
  loadingPremium: string | null;
  premiumData: any;
  onLoadFeature: (feature: string) => void;
  onLoadAll: () => void;
  onOpenConversationSimulator?: () => void;
}

export function PremiumFeatures({
  personaName,
  loadingPremium,
  premiumData,
  onLoadFeature,
  onLoadAll,
  onOpenConversationSimulator
}: PremiumFeaturesProps) {
  return (
    <Card className="max-w-4xl mx-auto bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
      <CardContent className="p-6">
        <div className="text-center space-y-4">
          <h4 className="text-lg font-bold text-blue-800">
            🚀 Funcionalidades Premium con IA
          </h4>
          <p className="text-blue-600">
            Análisis avanzados con inteligencia artificial para {personaName}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
            <motion.div
              className={`bg-white p-4 rounded-lg border cursor-pointer transition-all relative ${
                loadingPremium === "avatars"
                  ? "border-blue-500 bg-blue-50 shadow-lg"
                  : "border-blue-200 hover:shadow-lg hover:border-blue-300"
              }`}
              onClick={() => !loadingPremium && onLoadFeature("avatars")}
              whileHover={!loadingPremium ? { scale: 1.05 } : {}}
              whileTap={!loadingPremium ? { scale: 0.95 } : {}}
            >
              {loadingPremium === "avatars" ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Zap className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                </motion.div>
              ) : (
                <User className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              )}
              <h5 className="font-semibold text-sm">
                {loadingPremium === "avatars" ? "Generando..." : "Avatar AI"}
              </h5>
              <p className="text-xs text-gray-600">
                {loadingPremium === "avatars" ? "IA trabajando" : "Imagen realista"}
              </p>
              {premiumData?.avatars && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </motion.div>

            <motion.div
              className={`bg-white p-4 rounded-lg border cursor-pointer transition-all relative ${
                loadingPremium === "behavior"
                  ? "border-purple-500 bg-purple-50 shadow-lg"
                  : "border-purple-200 hover:shadow-lg hover:border-purple-300"
              }`}
              onClick={() => !loadingPremium && onLoadFeature("behavior")}
              whileHover={!loadingPremium ? { scale: 1.05 } : {}}
              whileTap={!loadingPremium ? { scale: 0.95 } : {}}
            >
              {loadingPremium === "behavior" ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Zap className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                </motion.div>
              ) : (
                <Brain className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              )}
              <h5 className="font-semibold text-sm">
                {loadingPremium === "behavior" ? "Analizando..." : "Predicción IA"}
              </h5>
              <p className="text-xs text-gray-600">
                {loadingPremium === "behavior" ? "IA trabajando" : "Comportamiento"}
              </p>
              {premiumData?.behavior && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </motion.div>

            <motion.div
              className={`bg-white p-4 rounded-lg border cursor-pointer transition-all relative ${
                loadingPremium === "conversation"
                  ? "border-purple-500 bg-purple-50 shadow-lg"
                  : "border-purple-200 hover:shadow-lg hover:border-purple-300"
              }`}
              onClick={() => {
                if (!loadingPremium) {
                  if (premiumData?.conversation && onOpenConversationSimulator) {
                    onOpenConversationSimulator();
                  } else {
                    onLoadFeature("conversation");
                  }
                }
              }}
              whileHover={!loadingPremium ? { scale: 1.05 } : {}}
              whileTap={!loadingPremium ? { scale: 0.95 } : {}}
            >
              {loadingPremium === "conversation" ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Zap className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                </motion.div>
              ) : (
                <MessageCircle className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              )}
              <h5 className="font-semibold text-sm">
                {loadingPremium === "conversation"
                  ? "Preparando..."
                  : premiumData?.conversation
                    ? "Abrir Chat"
                    : "Simulador IA"
                }
              </h5>
              <p className="text-xs text-gray-600">
                {loadingPremium === "conversation"
                  ? "IA trabajando"
                  : premiumData?.conversation
                    ? "Listo para chatear"
                    : "Conversaciones"
                }
              </p>
              {premiumData?.conversation && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </motion.div>

            <motion.div
              className={`bg-white p-4 rounded-lg border cursor-pointer transition-all relative ${
                loadingPremium === "geographic"
                  ? "border-purple-500 bg-purple-50 shadow-lg"
                  : "border-purple-200 hover:shadow-lg hover:border-purple-300"
              }`}
              onClick={() => !loadingPremium && onLoadFeature("geographic")}
              whileHover={!loadingPremium ? { scale: 1.05 } : {}}
              whileTap={!loadingPremium ? { scale: 0.95 } : {}}
            >
              {loadingPremium === "geographic" ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                >
                  <Zap className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                </motion.div>
              ) : (
                <Globe className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              )}
              <h5 className="font-semibold text-sm">
                {loadingPremium === "geographic" ? "Analizando..." : "Análisis Geo"}
              </h5>
              <p className="text-xs text-gray-600">
                {loadingPremium === "geographic" ? "IA trabajando" : "Cultural IA"}
              </p>
              {premiumData?.geographic && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </motion.div>
          </div>

          <div className="flex gap-3 justify-center mt-6">
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white"
              onClick={onLoadAll}
              disabled={loadingPremium !== null}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Cargar Todas las Funcionalidades IA
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
