/**
 * SEO & GPT Optimizer™ - Saved Research Header
 * Header component with navigation and actions
 */

import React from 'react';
import { ArrowLeft, Settings } from 'lucide-react';

interface SavedResearchHeaderProps {
  onBack: () => void;
  onManage: () => void;
}

const SavedResearchHeader: React.FC<SavedResearchHeaderProps> = ({
  onBack,
  onManage
}) => {
  return (
    <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-100 rounded-xl transition-colors duration-200"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Investigaciones Guardadas</h1>
              <p className="text-gray-600 text-sm">
                Revisa y gestiona tus investigaciones guardadas
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={onManage}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-200"
            >
              <Settings className="w-4 h-4" />
              Gestionar y Organizar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SavedResearchHeader;
