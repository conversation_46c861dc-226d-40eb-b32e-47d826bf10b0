/**
 * Lead Scoring tab - Scoring and next best actions
 */

import { BuyerPersona, PremiumFeatureData } from "../../buyer-persona-generator/types";

interface LeadScoringTabProps {
  persona: BuyerPersona;
  premiumData?: PremiumFeatureData | null;
}

export function LeadScoringTab({ persona, premiumData }: LeadScoringTabProps) {
  // Debug logging to identify the problematic object
  console.log('🔍 LeadScoringTab Debug:', {
    persona: persona,
    personaType: typeof persona,
    personaKeys: persona ? Object.keys(persona) : 'null',
    objections: persona?.objections,
    objectionsType: typeof persona?.objections,
    isObjectionsArray: Array.isArray(persona?.objections),
    premiumData: premiumData
  });

  // Validate persona data
  if (!persona || typeof persona !== 'object') {
    return (
      <div className="space-y-6">
        <div className="px-4">
          <p className="text-red-600">Error: Datos de persona no válidos</p>
        </div>
      </div>
    );
  }

  // Ensure objections is always an array of strings
  const safeObjections = (() => {
    console.log('🔍 Processing objections:', persona.objections, typeof persona.objections);

    if (!persona.objections) {
      console.log('🔍 No objections found, using defaults');
      return ['Precio elevado', 'Complejidad de implementación'];
    }

    if (Array.isArray(persona.objections)) {
      const mapped = persona.objections.map(obj => {
        const result = typeof obj === 'string' ? obj : String(obj);
        console.log('🔍 Mapping objection:', obj, typeof obj, '->', result);
        return result;
      });
      console.log('🔍 Mapped objections:', mapped);
      return mapped;
    }

    if (typeof persona.objections === 'string') {
      console.log('🔍 Single string objection:', persona.objections);
      return [persona.objections];
    }

    console.log('🔍 Invalid objections format, using defaults');
    return ['Precio elevado', 'Complejidad de implementación'];
  })();

  // Calculate a mock lead score based on persona data
  const calculateLeadScore = () => {
    let score = 50; // Base score

    // Add points based on job level
    const jobTitle = persona.job?.title || '';
    if (jobTitle.toLowerCase().includes('director') || jobTitle.toLowerCase().includes('ceo')) {
      score += 20;
    } else if (jobTitle.toLowerCase().includes('manager') || jobTitle.toLowerCase().includes('senior')) {
      score += 15;
    }

    // Add points based on company size
    const companySize = persona.job?.company_size || '';
    if (companySize === 'Grande' || companySize === 'Corporativa') {
      score += 15;
    } else if (companySize === 'Mediana') {
      score += 10;
    }

    // Add points based on income level
    const incomeLevel = persona.income_level || '';
    if (incomeLevel === 'Alto' || incomeLevel === 'Medio-alto') {
      score += 10;
    }

    return Math.min(score, 100);
  };

  const leadScore = premiumData?.behavior?.predictions?.lead_quality_score || calculateLeadScore();
  const purchaseProbability = premiumData?.behavior?.predictions?.purchase_probability || (leadScore * 0.8);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getRecommendation = () => {
    if (leadScore >= 80) {
      return {
        action: "Contacto inmediato con demo personalizada",
        priority: "Alta",
        timeline: "Dentro de 24 horas"
      };
    } else if (leadScore >= 60) {
      return {
        action: "Envío de contenido relevante y seguimiento",
        priority: "Media",
        timeline: "Dentro de 3-5 días"
      };
    } else {
      return {
        action: "Nutrición con contenido educativo",
        priority: "Baja",
        timeline: "Seguimiento semanal"
      };
    }
  };

  const recommendation = getRecommendation();

  return (
    <div className="space-y-6">
      {/* Section Title */}
      <h2 className="text-[#101419] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
        Puntuación y Recomendaciones
      </h2>

      {/* Lead Score Overview */}
      <p className="text-[#101419] text-base font-normal leading-normal pb-3 pt-1 px-4">
        El puntaje de lead de {String(persona.name || 'Usuario')} es {String(leadScore)}, indicando una {leadScore >= 80 ? 'alta' : leadScore >= 60 ? 'media' : 'baja'} probabilidad de conversión.
        La próxima mejor acción es {String(recommendation?.action?.toLowerCase() || 'contacto directo')}. Recomendamos adaptar nuestras estrategias de ventas y marketing
        para destacar la capacidad de nuestra plataforma de entregar resultados medibles y mantenerse a la vanguardia de las tendencias de la industria.
      </p>

      {/* Score Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 px-4">
        {/* Lead Score */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="text-center">
            <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${getScoreColor(leadScore)} mb-4`}>
              <span className="text-2xl font-bold">{String(leadScore)}</span>
            </div>
            <h3 className="text-[#101419] text-lg font-semibold mb-2">Lead Score</h3>
            <p className="text-[#57738e] text-sm">Puntuación de calidad del lead</p>
          </div>
        </div>

        {/* Purchase Probability */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-blue-100 text-blue-600 mb-4">
              <span className="text-2xl font-bold">{String(Math.round(purchaseProbability))}%</span>
            </div>
            <h3 className="text-[#101419] text-lg font-semibold mb-2">Probabilidad de Compra</h3>
            <p className="text-[#57738e] text-sm">Likelihood de conversión</p>
          </div>
        </div>

        {/* Priority Level */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="text-center">
            <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full ${
              recommendation?.priority === 'Alta' ? 'bg-red-100 text-red-600' :
              recommendation?.priority === 'Media' ? 'bg-yellow-100 text-yellow-600' :
              'bg-green-100 text-green-600'
            } mb-4`}>
              <span className="text-sm font-bold">{String(recommendation?.priority || 'Media')}</span>
            </div>
            <h3 className="text-[#101419] text-lg font-semibold mb-2">Prioridad</h3>
            <p className="text-[#57738e] text-sm">Nivel de urgencia</p>
          </div>
        </div>
      </div>

      {/* Next Best Action */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Próxima Mejor Acción</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Acción Recomendada</h4>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-[#101419] font-medium">{String(recommendation?.action || 'Contacto directo con propuesta personalizada')}</p>
                <p className="text-[#57738e] text-sm mt-2">Timeline: {String(recommendation?.timeline || 'Dentro de 1-2 semanas')}</p>
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Estrategia de Enfoque</h4>
              <ul className="space-y-2">
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                  <span className="text-[#101419] text-sm">Destacar ROI y resultados medibles</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                  <span className="text-[#101419] text-sm">Enfocarse en eficiencia y optimización</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                  <span className="text-[#101419] text-sm">Proporcionar casos de estudio relevantes</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Objections Handling */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Manejo de Objeciones</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="space-y-4">
            {safeObjections.map((objectionText, index) => {
              // Ensure objectionText is a string
              const safeObjText = String(objectionText || `Objeción ${index + 1}`);
              const lowerObjText = safeObjText.toLowerCase();

              console.log('🔍 Rendering objection:', { objectionText, safeObjText, index, type: typeof objectionText });

              return (
                <div key={index} className="border-l-4 border-orange-400 pl-4">
                  <h4 className="text-[#101419] font-semibold mb-2">Objeción: "{safeObjText}"</h4>
                  <p className="text-[#57738e] text-sm">
                    {lowerObjText.includes('precio') &&
                      "Enfatizar el ROI y los ahorros a largo plazo. Proporcionar calculadora de valor y casos de éxito."
                    }
                    {lowerObjText.includes('tiempo') &&
                      "Destacar la facilidad de implementación y el soporte dedicado. Ofrecer plan de implementación gradual."
                    }
                    {lowerObjText.includes('complejidad') &&
                      "Demostrar la interfaz intuitiva y proporcionar training personalizado. Mostrar testimonios de usuarios similares."
                    }
                    {!lowerObjText.includes('precio') &&
                     !lowerObjText.includes('tiempo') &&
                     !lowerObjText.includes('complejidad') &&
                      "Abordar con datos específicos, testimonios relevantes y propuesta de valor clara."
                    }
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Premium Lead Intelligence */}
      {premiumData?.behavior && (
        <div className="px-4">
          <h3 className="text-[#101419] text-lg font-semibold mb-4">Inteligencia Premium de Lead</h3>
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-3 h-3 bg-purple-500 rounded-full" />
              <span className="text-purple-800 font-semibold">Análisis Avanzado Disponible</span>
            </div>
            <p className="text-[#101419] mb-4">
              Datos adicionales de comportamiento y predicciones avanzadas están disponibles con el análisis premium.
            </p>
            {premiumData.behavior.predictions?.conversion_channels && Array.isArray(premiumData.behavior.predictions.conversion_channels) && (
              <div className="bg-white rounded-lg p-4">
                <h4 className="text-[#101419] font-semibold mb-2">Canales de Conversión Óptimos</h4>
                <div className="space-y-2">
                  {premiumData.behavior.predictions.conversion_channels.slice(0, 3).map((channel, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-[#101419]">{String(channel?.channel || 'Canal desconocido')}</span>
                      <span className="text-[#57738e] text-sm">{String(Math.round((channel?.effectiveness || 0) * 100))}% efectividad</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
