import { motion } from "framer-motion";
import { MessageSquare, CheckCircle, Star, ArrowRight } from "lucide-react";

export default function HumanServices() {
  const experts = [
    {
      name: "<PERSON>",
      role: "Fotógrafo Profesional",
      rating: 4.9,
      price: "Desde €90/hora",
      specialty: "Fotografía de Producto",
      image:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    },
    {
      name: "<PERSON>",
      role: "Diseñadora UX/UI",
      rating: 4.8,
      price: "Desde €75/hora",
      specialty: "Diseño Web & App",
      image:
        "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    },
    {
      name: "<PERSON>",
      role: "Editor de Video",
      rating: 4.7,
      price: "Desde €85/hora",
      specialty: "Anuncios & Sociales",
      image:
        "https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    },
    {
      name: "Ana Rodríguez",
      role: "Locutora Profesional",
      rating: 5.0,
      price: "Desde €60/hora",
      specialty: "Podcasts & Anuncios",
      image:
        "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3",
    },
  ];

  const categories = [
    "Diseño Gráfico",
    "Fotografía",
    "Video",
    "Desarrollo Web",
    "Locución",
    "Redacción",
    "Ilustración",
    "3D & Animación",
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Servicios Humanos Profesionales
          </h2>
          <p className="text-xl max-w-2xl mx-auto font-bold">
            Cuando necesitas el toque humano para complementar tu equipo IA
          </p>
        </motion.div>

        {/* Categorías/Filtros */}
        <motion.div
          className="flex flex-wrap justify-center gap-3 mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {categories.map((category, index) => (
            <motion.button
              key={index}
              className="py-2 px-4 bg-white rounded-full border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)] font-bold text-sm"
              whileHover={{
                y: -3,
                boxShadow: "5px 5px 0px 0px rgba(0,0,0,0.9)",
                backgroundColor: "#f3f4f6",
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 * index }}
            >
              {category}
            </motion.button>
          ))}
        </motion.div>

        {/* Expertos destacados */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {experts.map((expert, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.1 * index + 0.3 }}
              whileHover={{
                y: -10,
                boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
                transition: { duration: 0.2 },
              }}
            >
              <div className="h-48 bg-gray-200 relative overflow-hidden">
                <img
                  src={expert.image}
                  alt={expert.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-3 right-3 bg-white rounded-full px-3 py-1 text-sm font-bold border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">
                  {expert.specialty}
                </div>
              </div>

              <div className="p-5">
                <h3 className="text-lg font-black mb-1">{expert.name}</h3>
                <p className="text-gray-600 text-sm mb-2">{expert.role}</p>

                <div className="flex justify-between items-center mb-3">
                  <div className="flex items-center">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400 mr-1" />
                    <span className="font-bold text-sm">{expert.rating}</span>
                  </div>
                  <span className="text-sm font-bold text-gray-700">
                    {expert.price}
                  </span>
                </div>

                <motion.button
                  className="w-full py-2 px-4 bg-green-50 rounded-lg text-green-700 font-bold text-sm border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)] flex items-center justify-center"
                  whileHover={{
                    y: -3,
                    boxShadow: "5px 5px 0px 0px rgba(0,0,0,0.9)",
                  }}
                >
                  <MessageSquare size={16} className="mr-2" />
                  Contactar
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Proceso de contratación */}
        <motion.div
          className="bg-white rounded-2xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] p-8 mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h3 className="text-2xl font-black text-center mb-8">
            Proceso Simple y Efectivo
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              {
                step: "1",
                title: "Selecciona un Servicio",
                description:
                  "Navega por nuestro marketplace y elige el servicio que necesitas",
              },
              {
                step: "2",
                title: "Habla con el Profesional",
                description: "Discute tu proyecto y expectativas directamente",
              },
              {
                step: "3",
                title: "Recibe tu Trabajo",
                description: "Obtén resultados de calidad profesional",
              },
              {
                step: "4",
                title: "Integra con tus Agentes IA",
                description: "Combina el trabajo humano con tus agentes IA",
              },
            ].map((step, index) => (
              <motion.div
                key={index}
                className="text-center relative"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2 * index, duration: 0.4 }}
              >
                {index < 3 && (
                  <div className="hidden md:block absolute top-10 right-0 w-full h-1 border-t-2 border-dashed border-black z-0 transform translate-x-1/2"></div>
                )}

                <div className="w-16 h-16 bg-blue-100 rounded-full border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] flex items-center justify-center mx-auto mb-4 relative z-10">
                  <span className="text-2xl font-black">{step.step}</span>
                </div>
                <h4 className="text-lg font-black mb-2">{step.title}</h4>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Integración con IA */}
        <motion.div
          className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 flex flex-col md:flex-row items-center justify-between mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="mb-6 md:mb-0 md:mr-6">
            <h3 className="text-xl font-black mb-2">
              Lo Mejor de Ambos Mundos
            </h3>
            <p className="text-gray-700">
              Combina la eficiencia y escalabilidad de la IA con la creatividad
              y especialización humana.
            </p>
          </div>

          <div className="flex flex-wrap gap-3">
            {[
              "Integraciones automáticas",
              "Workflows híbridos",
              "Comunicación directa",
            ].map((feature, index) => (
              <div key={index} className="flex items-center">
                <CheckCircle size={16} className="text-green-600 mr-1" />
                <span className="text-sm font-bold">{feature}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <motion.button
            className="bg-blue-500 text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-blue-600 transition-all duration-300 inline-flex items-center"
            whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
            whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
          >
            Explorar Marketplace
            <ArrowRight size={20} className="ml-2" />
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
