/**
 * SEO & GPT Optimizer™ - Saved Research Grid
 * Grid component for displaying research cards
 */

import React from 'react';
import { SavedResearch } from '../../../../hooks/seo-gpt-optimizer/useSavedResearch';
import SavedResearchCard from './SavedResearchCard';

interface SavedResearchGridProps {
  research: SavedResearch[];
}

const SavedResearchGrid: React.FC<SavedResearchGridProps> = ({ research }) => {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {research.map((item) => (
        <SavedResearchCard key={item.id} research={item} />
      ))}
    </div>
  );
};

export default SavedResearchGrid;
