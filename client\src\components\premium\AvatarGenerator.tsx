"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  User,
  Download,
  Sparkles,
  RefreshCw,
  Eye,
  Heart,
  AlertCircle,
  Wand2,
} from "lucide-react";

interface AvatarData {
  avatar_id: string;
  avatar_url: string;
  avatar_base64: string;
  style: string;
  characteristics: {
    gender: string;
    age: number;
    ethnicity: string;
    style: string;
    description: string;
  };
  metadata: {
    generated_at: string;
    resolution: string;
    format: string;
  };
}

interface AvatarGeneratorProps {
  personaData: any;
  onAvatarSelected?: (avatar: AvatarData) => void;
}

export default function AvatarGenerator({ personaData, onAvatarSelected }: AvatarGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [avatars, setAvatars] = useState<AvatarData[]>([]);
  const [selectedAvatar, setSelectedAvatar] = useState<AvatarData | null>(null);
  const [selectedStyle, setSelectedStyle] = useState("professional");
  const [selectedGender, setSelectedGender] = useState("neutral");
  const [selectedEthnicity, setSelectedEthnicity] = useState("diverse");
  const [error, setError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewAvatar, setPreviewAvatar] = useState<AvatarData | null>(null);

  const avatarStyles = [
    { id: "professional", name: "Profesional", description: "Traje de negocios, expresión confiada" },
    { id: "casual", name: "Casual", description: "Ropa casual, sonrisa amigable" },
    { id: "creative", name: "Creativo", description: "Estilo artístico, moda única" },
    { id: "executive", name: "Ejecutivo", description: "Traje formal, presencia autoritaria" }
  ];

  const genderOptions = [
    { id: "neutral", name: "Neutral" },
    { id: "male", name: "Masculino" },
    { id: "female", name: "Femenino" }
  ];

  const ethnicityOptions = [
    { id: "diverse", name: "Diverso" },
    { id: "caucasian", name: "Caucásico" },
    { id: "hispanic", name: "Hispano" },
    { id: "asian", name: "Asiático" },
    { id: "african", name: "Africano" },
    { id: "middle_eastern", name: "Medio Oriente" }
  ];

  const generateAvatar = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch("/api/v1/premium/avatars/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          persona_description: personaData?.personal_background || personaData?.name || "Professional persona",
          style: selectedStyle,
          gender: selectedGender,
          age: personaData?.age || 35,
          ethnicity: selectedEthnicity
        }),
      });

      if (!response.ok) {
        throw new Error("Error al generar avatar");
      }

      const result = await response.json();
      
      if (result.status === "success") {
        setAvatars(prev => [result, ...prev]);
        setSelectedAvatar(result);
        if (onAvatarSelected) {
          onAvatarSelected(result);
        }
      } else {
        throw new Error(result.error_message || "Error al generar avatar");
      }

    } catch (err: any) {
      setError(err.message || "Error al generar avatar");
    } finally {
      setIsGenerating(false);
    }
  };

  const generateGallery = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch("/api/v1/premium/avatars/gallery", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          persona_description: personaData?.personal_background || personaData?.name || "Professional persona",
          style: selectedStyle,
          gender: selectedGender,
          age: personaData?.age || 35,
          ethnicity: selectedEthnicity
        }),
      });

      if (!response.ok) {
        throw new Error("Error al generar galería");
      }

      const result = await response.json();
      
      if (result.status === "success") {
        setAvatars(result.gallery);
        if (result.gallery.length > 0) {
          setSelectedAvatar(result.gallery[0]);
          if (onAvatarSelected) {
            onAvatarSelected(result.gallery[0]);
          }
        }
      } else {
        throw new Error("Error al generar galería");
      }

    } catch (err: any) {
      setError(err.message || "Error al generar galería");
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadAvatar = (avatar: AvatarData) => {
    const link = document.createElement('a');
    link.href = avatar.avatar_url;
    link.download = `avatar-${avatar.avatar_id}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const previewAvatarHandler = (avatar: AvatarData) => {
    setPreviewAvatar(avatar);
    setShowPreview(true);
  };

  const selectAvatar = (avatar: AvatarData) => {
    setSelectedAvatar(avatar);
    if (onAvatarSelected) {
      onAvatarSelected(avatar);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
          <Sparkles className="h-5 w-5 text-purple-600" />
          Generador de Avatares AI
        </h3>
        <p className="text-gray-600">
          Crea avatares realistas para tu buyer persona usando inteligencia artificial
        </p>
      </div>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            Configuración del Avatar
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Estilo</label>
              <Select value={selectedStyle} onValueChange={setSelectedStyle}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {avatarStyles.map((style) => (
                    <SelectItem key={style.id} value={style.id}>
                      {style.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Género</label>
              <Select value={selectedGender} onValueChange={setSelectedGender}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {genderOptions.map((gender) => (
                    <SelectItem key={gender.id} value={gender.id}>
                      {gender.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Etnia</label>
              <Select value={selectedEthnicity} onValueChange={setSelectedEthnicity}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ethnicityOptions.map((ethnicity) => (
                    <SelectItem key={ethnicity.id} value={ethnicity.id}>
                      {ethnicity.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              onClick={generateAvatar}
              disabled={isGenerating}
              className="flex-1"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Generando...
                </>
              ) : (
                <>
                  <User className="h-4 w-4 mr-2" />
                  Generar Avatar
                </>
              )}
            </Button>

            <Button
              onClick={generateGallery}
              disabled={isGenerating}
              variant="outline"
              className="flex-1"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Generando...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Generar Galería
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Avatar Gallery */}
      {avatars.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Galería de Avatares</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              <AnimatePresence>
                {avatars.map((avatar, index) => (
                  <motion.div
                    key={avatar.avatar_id}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ delay: index * 0.1 }}
                    className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                      selectedAvatar?.avatar_id === avatar.avatar_id
                        ? "border-blue-500 ring-2 ring-blue-200"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => selectAvatar(avatar)}
                  >
                    <img
                      src={avatar.avatar_url}
                      alt={`Avatar ${avatar.characteristics.style}`}
                      className="w-full h-32 object-cover"
                    />
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={(e) => {
                            e.stopPropagation();
                            previewAvatarHandler(avatar);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={(e) => {
                            e.stopPropagation();
                            downloadAvatar(avatar);
                          }}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Selected indicator */}
                    {selectedAvatar?.avatar_id === avatar.avatar_id && (
                      <div className="absolute top-2 right-2">
                        <Heart className="h-5 w-5 text-red-500 fill-current" />
                      </div>
                    )}

                    {/* Style badge */}
                    <div className="absolute bottom-2 left-2">
                      <Badge variant="secondary" className="text-xs">
                        {avatar.characteristics.style}
                      </Badge>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Vista Previa del Avatar</DialogTitle>
            <DialogDescription>
              Avatar generado con IA para {personaData?.name || "tu buyer persona"}
            </DialogDescription>
          </DialogHeader>
          
          {previewAvatar && (
            <div className="space-y-4">
              <img
                src={previewAvatar.avatar_url}
                alt="Avatar preview"
                className="w-full rounded-lg"
              />
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Estilo:</span>
                  <Badge>{previewAvatar.characteristics.style}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Edad:</span>
                  <span>{previewAvatar.characteristics.age} años</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Resolución:</span>
                  <span>{previewAvatar.metadata.resolution}</span>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={() => selectAvatar(previewAvatar)}
                  className="flex-1"
                >
                  Seleccionar
                </Button>
                <Button
                  onClick={() => downloadAvatar(previewAvatar)}
                  variant="outline"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
