/**
 * History button component for accessing generation history
 */

import { motion } from "framer-motion";
import { History } from "lucide-react";

interface HistoryButtonProps {
  onShowHistory: () => void;
}

export function HistoryButton({ onShowHistory }: HistoryButtonProps) {
  return (
    <div className="flex justify-center">
      <motion.button
        onClick={onShowHistory}
        className="flex items-center gap-2 bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm border border-[#3018ef]/20 rounded-full px-4 py-2 text-[#3018ef] hover:bg-gradient-to-r hover:from-[#3018ef]/20 hover:to-[#dd3a5a]/20 transition-all duration-300"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <History className="h-4 w-4" />
        Ver Historial de Generaciones
      </motion.button>
    </div>
  );
}
