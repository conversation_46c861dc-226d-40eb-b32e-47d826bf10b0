import React from "react";
import { motion } from "framer-motion";
import { <PERSON>R<PERSON>, Briefcase, <PERSON>T<PERSON>, <PERSON><PERSON>hart, MessageSquare } from "lucide-react";
import { <PERSON> } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

const FeaturedAgents: React.FC = () => {
  const { t } = useLanguage();

  const featuredAgents = t('profesionales_ia.featured_agents.agents') as Array<{
    name: string;
    role: string;
    description: string;
    specialty: string;
    projects: number;
  }>;

  const agentIcons = [
    <Briefcase size={32} className="text-white" />,
    <PenTool size={32} className="text-white" />,
    <BarChart size={32} className="text-white" />,
    <MessageSquare size={32} className="text-white" />
  ];

  return (
    <section id="agentes" className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-[#3018ef]/5 to-white">
      <div className="container mx-auto">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl sm:text-5xl font-black mb-6">
            {t('profesionales_ia.featured_agents.title')}
          </h2>
          <p className="text-xl text-gray-600">
            {t('profesionales_ia.featured_agents.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {featuredAgents.map((agent, index) => (
            <motion.div
              key={index}
              className="relative"
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 + index * 0.2 }}
              whileHover={{ scale: 1.05 }}
            >
              <div className="bg-white/20 backdrop-blur-md rounded-3xl border border-white/30 shadow-2xl overflow-hidden h-full">
                <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] p-4 text-white">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-bold">{t('profesionales_ia.featured_agents.premium_agent')}</h3>
                    <span className="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-xs font-bold border border-white/30">
                      {t('profesionales_ia.featured_agents.available')}
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-20 h-20 bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] rounded-3xl flex items-center justify-center shadow-xl text-white">
                      {agentIcons[index]}
                    </div>
                    <div>
                      <h4 className="text-2xl font-black text-gray-900">{agent.name}</h4>
                      <p className="text-[#3018ef] font-bold">{agent.role}</p>
                    </div>
                  </div>
                  <div className="border-t border-white/20 pt-4">
                    <div className="flex justify-between mb-3">
                      <span className="font-bold text-gray-900">{t('profesionales_ia.featured_agents.specialty')}</span>
                      <span className="font-medium text-gray-700">{agent.specialty}</span>
                    </div>
                    <div className="flex justify-between mb-3">
                      <span className="font-bold text-gray-900">{t('profesionales_ia.featured_agents.availability')}</span>
                      <span className="font-medium text-green-600">{t('profesionales_ia.featured_agents.availability_247')}</span>
                    </div>
                    <div className="flex justify-between mb-4">
                      <span className="font-bold text-gray-900">{t('profesionales_ia.featured_agents.completed_projects')}</span>
                      <span className="font-medium text-gray-700">{agent.projects}</span>
                    </div>
                    <p className="text-gray-700 mb-6">{agent.description}</p>
                    <Link href="/login">
                      <motion.button
                        className="w-full mt-2 bg-[#dd3a5a] text-white font-bold py-4 px-8 rounded-3xl shadow-xl hover:bg-[#c73351] transition-all duration-300"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span className="flex items-center justify-center gap-2">
                          {t('profesionales_ia.featured_agents.hire_now')} <ArrowRight size={18} />
                        </span>
                      </motion.button>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedAgents;
