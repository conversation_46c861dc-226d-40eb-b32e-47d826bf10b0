"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Shield, Clock, Target } from "lucide-react";

interface ObjectionsAnalysisProps {
  objections: Array<{
    objection: string;
    probability: number;
    emotional_root?: string;
    counter_strategy?: string;
    timing_to_address?: string;
  }>;
  delay?: number;
}

export function ObjectionsAnalysis({ objections, delay = 0.5 }: ObjectionsAnalysisProps) {
  if (!objections || objections.length === 0) {
    return null;
  }

  const getProbabilityColor = (probability: number) => {
    if (probability >= 80) return "bg-red-100 text-red-800";
    if (probability >= 60) return "bg-orange-100 text-orange-800";
    return "bg-yellow-100 text-yellow-800";
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay }}
    >
      <Card className="bg-gradient-to-br from-red-50 to-orange-100 border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            Análisis de Objeciones
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {objections.map((objection, index) => (
            <div key={index} className="bg-white rounded-lg p-4 border-l-4 border-red-400">
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-gray-800 flex-1">
                  {objection.objection}
                </h4>
                <Badge className={getProbabilityColor(objection.probability)}>
                  {objection.probability}% probabilidad
                </Badge>
              </div>

              <div className="space-y-3">
                {/* Raíz Emocional */}
                {objection.emotional_root && (
                  <div className="p-3 bg-pink-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <AlertTriangle className="h-4 w-4 text-pink-600" />
                      <span className="text-sm font-medium text-pink-800">Raíz Emocional:</span>
                    </div>
                    <p className="text-sm text-pink-700">{objection.emotional_root}</p>
                  </div>
                )}

                {/* Estrategia de Respuesta */}
                {objection.counter_strategy && (
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Shield className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">Estrategia de Respuesta:</span>
                    </div>
                    <p className="text-sm text-green-700">{objection.counter_strategy}</p>
                  </div>
                )}

                {/* Timing para Abordar */}
                {objection.timing_to_address && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Clock className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">Cuándo Abordar:</span>
                    </div>
                    <p className="text-sm text-blue-700">{objection.timing_to_address}</p>
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Resumen de Estrategia */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-blue-600" />
              <span className="font-semibold text-blue-800">Estrategia General:</span>
            </div>
            <p className="text-sm text-blue-700">
              Prepárate para {objections.length} objeciones principales. 
              Enfócate en las de mayor probabilidad ({objections.filter(o => o.probability >= 70).length} críticas) 
              y aborda las raíces emocionales antes que los aspectos racionales.
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
