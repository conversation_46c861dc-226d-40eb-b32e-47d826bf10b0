import React, { createContext, useContext, ReactNode } from 'react';
import { useTranslation, Language } from '@/hooks/useTranslation';

// Language context type
interface LanguageContextType {
  currentLanguage: Language;
  changeLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isLoading: boolean;
  availableLanguages: Language[];
}

// Create context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Provider component
interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const translation = useTranslation();

  return (
    <LanguageContext.Provider value={translation}>
      {children}
    </LanguageContext.Provider>
  );
};

// Hook to use language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Export types for convenience
export type { Language, LanguageContextType };
