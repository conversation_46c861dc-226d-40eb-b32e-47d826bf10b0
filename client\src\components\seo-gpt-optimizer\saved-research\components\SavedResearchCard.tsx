/**
 * SEO & GPT Optimizer™ - Saved Research Card
 * Individual research card component
 */

import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, Eye, Download, Trash2 } from 'lucide-react';
import { SavedResearch } from '../../../../hooks/seo-gpt-optimizer/useSavedResearch';
import { useSavedResearchContext } from '../context/SavedResearchContext';

interface SavedResearchCardProps {
  research: SavedResearch;
}

const SavedResearchCard: React.FC<SavedResearchCardProps> = ({ research }) => {
  const {
    handleViewResearch,
    handleDeleteResearch,
    handleExportResearch
  } = useSavedResearchContext();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <motion.div
      className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-200"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <div className="flex items-start justify-between mb-4">
        <h3 className="font-semibold text-gray-900 text-lg line-clamp-2">
          {research.topic}
        </h3>
      </div>

      <div className="space-y-2 mb-4 text-sm text-gray-600">
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4" />
          {formatDate(research.savedAt)}
        </div>
        <div>
          Confianza: {(research.confidence * 100).toFixed(1)}%
        </div>
        <div>
          Procesamiento: {research.processingTime.toFixed(1)}s
        </div>
      </div>

      <div className="flex items-center gap-2">
        <button
          onClick={() => handleViewResearch(research)}
          className="flex items-center gap-1 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 text-sm"
        >
          <Eye className="w-4 h-4" />
          Ver
        </button>
        <button
          onClick={() => handleExportResearch(research)}
          className="flex items-center gap-1 px-3 py-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 text-sm"
        >
          <Download className="w-4 h-4" />
          Descargar
        </button>
        <button
          onClick={() => handleDeleteResearch(research.id)}
          className="flex items-center gap-1 px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 text-sm"
        >
          <Trash2 className="w-4 h-4" />
          Eliminar
        </button>
      </div>
    </motion.div>
  );
};

export default SavedResearchCard;
