/**
 * Content Creation Step Component
 * Second step of the simplified ad creator - content input and editing
 */

import { Wand2 } from "lucide-react";
import { PlatformConfig } from "@/types/ad-creator-types";
import { SizeOption } from "../types/simplified-ad-types";
import { ProductImageUpload } from "../components/ProductImageUpload";
import { ProductDescription } from "../components/ProductDescription";
import { TextFieldsEditor } from "../components/TextFieldsEditor";
import { LivePreview } from "../components/LivePreview";

interface ContentCreationStepProps {
  platform: string;
  config: PlatformConfig;
  selectedSize: SizeOption | null;
  prompt: string;
  setPrompt: (prompt: string) => void;
  uploadedImage: File | null;
  setUploadedImage: (image: File | null) => void;
  headline: string;
  setHeadline: (headline: string) => void;
  punchline: string;
  setPunchline: (punchline: string) => void;
  cta: string;
  setCta: (cta: string) => void;
  onGenerate: () => void;
  isGenerating: boolean;
}

export function ContentCreationStep({
  platform,
  config,
  selectedSize,
  prompt,
  setPrompt,
  uploadedImage,
  setUploadedImage,
  headline,
  setHeadline,
  punchline,
  setPunchline,
  cta,
  setCta,
  onGenerate,
  isGenerating
}: ContentCreationStepProps) {
  return (
    <div className="max-w-7xl mx-auto px-4">
      {/* Header Section */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-3">
          <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
            Crea Contenido Profesional
          </span>
        </h2>
        <p className="text-lg text-slate-600 max-w-2xl mx-auto mb-6">
          Define cada elemento de tu anuncio con precisión y genera textos optimizados con IA
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Panel - Content Inputs */}
        <div className="space-y-6">
          {/* Upload Product Image */}
          <ProductImageUpload
            uploadedImage={uploadedImage}
            onImageUpload={setUploadedImage}
          />

          {/* Product Description */}
          <ProductDescription
            prompt={prompt}
            onPromptChange={setPrompt}
          />

          {/* Text Fields Editor */}
          <TextFieldsEditor
            platform={platform}
            prompt={prompt}
            headline={headline}
            punchline={punchline}
            cta={cta}
            onHeadlineChange={setHeadline}
            onPunchlineChange={setPunchline}
            onCtaChange={setCta}
          />

          {/* Next Step Button */}
          <div className="relative">
            <button
              onClick={onGenerate}
              disabled={!prompt.trim() || isGenerating}
              className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#2516d6] hover:to-[#c73650] text-white py-4 px-6 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl disabled:opacity-50 disabled:hover:scale-100 flex items-center justify-center gap-3 shadow-lg"
            >
              {isGenerating ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Generating...
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5" />
                  Next Step →
                </>
              )}
            </button>
            {!prompt.trim() && (
              <div className="absolute -bottom-8 left-0 text-xs text-red-500 flex items-center gap-1">
                <div className="w-1 h-1 bg-red-500 rounded-full"></div>
                Descripción del producto requerida
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Enhanced Preview */}
        <div className="space-y-6">
          <LivePreview
            headline={headline}
            punchline={punchline}
            cta={cta}
            uploadedImage={uploadedImage}
          />
        </div>
      </div>
    </div>
  );
}
