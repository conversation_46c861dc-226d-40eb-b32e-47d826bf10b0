/**
 * Tool Suggester Component
 * Suggests relevant tools based on conversation context
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs';
import { Search, Wrench, Star, Clock } from 'lucide-react';

// Tool categories
const TOOL_CATEGORIES = [
  'Content',
  'SEO',
  'Social Media',
  'Analytics',
  'Design',
  'Marketing'
];

// Tool definitions
interface ToolDefinition {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ReactNode;
}

const TOOLS: ToolDefinition[] = [
  {
    id: 'content-generator',
    name: 'Content Generator',
    description: 'Generate high-quality content for blogs, social media, and more',
    category: 'Content',
    icon: <Wrench className="h-5 w-5" />
  },
  {
    id: 'content-calendar',
    name: 'Content Calendar',
    description: 'Plan and schedule your content across multiple platforms',
    category: 'Content',
    icon: <Clock className="h-5 w-5" />
  },
  {
    id: 'keyword-research',
    name: 'Keyword Research Tool',
    description: 'Find high-value keywords for your SEO strategy',
    category: 'SEO',
    icon: <Search className="h-5 w-5" />
  },
  {
    id: 'seo-analyzer',
    name: 'SEO Analyzer',
    description: 'Analyze your content for SEO optimization opportunities',
    category: 'SEO',
    icon: <Wrench className="h-5 w-5" />
  },
  {
    id: 'social-scheduler',
    name: 'Social Media Scheduler',
    description: 'Schedule and manage your social media posts',
    category: 'Social Media',
    icon: <Clock className="h-5 w-5" />
  },
  {
    id: 'hashtag-generator',
    name: 'Hashtag Generator',
    description: 'Generate relevant hashtags for your social media posts',
    category: 'Social Media',
    icon: <Wrench className="h-5 w-5" />
  },
  {
    id: 'data-visualizer',
    name: 'Data Visualizer',
    description: 'Create beautiful visualizations of your data',
    category: 'Analytics',
    icon: <Wrench className="h-5 w-5" />
  },
  {
    id: 'performance-dashboard',
    name: 'Performance Dashboard',
    description: 'Track and analyze your marketing performance',
    category: 'Analytics',
    icon: <Wrench className="h-5 w-5" />
  },
  {
    id: 'campaign-planner',
    name: 'Campaign Planner',
    description: 'Plan and organize your marketing campaigns',
    category: 'Marketing',
    icon: <Wrench className="h-5 w-5" />
  },
  {
    id: 'marketing-calendar',
    name: 'Marketing Calendar',
    description: 'Schedule and manage your marketing activities',
    category: 'Marketing',
    icon: <Clock className="h-5 w-5" />
  }
];

interface ToolSuggesterProps {
  suggestedTools: string[];
  mainTopic: string | null;
  userIntent: string | null;
  className?: string;
}

export const ToolSuggester: React.FC<ToolSuggesterProps> = ({
  suggestedTools,
  mainTopic,
  userIntent,
  className = ''
}) => {
  // Local state
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [activeCategory, setActiveCategory] = useState<string>('suggested');

  // Filter tools based on search query
  const filteredTools = TOOLS.filter((tool) => {
    if (searchQuery) {
      return (
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (activeCategory === 'suggested') {
      return suggestedTools.some((suggestedTool) =>
        tool.name.toLowerCase().includes(suggestedTool.toLowerCase())
      );
    }

    if (activeCategory === 'all') {
      return true;
    }

    return tool.category === activeCategory;
  });

  // Get suggested tools
  const suggestedToolsList = TOOLS.filter((tool) =>
    suggestedTools.some((suggestedTool) =>
      tool.name.toLowerCase().includes(suggestedTool.toLowerCase())
    )
  );

  // Handle tool selection
  const handleSelectTool = (tool: ToolDefinition) => {
    // This would launch the tool in a real implementation
    console.log('Selected tool:', tool);
    alert(`Tool "${tool.name}" would launch here`);
  };

  // Render a tool card
  const renderToolCard = (tool: ToolDefinition) => {
    const isSuggested = suggestedTools.some((suggestedTool) =>
      tool.name.toLowerCase().includes(suggestedTool.toLowerCase())
    );

    return (
      <Card
        key={tool.id}
        className={`cursor-pointer hover:shadow-md transition-shadow ${
          isSuggested ? 'border-blue-200' : ''
        }`}
        onClick={() => handleSelectTool(tool)}
      >
        <CardHeader className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {tool.icon}
              <CardTitle className="text-base ml-2">{tool.name}</CardTitle>
            </div>
            {isSuggested && (
              <Star className="h-4 w-4 text-yellow-400" />
            )}
          </div>
          <CardDescription className="text-xs mt-1">
            {tool.description}
          </CardDescription>
        </CardHeader>
      </Card>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search tools..."
            className="pl-8"
          />
        </div>
      </div>

      <Tabs value={activeCategory} onValueChange={setActiveCategory}>
        <TabsList className="w-full">
          <TabsTrigger value="suggested" className="flex-1">
            Suggested
            {suggestedToolsList.length > 0 && (
              <span className="ml-1 text-xs bg-blue-100 text-blue-800 rounded-full px-2">
                {suggestedToolsList.length}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger value="all" className="flex-1">All</TabsTrigger>
          {TOOL_CATEGORIES.map((category) => (
            <TabsTrigger key={category} value={category} className="flex-1">
              {category}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeCategory} className="mt-4">
          {filteredTools.length === 0 ? (
            <div className="text-center text-gray-500 p-4">
              No tools found
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredTools.map(renderToolCard)}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {mainTopic && userIntent && (
        <Card className="mt-4">
          <CardHeader className="p-4">
            <CardTitle className="text-sm">Context-Aware Recommendations</CardTitle>
            <CardDescription className="text-xs">
              Based on your conversation about <strong>{mainTopic}</strong> with the intent to <strong>{userIntent?.replace('_', ' ')}</strong>
            </CardDescription>
          </CardHeader>
          <CardFooter className="p-4 pt-0">
            <Button variant="outline" size="sm" className="w-full">
              View Personalized Workflow
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  );
};
