import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Image as ImageIcon, 
  AlertTriangle, 
  Eye, 
  Code, 
  Copy,
  ExternalLink,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ProblematicImage {
  position: number;
  src: string;
  alt_status: 'missing' | 'empty';
  width: string;
  height: string;
  class: string;
  title: string;
  html_snippet: string;
  image_type: string;
}

interface ProblematicImagesTabProps {
  images: {
    total: number;
    without_alt: number;
    without_alt_percentage: number;
    without_alt_details: ProblematicImage[];
  };
}

const ProblematicImagesTab: React.FC<ProblematicImagesTabProps> = ({ images }) => {
  const [selectedImage, setSelectedImage] = useState<ProblematicImage | null>(null);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const getImageTypeInfo = (type: string) => {
    const types = {
      'tracking_pixel': {
        name: 'Pixel de Tracking',
        description: 'Imagen de seguimiento (Facebook, Google Analytics)',
        priority: 'baja',
        solution: 'Agregar alt="" (imagen decorativa)'
      },
      'avatar': {
        name: 'Avatar/Perfil',
        description: 'Imagen de perfil de usuario',
        priority: 'media',
        solution: 'Agregar alt="Avatar de [nombre]" o "Foto de perfil"'
      },
      'logo': {
        name: 'Logo',
        description: 'Logo de la empresa o marca',
        priority: 'alta',
        solution: 'Agregar alt="Logo de [nombre empresa]"'
      },
      'screenshot': {
        name: 'Captura de Pantalla',
        description: 'Imagen de captura de pantalla',
        priority: 'alta',
        solution: 'Describir qué muestra la captura específicamente'
      },
      'content_image': {
        name: 'Imagen de Contenido',
        description: 'Imagen principal del contenido',
        priority: 'crítica',
        solution: 'Describir detalladamente lo que muestra la imagen'
      },
      'icon': {
        name: 'Icono',
        description: 'Icono o símbolo',
        priority: 'media',
        solution: 'Describir la función del icono'
      },
      'inline_image': {
        name: 'Imagen Inline',
        description: 'Imagen SVG o data URI',
        priority: 'media',
        solution: 'Describir el contenido visual'
      },
      'unknown': {
        name: 'Tipo Desconocido',
        description: 'Tipo de imagen no identificado',
        priority: 'media',
        solution: 'Analizar manualmente y agregar descripción apropiada'
      }
    };
    
    return types[type as keyof typeof types] || types.unknown;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'crítica': return 'bg-red-100 text-red-800 border-red-200';
      case 'alta': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'media': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'baja': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Error copying to clipboard:', err);
    }
  };

  const generateFixedHTML = (img: ProblematicImage) => {
    const typeInfo = getImageTypeInfo(img.image_type);
    const suggestedAlt = typeInfo.solution.includes('alt=""') 
      ? '""' 
      : '"Descripción sugerida basada en el contenido visual"';
    
    return img.html_snippet.replace(
      /alt="[^"]*"/g, 
      `alt=${suggestedAlt}`
    ).replace(
      /<img(?![^>]*alt=)/,
      `<img alt=${suggestedAlt}`
    );
  };

  if (images.without_alt === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-green-700 mb-2">
            ¡Excelente! Todas las imágenes tienen alt text
          </h3>
          <p className="text-gray-600">
            Las {images.total} imágenes de tu página tienen texto alternativo apropiado.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>{images.without_alt} de {images.total} imágenes</strong> necesitan texto alternativo 
          ({images.without_alt_percentage.toFixed(1)}% del total)
        </AlertDescription>
      </Alert>

      {/* Problematic Images List */}
      <div className="grid gap-4">
        {images.without_alt_details.map((img, index) => {
          const typeInfo = getImageTypeInfo(img.image_type);
          
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="border-l-4 border-l-red-500">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-red-100 rounded-lg">
                        <ImageIcon className="w-5 h-5 text-red-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">
                          Imagen #{img.position} - {typeInfo.name}
                        </CardTitle>
                        <p className="text-sm text-gray-600">{typeInfo.description}</p>
                      </div>
                    </div>
                    <Badge className={`${getPriorityColor(typeInfo.priority)} border`}>
                      Prioridad {typeInfo.priority}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Image Preview */}
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      {img.src && !img.src.includes('tr?') && !img.src.startsWith('data:') ? (
                        <div className="relative">
                          <img 
                            src={img.src} 
                            alt="Vista previa de imagen problemática"
                            className="w-24 h-24 object-cover rounded-lg border border-gray-200"
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                          />
                          <div className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1">
                            <XCircle className="w-4 h-4" />
                          </div>
                        </div>
                      ) : (
                        <div className="w-24 h-24 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center">
                          <ImageIcon className="w-8 h-8 text-gray-400" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 space-y-2">
                      <div>
                        <strong>Problema:</strong> {img.alt_status === 'missing' ? 'Sin atributo alt' : 'Alt text vacío'}
                      </div>
                      <div>
                        <strong>Solución:</strong> {typeInfo.solution}
                      </div>
                      {img.src && (
                        <div className="flex items-center gap-2">
                          <strong>URL:</strong> 
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {img.src.length > 50 ? img.src.substring(0, 50) + '...' : img.src}
                          </code>
                          {img.src.startsWith('http') && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(img.src, '_blank')}
                            >
                              <ExternalLink className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* HTML Code */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium flex items-center gap-2">
                        <Code className="w-4 h-4" />
                        Código HTML Actual
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(img.html_snippet, index)}
                      >
                        {copiedIndex === index ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                    <pre className="bg-gray-50 p-3 rounded-lg text-xs overflow-x-auto border">
                      <code>{img.html_snippet}</code>
                    </pre>
                  </div>

                  {/* Fixed HTML */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium flex items-center gap-2 text-green-700">
                        <CheckCircle className="w-4 h-4" />
                        Código HTML Corregido
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(generateFixedHTML(img), index + 1000)}
                      >
                        {copiedIndex === index + 1000 ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                    <pre className="bg-green-50 p-3 rounded-lg text-xs overflow-x-auto border border-green-200">
                      <code>{generateFixedHTML(img)}</code>
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Action Summary */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-2">Próximos Pasos</h4>
              <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                <li>Copia el código HTML corregido de cada imagen</li>
                <li>Reemplaza el código original en tu sitio web</li>
                <li>Vuelve a analizar con Emma SEO para verificar los cambios</li>
                <li>Monitorea el impacto en el ranking de búsqueda de imágenes</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProblematicImagesTab;
