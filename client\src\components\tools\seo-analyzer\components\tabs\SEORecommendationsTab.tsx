import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import SEORecommendations from "@/components/tools/seo-recommendations";
import { SEORecommendation } from "../../types/seo";

interface SEORecommendationsTabProps {
  recommendations: SEORecommendation[];
  url: string;
}

export const SEORecommendationsTab: React.FC<SEORecommendationsTabProps> = ({
  recommendations,
  url,
}) => {
  return (
    <TabsContent value="recommendations">
      {recommendations && recommendations.length > 0 ? (
        <SEORecommendations
          recommendations={recommendations}
          url={url}
        />
      ) : (
        <div className="py-8 text-center">
          <p className="text-muted-foreground">
            No se encontraron recomendaciones o problemas
            importantes para esta URL
          </p>
        </div>
      )}
    </TabsContent>
  );
};
