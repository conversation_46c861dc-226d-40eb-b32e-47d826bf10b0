import React from 'react'
import { Calculator, DollarSign } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'

interface CalculadoraInputs {
  moneda: 'MXN' | 'USD' | 'EUR'
  gastoMensual: number
  tipoGasto: 'agencia' | 'herramientas' | 'ambos'
  serviciosActuales: string[]
  limitacionesActuales: string
}

interface CalculadoraFormProps {
  inputs: CalculadoraInputs
  onInputChange: (field: keyof CalculadoraInputs, value: any) => void
}





export function CalculadoraForm({ inputs, onInputChange }: CalculadoraFormProps) {
  const { t } = useLanguage()

  const getCurrencySymbol = () => {
    const symbols = { MXN: '$', USD: '$', EUR: '€' }
    return symbols[inputs.moneda]
  }

  const getCurrencyOptions = () => [
    { value: 'USD', label: '🇺🇸 USD', name: t('calculadora.form.currency_options.usd') },
    { value: 'MXN', label: '🇲🇽 MXN', name: t('calculadora.form.currency_options.mxn') },
    { value: 'EUR', label: '🇪🇺 EUR', name: t('calculadora.form.currency_options.eur') }
  ]

  const getServiciosComunes = () => {
    return t('calculadora.form.common_services') as string[]
  }

  const getTiposGasto = () => [
    {
      value: 'agencia',
      label: t('calculadora.form.spend_types.agencia'),
      description: t('calculadora.form.spend_descriptions.agencia'),
      limitaciones: t('calculadora.form.limitations.agencia')
    },
    {
      value: 'herramientas',
      label: t('calculadora.form.spend_types.herramientas'),
      description: t('calculadora.form.spend_descriptions.herramientas'),
      limitaciones: t('calculadora.form.limitations.herramientas')
    },
    {
      value: 'ambos',
      label: t('calculadora.form.spend_types.ambos'),
      description: t('calculadora.form.spend_descriptions.ambos'),
      limitaciones: t('calculadora.form.limitations.ambos')
    }
  ]

  return (
    <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-[#3018ef] rounded-xl flex items-center justify-center">
          <Calculator className="text-white" size={20} />
        </div>
        <div>
          <h3 className="text-xl font-bold text-gray-900">{t('calculadora.form.title')}</h3>
          <p className="text-gray-600">{t('calculadora.form.subtitle')}</p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Selector de Moneda */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            {t('calculadora.form.currency_label')}
          </label>
          <div className="grid grid-cols-3 gap-3">
            {getCurrencyOptions().map((moneda) => (
              <button
                key={moneda.value}
                onClick={() => onInputChange('moneda', moneda.value)}
                className={`p-4 rounded-2xl border-2 transition-all text-center ${
                  inputs.moneda === moneda.value
                    ? 'border-[#3018ef] bg-[#3018ef]/5 text-[#3018ef]'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                <div className="text-lg font-bold">{moneda.label}</div>
                <div className="text-sm">{moneda.name}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Tipo de Gasto */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            {t('calculadora.form.current_spend_question')}
          </label>
          <div className="space-y-3">
            {getTiposGasto().map((tipo) => (
              <button
                key={tipo.value}
                onClick={() => onInputChange('tipoGasto', tipo.value)}
                className={`w-full p-4 rounded-2xl border-2 transition-all text-left ${
                  inputs.tipoGasto === tipo.value
                    ? 'border-[#3018ef] bg-[#3018ef]/5'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className={`font-semibold ${
                      inputs.tipoGasto === tipo.value ? 'text-[#3018ef]' : 'text-gray-900'
                    }`}>
                      {tipo.label}
                    </div>
                    <div className="text-sm text-gray-600 mb-2">{tipo.description}</div>
                    {inputs.tipoGasto === tipo.value && (
                      <div className="text-xs text-red-600">
                        <div className="font-medium mb-1">{t('calculadora.form.limitations_title')}</div>
                        <ul className="list-disc list-inside space-y-1">
                          {tipo.limitaciones.map((limitacion, idx) => (
                            <li key={idx}>{limitacion}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                  <div className={`w-5 h-5 rounded-full border-2 flex-shrink-0 ${
                    inputs.tipoGasto === tipo.value
                      ? 'border-[#3018ef] bg-[#3018ef]'
                      : 'border-gray-300'
                  }`}>
                    {inputs.tipoGasto === tipo.value && (
                      <div className="w-full h-full rounded-full bg-white scale-50"></div>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Gasto Mensual */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            {t('calculadora.form.monthly_spend_question').replace('($)', `(${getCurrencySymbol()})`)}
          </label>
          <div className="relative">
            <DollarSign className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="number"
              min="0"
              step="100"
              value={inputs.gastoMensual || ''}
              onChange={(e) => onInputChange('gastoMensual', parseInt(e.target.value) || 0)}
              placeholder="Ej: 5000"
              className="w-full pl-12 pr-4 py-4 text-xl font-semibold rounded-2xl border-2 border-gray-200 focus:border-[#3018ef] focus:ring-4 focus:ring-[#3018ef]/10 outline-none transition-all"
            />
          </div>
          <div className="mt-2 text-sm text-gray-500">
            {t('calculadora.form.monthly_spend_description')}
          </div>
        </div>

        {/* Servicios Actuales */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            {t('calculadora.form.current_services_question')}
          </label>
          <div className="grid grid-cols-2 gap-2">
            {getServiciosComunes().map((servicio) => (
              <button
                key={servicio}
                onClick={() => {
                  const serviciosActuales = inputs.serviciosActuales || []
                  const isSelected = serviciosActuales.includes(servicio)
                  const newServicios = isSelected
                    ? serviciosActuales.filter(s => s !== servicio)
                    : [...serviciosActuales, servicio]
                  onInputChange('serviciosActuales', newServicios)
                }}
                className={`p-3 rounded-xl border text-sm transition-all ${
                  inputs.serviciosActuales?.includes(servicio)
                    ? 'border-[#3018ef] bg-[#3018ef]/10 text-[#3018ef]'
                    : 'border-gray-200 hover:border-gray-300 text-gray-700'
                }`}
              >
                {servicio}
              </button>
            ))}
          </div>
        </div>

        {/* Limitaciones Actuales */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            {t('calculadora.form.current_limitations_question')}
          </label>
          <textarea
            value={inputs.limitacionesActuales || ''}
            onChange={(e) => onInputChange('limitacionesActuales', e.target.value)}
            placeholder={t('calculadora.form.current_limitations_placeholder')}
            className="w-full px-4 py-3 rounded-2xl border-2 border-gray-200 focus:border-[#3018ef] focus:ring-4 focus:ring-[#3018ef]/10 outline-none transition-all resize-none"
            rows={3}
          />
          <div className="mt-2 text-sm text-gray-500">
            {t('calculadora.form.current_limitations_help')}
          </div>
        </div>
      </div>
    </div>
  )
}
