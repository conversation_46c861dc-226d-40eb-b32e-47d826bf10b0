import React, { useState } from "react";
import { motion } from "framer-motion";
import { ArrowLeft, Zap, <PERSON>rkles, Target, Palette, MessageSquare, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ExpressPostGeneratorProps {
  onBack: () => void;
  onGenerate: (data: ExpressGenerationData) => void;
}

interface ExpressGenerationData {
  topic: string;
  businessName: string;
  platform: string;
  theme: string;
  brandColor: string;
  voice: string;
  cta: string;
}

const ExpressPostGenerator: React.FC<ExpressPostGeneratorProps> = ({
  onBack,
  onGenerate,
}) => {
  const [formData, setFormData] = useState<ExpressGenerationData>({
    topic: "",
    businessName: "",
    platform: "Instagram",
    theme: "Balance",
    brandColor: "#3018ef",
    voice: "Profesional y amigable",
    cta: "¡Descubre más!"
  });

  const platforms = [
    { id: "Instagram", name: "Instagram", icon: "📸" },
    { id: "Facebook", name: "Facebook", icon: "👥" },
    { id: "LinkedIn", name: "LinkedIn", icon: "💼" },
    { id: "Twitter", name: "Twitter", icon: "🐦" },
  ];

  const themes = [
    { id: "Balance", name: "Balance", description: "Equilibrio perfecto", color: "#3018ef" },
    { id: "Profesional", name: "Profesional", description: "Serio y confiable", color: "#1e40af" },
    { id: "Creativo", name: "Creativo", description: "Innovador y único", color: "#dd3a5a" },
    { id: "Minimalista", name: "Minimalista", description: "Simple y elegante", color: "#6b7280" },
  ];

  const voiceOptions = [
    "Profesional y amigable",
    "Casual y cercano",
    "Experto y técnico",
    "Inspirador y motivacional",
    "Divertido y creativo"
  ];

  const ctaOptions = [
    "¡Descubre más!",
    "¡Contáctanos ahora!",
    "¡Únete a nosotros!",
    "¡Aprende más!",
    "¡Comienza hoy!"
  ];

  const handleInputChange = (field: keyof ExpressGenerationData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleGenerate = () => {
    if (!formData.topic.trim()) {
      return;
    }
    onGenerate(formData);
  };

  const canGenerate = formData.topic.trim().length > 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <Button
            onClick={onBack}
            variant="ghost"
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Volver
          </Button>
          
          <div className="text-center">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent flex items-center justify-center">
              <Zap className="w-8 h-8 mr-3 text-[#3018ef]" />
              Generación Express
            </h1>
            <p className="text-gray-600 mt-2">Crea posts increíbles en segundos</p>
          </div>
          
          <div className="w-20"></div> {/* Spacer for centering */}
        </motion.div>

        {/* Main Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8"
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Essential Info */}
            <div className="space-y-6">
              <div className="text-center lg:text-left">
                <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                  <Sparkles className="w-6 h-6 mr-2 text-[#dd3a5a]" />
                  Información Esencial
                </h2>
                <p className="text-gray-600">Solo lo necesario para crear contenido increíble</p>
              </div>

              {/* Topic Input */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  <Target className="w-4 h-4 inline mr-2" />
                  ¿De qué tema quieres crear posts? *
                </label>
                <textarea
                  placeholder="Ej: Mi marca es de suplementos para perros, se llama Wouf y vendo muchos suplementos..."
                  value={formData.topic}
                  onChange={(e) => handleInputChange("topic", e.target.value)}
                  rows={3}
                  className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent resize-none text-lg"
                />
                <p className="text-xs text-gray-500 mt-2">
                  Puedes escribir una descripción natural de tu marca o simplemente el tema (ej: "fitness", "recetas veganas")
                </p>
              </div>

              {/* Business Name */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Nombre de tu marca (opcional)
                </label>
                <Input
                  type="text"
                  placeholder="Se detectará automáticamente si lo mencionas arriba"
                  value={formData.businessName}
                  onChange={(e) => handleInputChange("businessName", e.target.value)}
                  className="h-12 text-lg"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Si mencionas el nombre en la descripción de arriba, se detectará automáticamente
                </p>
              </div>

              {/* Platform Selection */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Plataforma
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {platforms.map((platform) => (
                    <button
                      key={platform.id}
                      onClick={() => handleInputChange("platform", platform.id)}
                      className={`p-3 rounded-lg border-2 transition-all ${
                        formData.platform === platform.id
                          ? "border-[#3018ef] bg-[#3018ef]/10 text-[#3018ef]"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="text-2xl mb-1">{platform.icon}</div>
                      <div className="text-sm font-medium">{platform.name}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column - Style Options */}
            <div className="space-y-6">
              <div className="text-center lg:text-left">
                <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                  <Palette className="w-6 h-6 mr-2 text-[#3018ef]" />
                  Estilo y Personalidad
                </h2>
                <p className="text-gray-600">Personaliza el tono de tu contenido</p>
              </div>

              {/* Theme Selection */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Estilo Visual
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {themes.map((theme) => (
                    <button
                      key={theme.id}
                      onClick={() => {
                        handleInputChange("theme", theme.id);
                        handleInputChange("brandColor", theme.color);
                      }}
                      className={`p-3 rounded-lg border-2 transition-all text-left ${
                        formData.theme === theme.id
                          ? "border-[#3018ef] bg-[#3018ef]/10"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-center mb-1">
                        <div 
                          className="w-4 h-4 rounded-full mr-2" 
                          style={{ backgroundColor: theme.color }}
                        ></div>
                        <div className="font-medium text-sm">{theme.name}</div>
                      </div>
                      <div className="text-xs text-gray-600">{theme.description}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Voice Selection */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  <MessageSquare className="w-4 h-4 inline mr-2" />
                  Tono de Voz
                </label>
                <select
                  value={formData.voice}
                  onChange={(e) => handleInputChange("voice", e.target.value)}
                  className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent text-lg"
                >
                  {voiceOptions.map((voice) => (
                    <option key={voice} value={voice}>
                      {voice}
                    </option>
                  ))}
                </select>
              </div>

              {/* CTA Selection */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  Call to Action
                </label>
                <select
                  value={formData.cta}
                  onChange={(e) => handleInputChange("cta", e.target.value)}
                  className="w-full h-12 px-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent text-lg"
                >
                  {ctaOptions.map((cta) => (
                    <option key={cta} value={cta}>
                      {cta}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Generate Button */}
          <div className="mt-8 text-center">
            <Button
              onClick={handleGenerate}
              disabled={!canGenerate}
              className="h-14 px-12 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#2614d4] hover:to-[#c23350] text-white font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="flex items-center justify-center">
                <Zap className="w-6 h-6 mr-3" />
                Generar Posts Ahora
                <ArrowRight className="w-6 h-6 ml-3" />
              </div>
            </Button>
            <p className="text-sm text-gray-500 mt-3">
              Se generarán 3 posts únicos basados en tu tema
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ExpressPostGenerator;
