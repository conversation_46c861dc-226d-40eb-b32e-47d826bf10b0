/**
 * SEO & GPT Optimizer™ - Saved Research Empty State
 * Component for displaying empty states and loading
 */

import React from 'react';
import { Search } from 'lucide-react';

interface SavedResearchEmptyStateProps {
  type: 'loading' | 'empty' | 'no-results';
  title: string;
  description?: string;
}

const SavedResearchEmptyState: React.FC<SavedResearchEmptyStateProps> = ({
  type,
  title,
  description
}) => {
  const getIcon = () => {
    switch (type) {
      case 'loading':
        return (
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
        );
      default:
        return (
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="w-8 h-8 text-gray-400" />
          </div>
        );
    }
  };

  return (
    <div className="text-center py-12">
      {getIcon()}
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {title}
      </h3>
      {description && (
        <p className="text-gray-600">
          {description}
        </p>
      )}
    </div>
  );
};

export default SavedResearchEmptyState;
