import React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { PostsListProps } from "../types";
import PostCard from "./PostCard";
import ErrorDisplay from "./ErrorDisplay";

const PostsList: React.FC<PostsListProps> = ({
  posts,
  brandData,
  onGenerateMore,
  error,
  onRetry
}) => {
  if (error) {
    return <ErrorDisplay error={error} onRetry={onRetry} />;
  }

  return (
    <div className="px-6 py-8 flex flex-col items-center">
      <div className="flex flex-col space-y-8 items-center">
        {posts.map((post, index) => (
          <motion.div
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <PostCard 
              post={post} 
              index={index} 
              brandData={brandData}
              onEdit={(post) => {
                // TODO: Implement edit functionality
                console.log("Edit post:", post);
              }}
              onDownload={(post) => {
                // TODO: Implement download functionality
                console.log("Download post:", post);
              }}
              onShare={(post) => {
                // TODO: Implement share functionality
                console.log("Share post:", post);
              }}
            />
          </motion.div>
        ))}
      </div>

      {posts.length > 0 && (
        <div className="text-center mt-8">
          <Button
            onClick={onGenerateMore}
            className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-3"
          >
            🚀 Generar 3 posts más
          </Button>
        </div>
      )}
    </div>
  );
};

export default PostsList;
