/**
 * Enhanced utility functions for Smart Form
 * Improved error handling, validation, and country detection
 */

import { Country, SmartFormData, ValidationResult, FieldValidation } from './types';

// Enhanced country detection with fallback strategies
export const getUserCountry = (): Country => {
  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const locale = navigator.language || 'es-ES';

    // Enhanced timezone mapping with more specific patterns
    const countryMappings: Record<string, Country> = {
      // Mexico patterns
      'America/Mexico_City': { code: 'MX', name: 'México', flag: '🇲🇽' },
      'America/Cancun': { code: 'MX', name: 'México', flag: '🇲🇽' },
      'America/Merida': { code: 'MX', name: 'México', flag: '🇲🇽' },

      // Argentina patterns
      'America/Argentina/Buenos_Aires': { code: 'AR', name: 'Argentina', flag: '🇦🇷' },
      'America/Argentina/Cordoba': { code: 'AR', name: 'Argentina', flag: '🇦🇷' },

      // Colombia patterns
      'America/Bogota': { code: 'CO', name: 'Colombia', flag: '🇨🇴' },

      // Chile patterns
      'America/Santiago': { code: 'CL', name: 'Chile', flag: '🇨🇱' },

      // Peru patterns
      'America/Lima': { code: 'PE', name: 'Perú', flag: '🇵🇪' },

      // Venezuela patterns
      'America/Caracas': { code: 'VE', name: 'Venezuela', flag: '🇻🇪' },

      // Ecuador patterns
      'America/Guayaquil': { code: 'EC', name: 'Ecuador', flag: '🇪🇨' },

      // Uruguay patterns
      'America/Montevideo': { code: 'UY', name: 'Uruguay', flag: '🇺🇾' },

      // Paraguay patterns
      'America/Asuncion': { code: 'PY', name: 'Paraguay', flag: '🇵🇾' },

      // Bolivia patterns
      'America/La_Paz': { code: 'BO', name: 'Bolivia', flag: '🇧🇴' },

      // Spain patterns
      'Europe/Madrid': { code: 'ES', name: 'España', flag: '🇪🇸' },

      // USA patterns
      'America/New_York': { code: 'US', name: 'Estados Unidos', flag: '🇺🇸' },
      'America/Los_Angeles': { code: 'US', name: 'Estados Unidos', flag: '🇺🇸' },
      'America/Chicago': { code: 'US', name: 'Estados Unidos', flag: '🇺🇸' },
    };

    // Direct timezone match
    if (countryMappings[timezone]) {
      return countryMappings[timezone];
    }

    // Fallback to pattern matching
    const patterns = [
      { pattern: /mexico|mx/i, country: { code: 'MX', name: 'México', flag: '🇲🇽' } },
      { pattern: /argentina|ar/i, country: { code: 'AR', name: 'Argentina', flag: '🇦🇷' } },
      { pattern: /colombia|co/i, country: { code: 'CO', name: 'Colombia', flag: '🇨🇴' } },
      { pattern: /chile|cl/i, country: { code: 'CL', name: 'Chile', flag: '🇨🇱' } },
      { pattern: /peru|pe/i, country: { code: 'PE', name: 'Perú', flag: '🇵🇪' } },
      { pattern: /venezuela|ve/i, country: { code: 'VE', name: 'Venezuela', flag: '🇻🇪' } },
      { pattern: /ecuador|ec/i, country: { code: 'EC', name: 'Ecuador', flag: '🇪🇨' } },
      { pattern: /uruguay|uy/i, country: { code: 'UY', name: 'Uruguay', flag: '🇺🇾' } },
      { pattern: /paraguay|py/i, country: { code: 'PY', name: 'Paraguay', flag: '🇵🇾' } },
      { pattern: /bolivia|bo/i, country: { code: 'BO', name: 'Bolivia', flag: '🇧🇴' } },
      { pattern: /spain|es/i, country: { code: 'ES', name: 'España', flag: '🇪🇸' } },
      { pattern: /united.states|usa|us/i, country: { code: 'US', name: 'Estados Unidos', flag: '🇺🇸' } },
    ];

    for (const { pattern, country } of patterns) {
      if (pattern.test(timezone) || pattern.test(locale)) {
        return country;
      }
    }

    // Default fallback
    return { code: 'ES', name: 'España', flag: '🇪🇸' };

  } catch (error) {
    console.warn('Error detecting user country:', error);
    return { code: 'ES', name: 'España', flag: '🇪🇸' };
  }
};

// Enhanced step validation with detailed error reporting
export const isStepComplete = (stepIndex: number, formData: SmartFormData): boolean => {
  try {
    switch (stepIndex) {
      case 0: // Product & Name
        return !!(formData.product_type?.trim() && formData.product_name?.trim());
      case 1: // Industry
        return !!(formData.industry?.trim());
      case 2: // Target Audience
        return !!(formData.target_audience?.trim() && formData.target_audience.length >= 10);
      case 3: // Main Problem
        return !!(formData.main_problem?.trim() && formData.main_problem.length >= 10);
      case 4: // Price Range
        return !!(formData.price_range?.trim());
      case 5: // Geography
        return !!(
          formData.geographic_scope?.trim() &&
          formData.target_country?.trim() &&
          formData.sales_channels?.length > 0
        );
      case 6: // Audience Knowledge
        return !!(formData.audience_knowledge?.trim());
      case 7: // Business Sizes
        return !!(formData.business_sizes?.length > 0);
      case 8: // Problem Urgency
        return !!(formData.problem_urgency?.trim());
      case 9: // Decision Maker
        return !!(formData.decision_maker?.trim());
      default:
        return false;
    }
  } catch (error) {
    console.warn(`Error validating step ${stepIndex}:`, error);
    return false;
  }
};

// Enhanced validation with detailed feedback
export const validateStep = (stepIndex: number, formData: SmartFormData): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    switch (stepIndex) {
      case 0:
        if (!formData.product_type?.trim()) errors.push('Selecciona un tipo de producto');
        if (!formData.product_name?.trim()) errors.push('Ingresa el nombre del producto');
        if (formData.product_name && formData.product_name.length < 3) {
          warnings.push('El nombre del producto es muy corto');
        }
        break;

      case 2:
        if (!formData.target_audience?.trim()) errors.push('Describe tu audiencia objetivo');
        if (formData.target_audience && formData.target_audience.length < 10) {
          errors.push('La descripción de audiencia debe tener al menos 10 caracteres');
        }
        if (formData.target_audience && formData.target_audience.length < 50) {
          warnings.push('Una descripción más detallada mejorará los resultados');
        }
        break;

      case 3:
        if (!formData.main_problem?.trim()) errors.push('Describe el problema que resuelves');
        if (formData.main_problem && formData.main_problem.length < 10) {
          errors.push('La descripción del problema debe tener al menos 10 caracteres');
        }
        break;

      case 5:
        if (!formData.geographic_scope?.trim()) errors.push('Selecciona tu alcance geográfico');
        if (!formData.target_country?.trim()) errors.push('Selecciona tu país objetivo');
        if (!formData.sales_channels?.length) errors.push('Selecciona al menos un canal de venta');
        break;

      case 7:
        if (!formData.business_sizes?.length) errors.push('Selecciona al menos un tipo de cliente');
        break;
    }
  } catch (error) {
    console.warn(`Error validating step ${stepIndex}:`, error);
    errors.push('Error de validación interno');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Enhanced form description generation with better formatting
export const generateFormDescription = (formData: SmartFormData): string => {
  try {
    // Import constants would be better, but for now we'll use the IDs directly
    const selectedChannels = formData.sales_channels?.join(', ') || 'No especificados';
    const selectedBusinessSizes = formData.business_sizes?.join(', ') || 'No especificados';
    const targetCountryName = formData.target_country || 'No especificado';

    const description = `
INFORMACIÓN DETALLADA DEL PRODUCTO/SERVICIO:
• Producto: ${formData.product_name || 'No especificado'}
• Tipo: ${formData.product_type || 'No especificado'}
• Industria: ${formData.industry || 'No especificada'}
• Rango de precio: ${formData.price_range || 'No especificado'}
• Propuesta de valor única: ${formData.unique_value || 'No especificada'}

AUDIENCIA Y MERCADO:
• Audiencia objetivo: ${formData.target_audience || 'No especificada'}
• Conocimiento del público: ${formData.audience_knowledge || 'No especificado'}
• Público actual: ${formData.existing_audience || 'No especificado'}
• Tipos de cliente objetivo: ${selectedBusinessSizes}

PROBLEMA Y CONTEXTO:
• Problema principal que resuelve: ${formData.main_problem || 'No especificado'}
• Nivel de urgencia: ${formData.problem_urgency || 'No especificado'}
• Decisor de compra: ${formData.decision_maker || 'No especificado'}

GEOGRAFÍA Y DISTRIBUCIÓN:
• Alcance geográfico: ${formData.geographic_scope || 'No especificado'}
• País objetivo principal: ${targetCountryName}
• Regiones específicas: ${formData.target_regions?.join(', ') || 'No especificadas'}
• Canales de venta: ${selectedChannels}

CONTEXTO CULTURAL Y GEOGRÁFICO:
• Mercado principal: ${targetCountryName}
• Enfoque: Adaptar personas al contexto cultural y socioeconómico de ${targetCountryName}
• Considerar características locales, preferencias culturales y comportamientos de compra específicos de la región
    `.trim();

    return description;
  } catch (error) {
    console.error('Error generating form description:', error);
    return 'Error al generar la descripción del formulario';
  }
};

// Utility to calculate form completion percentage
export const getFormCompletionPercentage = (formData: SmartFormData): number => {
  const totalSteps = 10;
  let completedSteps = 0;

  for (let i = 0; i < totalSteps; i++) {
    if (isStepComplete(i, formData)) {
      completedSteps++;
    }
  }

  return Math.round((completedSteps / totalSteps) * 100);
};

// Utility to get next incomplete step
export const getNextIncompleteStep = (formData: SmartFormData): number | null => {
  const totalSteps = 10;

  for (let i = 0; i < totalSteps; i++) {
    if (!isStepComplete(i, formData)) {
      return i;
    }
  }

  return null; // All steps complete
};

// Utility to sanitize form data
export const sanitizeFormData = (formData: Partial<SmartFormData>): SmartFormData => {
  return {
    product_type: formData.product_type?.trim() || '',
    product_name: formData.product_name?.trim() || '',
    industry: formData.industry?.trim() || '',
    target_audience: formData.target_audience?.trim() || '',
    main_problem: formData.main_problem?.trim() || '',
    price_range: formData.price_range?.trim() || '',
    unique_value: formData.unique_value?.trim() || '',
    geographic_scope: formData.geographic_scope?.trim() || '',
    target_country: formData.target_country?.trim() || '',
    target_regions: formData.target_regions || [],
    sales_channels: formData.sales_channels || [],
    audience_knowledge: formData.audience_knowledge?.trim() || '',
    existing_audience: formData.existing_audience?.trim() || '',
    business_sizes: formData.business_sizes || [],
    problem_urgency: formData.problem_urgency?.trim() || '',
    decision_maker: formData.decision_maker?.trim() || '',
  };
};
