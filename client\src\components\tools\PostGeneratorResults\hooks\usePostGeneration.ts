import { useState, useCallback } from 'react';
import { GeneratedPost, PostGenerationData } from '../types';
import { getplatformFromContentType } from '../utils/platformUtils';

export const usePostGeneration = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [posts, setPosts] = useState<GeneratedPost[]>([]);
  const [error, setError] = useState<string | null>(null);

  const generatePosts = useCallback(async (
    brandData: any,
    selectedTheme: string,
    selectedContentType: string,
    isGeneratingMore = false,
    setCompletionProgress?: () => void
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const platform = getplatformFromContentType(selectedContentType);
      
      const postGenerationData: PostGenerationData = {
        brandInfo: {
          businessName: brandData.businessName,
          brandUrl: brandData.brandUrl,
          brandDescription: brandData.brandDescription,
          brandColor: brandData.brandColor,
          voice: brandData.voice,
          topics: brandData.topics,
          ctas: brandData.ctas,
          brandAnalysis: brandData.brandAnalysis
        },
        designConfig: {
          selectedTheme: selectedTheme,
          contentType: selectedContentType,
          platform: platform
        },
        generationConfig: {
          count: 3, // Solo 3 posts por defecto
          useGPT4: true,
          useStabilityAI: true,
          includeImages: true
        }
      };

      const response = await fetch('/api/v1/posts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postGenerationData),
      });

      if (response.ok) {
        const result = await response.json();
        console.log("🔍 PostGeneratorResults - Raw API response:", result);

        if (result.success) {
          console.log("✅ PostGeneratorResults - Posts received:", result.posts);
          console.log("🖼️ PostGeneratorResults - Checking image URLs:");
          result.posts.forEach((post: any, index: number) => {
            console.log(`  Post ${index + 1}: ${post.image_url || 'No image URL'}`);
          });

          // Final progress update
          if (setCompletionProgress) {
            setCompletionProgress();
          }

          // Small delay to show completion before showing results
          setTimeout(() => {
            if (isGeneratingMore) {
              // Add new posts to existing ones
              setPosts(prevPosts => [...prevPosts, ...result.posts]);
              console.log("➕ PostGeneratorResults - Added new posts to existing list");
            } else {
              // Replace posts (initial load)
              setPosts(result.posts);
              console.log("🔄 PostGeneratorResults - Set initial posts");
            }
            setIsLoading(false); // Only stop loading when posts are ready to display
          }, 1000);
        } else {
          console.error("❌ PostGeneratorResults - API error:", result.error);
          setError(result.error || "Error al generar posts");
          setIsLoading(false); // Stop loading on error
        }
      } else {
        console.error("❌ PostGeneratorResults - HTTP error:", response.status);
        setError("Error al conectar con el servidor");
        setIsLoading(false); // Stop loading on HTTP error
      }
    } catch (err) {
      setError("Error inesperado al generar posts");
      console.error("Error generating posts:", err);
      setIsLoading(false); // Stop loading on exception
    }
  }, []);

  return {
    isLoading,
    posts,
    error,
    generatePosts,
    setIsLoading
  };
};
