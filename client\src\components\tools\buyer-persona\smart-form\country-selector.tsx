/**
 * Enhanced Country Selector for Smart Form
 * Features: Search, regional grouping, better UX
 */

import { useState, useMemo } from "react";
import { motion } from "framer-motion";
import { Globe, Search, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { COUNTRIES, getCountriesByRegion } from "./constants";
import { Country } from "./types";

interface CountrySelectorProps {
  selectedCountry: string;
  onChange: (countryCode: string) => void;
  userCountry: Country;
  label?: string;
  placeholder?: string;
}

export function CountrySelector({ 
  selectedCountry, 
  onChange, 
  userCountry,
  label = "Selecciona tu país",
  placeholder = "Buscar países..."
}: CountrySelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Memoized country data
  const countriesByRegion = useMemo(() => getCountriesByRegion(), []);
  
  // Filter countries based on search query
  const filteredCountries = useMemo(() => {
    if (!searchQuery.trim()) return COUNTRIES;
    
    const query = searchQuery.toLowerCase();
    return COUNTRIES.filter(country => 
      country.name.toLowerCase().includes(query) ||
      country.code.toLowerCase().includes(query) ||
      country.region.toLowerCase().includes(query)
    );
  }, [searchQuery]);

  const selectedCountryObject = COUNTRIES.find(country => country.code === selectedCountry);

  return (
    <div className="space-y-3">
      <div className="text-base font-medium text-gray-700 flex items-center gap-2">
        <Globe className="h-4 w-4" />
        {label} {userCountry.flag} (Detectado: {userCountry.name})
      </div>
      
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-12 text-left font-normal border-2 hover:border-blue-400 transition-colors"
          >
            <div className="flex items-center gap-3">
              {selectedCountryObject ? (
                <>
                  <span className="text-lg">{selectedCountryObject.flag}</span>
                  <div>
                    <div className="font-medium">{selectedCountryObject.name}</div>
                    <div className="text-xs text-gray-500">{selectedCountryObject.region}</div>
                  </div>
                </>
              ) : (
                <div className="flex items-center gap-2 text-gray-500">
                  <Globe className="h-4 w-4" />
                  <span>Seleccionar país...</span>
                </div>
              )}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <CommandInput 
                placeholder={placeholder}
                value={searchQuery}
                onValueChange={setSearchQuery}
                className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
            <CommandList className="max-h-[300px] overflow-y-auto">
              <CommandEmpty>No se encontraron países.</CommandEmpty>
              
              {/* Group countries by region */}
              {Object.entries(countriesByRegion).map(([region, countries]) => {
                const filteredRegionCountries = countries.filter(country => 
                  filteredCountries.includes(country)
                );
                
                if (filteredRegionCountries.length === 0) return null;
                
                return (
                  <CommandGroup key={region} heading={region}>
                    {filteredRegionCountries.map((country) => {
                      const isSelected = selectedCountry === country.code;
                      
                      return (
                        <CommandItem
                          key={country.code}
                          value={`${country.name} ${country.code} ${country.region}`}
                          onSelect={() => {
                            onChange(country.code);
                            setOpen(false);
                          }}
                          className={`flex items-center gap-3 px-3 py-2 cursor-pointer ${
                            isSelected ? 'bg-blue-50 text-blue-700' : ''
                          }`}
                        >
                          <span className="text-lg">{country.flag}</span>
                          <div className="flex-1">
                            <div className="font-medium">{country.name}</div>
                            <div className="text-xs text-gray-500">{country.region}</div>
                          </div>
                          {isSelected && (
                            <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                              <div className="w-2 h-2 bg-white rounded-full" />
                            </div>
                          )}
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                );
              })}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Quick Selection Hint */}
      {!selectedCountry && (
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
          💡 <strong>Consejo:</strong> Seleccionar tu país ayudará a Emma a crear buyer personas más precisos y culturalmente relevantes.
        </div>
      )}
    </div>
  );
}
