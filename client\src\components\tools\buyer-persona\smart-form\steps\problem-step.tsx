"use client";

import { Label } from "@/components/ui/label";
import { SmartFormData } from '../types';

interface ProblemStepProps {
  formData: SmartFormData;
  updateFormData: (field: keyof SmartFormData, value: string) => void;
}

export function ProblemStep({ formData, updateFormData }: ProblemStepProps) {
  return (
    <div className="space-y-4">
      <Label htmlFor="main_problem" className="text-base font-medium text-gray-700">
        ¿Qué problema principal resuelves?
      </Label>
      <textarea
        id="main_problem"
        placeholder="Ej: Los emprendedores pierden mucho tiempo en tareas repetitivas y no pueden enfocarse en hacer crecer su negocio..."
        value={formData.main_problem}
        onChange={(e) => updateFormData("main_problem", e.target.value)}
        rows={4}
        className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:outline-none text-lg resize-none"
      />
      <div className="text-sm text-gray-500">
        💡 Describe el dolor, frustración o necesidad que tu producto soluciona
      </div>
    </div>
  );
}
