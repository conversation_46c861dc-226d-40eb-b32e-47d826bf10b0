import { useEffect } from "react"
import { useLocation } from "wouter"

export default function MoodBoard() {
  const [, setLocation] = useLocation()

  // Redirigir automáticamente a la página de lista
  useEffect(() => {
    setLocation("/dashboard/herramientas/mood-board")
  }, [])

  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirigiendo a Mood Boards...</p>
      </div>
    </div>
  )
}
