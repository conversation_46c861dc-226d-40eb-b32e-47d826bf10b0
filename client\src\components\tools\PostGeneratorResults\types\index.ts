export interface GeneratedPost {
  id: string;
  text: string;
  image_url?: string;
  template: string;
  platform: string;
  metadata: any;
}

export interface PostGeneratorResultsProps {
  brandData: any;
  selectedTheme: string;
  selectedContentType: string;
  onBack: () => void;
}

export interface BrandData {
  businessName: string;
  brandUrl: string;
  brandDescription: string;
  brandColor: string;
  voice: string;
  topics: string[];
  ctas: string[];
  brandAnalysis?: any;
}

export interface PostGenerationData {
  brandInfo: BrandData;
  designConfig: {
    selectedTheme: string;
    contentType: string;
    platform: string;
  };
  generationConfig: {
    count: number;
    useGPT4: boolean;
    useStabilityAI: boolean;
    includeImages: boolean;
  };
}

export interface ImageDimensions {
  width: number;
  height: number;
  aspectRatio: number;
}

export interface LoadingScreenProps {
  brandData: any;
  loadingStage: string;
  loadingProgress: number;
  startTime: number;
}

export interface PostCardProps {
  post: GeneratedPost;
  index: number;
  brandData: any;
  onEdit?: (post: GeneratedPost) => void;
  onDownload?: (post: GeneratedPost) => void;
  onShare?: (post: GeneratedPost) => void;
}

export interface HeaderProps {
  selectedTheme: string;
  selectedContentType: string;
  onBack: () => void;
  onGenerateMore: () => void;
  onPreview: () => void;
}

export interface PostsListProps {
  posts: GeneratedPost[];
  brandData: any;
  onGenerateMore: () => void;
  error: string | null;
  onRetry: () => void;
}

export interface ErrorDisplayProps {
  error: string;
  onRetry: () => void;
}
