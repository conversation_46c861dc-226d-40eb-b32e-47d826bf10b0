import React from "react";

interface IconProps {
  size?: number;
  color?: string;
}

const FaShapes: React.FC<IconProps> = ({
  size = 24,
  color = "currentColor",
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M7 7h10v10H7z" />
      <circle cx="17" cy="17" r="4" />
      <path d="M5 17H1v-2a4 4 0 0 1 4-4h2" />
    </svg>
  );
};

export default FaShapes;
