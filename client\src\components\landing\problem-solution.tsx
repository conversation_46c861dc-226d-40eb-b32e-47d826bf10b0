import { motion } from "framer-motion";
import { X, Check } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

export default function ProblemSolution() {
  const { t } = useLanguage();

  const problems = t('landing.problem_solution.problems');
  const solutions = t('landing.problem_solution.solutions');

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            {t('landing.problem_solution.title')}
          </h2>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {/* Problemas */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="bg-white rounded-2xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 h-full"
          >
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center mr-3 border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">
                <X size={24} className="text-white" />
              </div>
              <h3 className="text-2xl font-black">{t('landing.problem_solution.problems_title')}</h3>
            </div>

            <motion.div className="space-y-4">
              {problems.map((problem, index) => (
                <motion.div
                  key={index}
                  className="bg-red-50 rounded-lg border-2 border-black p-3 flex items-start"
                  initial={{ opacity: 0, x: -10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: 0.1 * index }}
                  whileHover={{ x: 5 }}
                >
                  <div className="w-6 h-6 bg-red-100 border-2 border-black rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <X size={12} className="text-red-500" />
                  </div>
                  <p className="text-sm font-bold">{problem}</p>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Soluciones */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="bg-white rounded-2xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 h-full"
          >
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3 border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">
                <Check size={24} className="text-white" />
              </div>
              <h3 className="text-2xl font-black">{t('landing.problem_solution.solutions_title')}</h3>
            </div>

            <motion.div className="space-y-4">
              {solutions.map((solution, index) => (
                <motion.div
                  key={index}
                  className="bg-green-50 rounded-lg border-2 border-black p-3 flex items-start"
                  initial={{ opacity: 0, x: 10 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: 0.1 * index }}
                  whileHover={{ x: -5 }}
                >
                  <div className="w-6 h-6 bg-green-100 border-2 border-black rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <Check size={12} className="text-green-500" />
                  </div>
                  <p className="text-sm font-bold">{solution}</p>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>

        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <motion.button
            className="bg-purple-500 text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-purple-600 transition-all duration-300"
            whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
            whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
          >
            Resolver Mis Problemas de Marketing →
          </motion.button>
        </motion.div>
      </div>

      {/* Elementos decorativos */}
      <motion.div
        className="absolute -left-20 top-40 w-40 h-40 bg-yellow-200 opacity-20 rounded-full"
        animate={{
          scale: [1, 1.2, 1],
          x: [0, 20, 0],
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      />

      <motion.div
        className="absolute -right-20 bottom-40 w-60 h-60 bg-blue-200 opacity-20 rounded-full"
        animate={{
          scale: [1, 1.1, 1],
          x: [0, -10, 0],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      />
    </section>
  );
}
