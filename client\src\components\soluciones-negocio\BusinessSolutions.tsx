import React from "react";
import { motion } from "framer-motion";
import { ArrowRight, Zap, Megaphone, BarChart3 } from "lucide-react";
import { <PERSON> } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

const BusinessSolutions: React.FC = () => {
  const { t } = useLanguage();

  const businessSolutions = t('soluciones_negocio.business_solutions.solutions') as Array<{
    title: string;
    description: string;
    features: string[];
  }>;

  const getIcon = (index: number) => {
    const icons = [
      <Zap size={24} className="text-white" />,
      <Megaphone size={24} className="text-white" />,
      <BarChart3 size={24} className="text-white" />
    ];
    return icons[index] || icons[0];
  };

  return (
    <section id="soluciones" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-black mb-4 text-gray-900">
              {t('soluciones_negocio.business_solutions.title')} <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]">{t('soluciones_negocio.business_solutions.title_highlight')}</span> {t('soluciones_negocio.business_solutions.title_suffix')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('soluciones_negocio.business_solutions.subtitle')}
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {businessSolutions.map((solution, index) => (
            <motion.div
              key={index}
              className="rounded-3xl overflow-hidden shadow-2xl bg-white/20 backdrop-blur-md border border-white/30 hover:bg-white/30 transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ scale: 1.05 }}
            >
              <div className="p-6 bg-gradient-to-br from-[#3018ef] to-[#dd3a5a]">
                <div className="w-14 h-14 rounded-3xl flex items-center justify-center bg-white/20 backdrop-blur-sm shadow-xl mb-4 text-white">
                  {getIcon(index)}
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">{solution.title}</h3>
                <p className="text-white/90">{solution.description}</p>
              </div>
              <div className="p-6 bg-white/90 backdrop-blur-sm">
                <ul className="space-y-3 mb-6">
                  {solution.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start">
                      <svg
                        className="w-5 h-5 text-green-500 mr-2 mt-1 flex-shrink-0"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        ></path>
                      </svg>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link href={`/solutions/${solution.title.toLowerCase().replace(/\s+/g, '-')}`}>
                  <motion.button
                    className="w-full bg-white/20 backdrop-blur-md hover:bg-white/30 text-[#3018ef] font-bold py-3 px-4 rounded-3xl border border-white/30 shadow-xl flex items-center justify-center gap-2 transition-all duration-300"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {t('soluciones_negocio.business_solutions.learn_more')} <ArrowRight size={18} />
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <Link href="/all-solutions">
            <motion.button
              className="bg-[#dd3a5a] text-white font-bold py-3 px-8 rounded-3xl shadow-2xl hover:bg-[#c73351] transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="flex items-center justify-center gap-2">
                {t('soluciones_negocio.business_solutions.view_all_solutions')} <ArrowRight size={18} />
              </span>
            </motion.button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default BusinessSolutions;
