/**
 * Enhanced Image Generator - Seamless Ideogram AI integration
 * Uses Ideogram 3.0 Quality model with inline image insertion
 */

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Image,
  Wand2,
  X,
  Copy,
  Download,
  Plus,
  Sparkles
} from 'lucide-react';

interface EnhancedImageGeneratorProps {
  onImageInsert: (imageUrl: string, prompt: string) => void;
  onClose: () => void;
  className?: string;
}

interface GeneratedImage {
  id: string;
  url: string;
  prompt: string;
  timestamp: Date;
}

const EnhancedImageGenerator: React.FC<EnhancedImageGeneratorProps> = ({
  onImageInsert,
  onClose,
  className = ''
}) => {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [selectedStyle, setSelectedStyle] = useState('realistic');

  const styleOptions = [
    { id: 'realistic', label: 'Realista', description: 'Fotografías realistas' },
    { id: 'artistic', label: 'Artístico', description: 'Arte conceptual' },
    { id: 'minimal', label: 'Minimalista', description: 'Diseño limpio' },
    { id: 'vibrant', label: 'Vibrante', description: 'Colores intensos' }
  ];

  const generateImage = useCallback(async () => {
    if (!prompt.trim() || isGenerating) return;

    setIsGenerating(true);

    try {
      // Enhance prompt based on style
      let enhancedPrompt = prompt;
      switch (selectedStyle) {
        case 'realistic':
          enhancedPrompt = `Professional photograph of ${prompt}, high quality, detailed, realistic lighting`;
          break;
        case 'artistic':
          enhancedPrompt = `Artistic interpretation of ${prompt}, creative, conceptual art style`;
          break;
        case 'minimal':
          enhancedPrompt = `Minimalist design of ${prompt}, clean, simple, modern aesthetic`;
          break;
        case 'vibrant':
          enhancedPrompt = `Vibrant illustration of ${prompt}, bright colors, energetic, dynamic`;
          break;
      }

      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: enhancedPrompt,
          style: selectedStyle,
          model: 'ideogram-3.0-quality',
          aspect_ratio: '16:9',
          magic_prompt_option: 'AUTO'
        }),
      });

      if (!response.ok) {
        throw new Error('Error al generar imagen');
      }

      const data = await response.json();
      
      const newImage: GeneratedImage = {
        id: Date.now().toString(),
        url: data.imageUrl,
        prompt: prompt,
        timestamp: new Date()
      };

      setGeneratedImages(prev => [newImage, ...prev]);
      
    } catch (error) {
      console.error('Error generating image:', error);
      // Show user-friendly error without alert
    } finally {
      setIsGenerating(false);
    }
  }, [prompt, selectedStyle, isGenerating]);

  const handleImageSelect = useCallback((image: GeneratedImage) => {
    onImageInsert(image.url, image.prompt);
    onClose();
  }, [onImageInsert, onClose]);

  const copyImageUrl = useCallback(async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
    } catch (error) {
      console.error('Error copying URL:', error);
    }
  }, []);

  const downloadImage = useCallback(async (url: string, prompt: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${prompt.slice(0, 30)}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Error downloading image:', error);
    }
  }, []);

  return (
    <motion.div
      className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
      >
        {/* Header with Emma Styling */}
        <div className="p-6 border-b border-white/20 bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-xl shadow-lg">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Generador de Imágenes IA</h2>
                <p className="text-sm text-gray-600 font-medium">Powered by Ideogram 3.0 Quality</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-3 text-gray-500 hover:text-[#dd3a5a] hover:bg-white/30 backdrop-blur-sm rounded-xl border border-white/30 transition-all duration-200"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[70vh] overflow-y-auto">
          {/* Generation Form */}
          <div className="space-y-6 mb-8">
            {/* Prompt Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descripción de la imagen
              </label>
              <textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Ej: Una oficina moderna con plantas, luz natural y ambiente profesional"
                className="w-full px-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none"
                rows={3}
              />
            </div>

            {/* Style Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Estilo de imagen
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {styleOptions.map((style) => (
                  <button
                    key={style.id}
                    onClick={() => setSelectedStyle(style.id)}
                    className={`p-3 rounded-xl border-2 transition-all duration-200 ${
                      selectedStyle === style.id
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 hover:border-gray-300 text-gray-700'
                    }`}
                  >
                    <div className="text-sm font-medium">{style.label}</div>
                    <div className="text-xs text-gray-500 mt-1">{style.description}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Generate Button with Emma Styling */}
            <button
              onClick={generateImage}
              disabled={!prompt.trim() || isGenerating}
              className="w-full px-8 py-4 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white rounded-xl hover:from-[#3018ef]/90 hover:to-[#dd3a5a]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-3 font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              {isGenerating ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Generando imagen...
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5" />
                  Generar Imagen con IA
                </>
              )}
            </button>
          </div>

          {/* Generated Images */}
          <AnimatePresence>
            {generatedImages.length > 0 && (
              <motion.div
                className="space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <h3 className="text-lg font-semibold text-gray-900">Imágenes generadas</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {generatedImages.map((image) => (
                    <motion.div
                      key={image.id}
                      className="relative group bg-gray-100 rounded-xl overflow-hidden"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <img
                        src={image.url}
                        alt={image.prompt}
                        className="w-full h-48 object-cover"
                      />
                      
                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                          <button
                            onClick={() => handleImageSelect(image)}
                            className="p-2 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-colors"
                            title="Insertar en blog"
                          >
                            <Plus className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => copyImageUrl(image.url)}
                            className="p-2 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-colors"
                            title="Copiar URL"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => downloadImage(image.url, image.prompt)}
                            className="p-2 bg-white text-gray-900 rounded-lg hover:bg-gray-100 transition-colors"
                            title="Descargar"
                          >
                            <Download className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                      
                      {/* Prompt */}
                      <div className="p-3 bg-white">
                        <p className="text-sm text-gray-600 truncate" title={image.prompt}>
                          {image.prompt}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default EnhancedImageGenerator;
