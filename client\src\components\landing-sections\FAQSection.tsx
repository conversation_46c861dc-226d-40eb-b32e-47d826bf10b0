"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Plus, Minus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/contexts/LanguageContext"



export function FAQSection() {
  const { t } = useLanguage()
  const [openIndex, setOpenIndex] = useState<number | null>(0)
  const [isVisible, setIsVisible] = useState(false)

  const faqs = t('landing.emma_faq.questions') as unknown as Array<{question: string, answer: string}>

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const section = document.getElementById('faq-section')
    if (section) observer.observe(section)

    return () => observer.disconnect()
  }, [])

  const toggleQuestion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section
      id="faq-section"
      className="py-20 sm:py-24 bg-white relative overflow-hidden"
    >
      {/* Clean Background */}
      <div className="absolute inset-0 bg-white" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div
          className={`text-center mb-12 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="inline-block bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm px-8 py-4 rounded-2xl border border-white/20 shadow-lg text-sm font-semibold text-gray-800 mb-8">
            ❓ {t('landing.emma_faq.title')}
          </div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
            {t('landing.emma_faq.subtitle')}
          </h2>
          <p className="text-xl sm:text-2xl font-medium text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('landing.emma_faq.subtitle')}
          </p>
        </div>

        {/* FAQ Items */}
        <div className="max-w-4xl mx-auto">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              className={`mb-6 transition-all duration-500 ${
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
              }`}
              style={{ transitionDelay: `${index * 100}ms` }}
            >
              <motion.button
                className={`w-full p-8 bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 flex justify-between items-center text-left transition-all duration-300 hover:shadow-2xl hover:scale-105 ${
                  openIndex === index ? "rounded-b-none border-b-0" : ""
                }`}
                onClick={() => toggleQuestion(index)}
                whileHover={{ y: openIndex === index ? 0 : -2 }}
              >
                <span className="font-bold text-xl text-gray-900 pr-6">{faq.question}</span>
                <div className={`flex-shrink-0 w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-300 shadow-lg ${
                  openIndex === index
                    ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}>
                  {openIndex === index ? (
                    <Minus size={20} strokeWidth={2} />
                  ) : (
                    <Plus size={20} strokeWidth={2} />
                  )}
                </div>
              </motion.button>

              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="overflow-hidden"
                  >
                    <div className="bg-white/80 backdrop-blur-sm border border-gray-100 border-t-0 rounded-b-3xl shadow-xl p-8">
                      <p className="text-gray-700 leading-relaxed text-lg font-medium">
                        {faq.answer}
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        {/* CTA */}
        <div
          className={`text-center mt-16 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '800ms' }}
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 p-12 max-w-3xl mx-auto">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              {t('landing.emma_faq.cta_title')}
            </h3>
            <p className="text-gray-600 mb-8 text-lg leading-relaxed font-medium">
              {t('landing.emma_faq.cta_subtitle')}
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                variant="blue"
                size="lg"
                onClick={() => window.open('/dashboard', '_blank')}
                className="px-8 py-4 text-lg font-bold"
              >
                {t('landing.emma_faq.contact_support')}
              </Button>
              <Button
                variant="red"
                size="lg"
                onClick={() => window.open('/dashboard', '_blank')}
                className="px-8 py-4 text-lg font-bold"
              >
                {t('landing.emma_faq.start_now')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
