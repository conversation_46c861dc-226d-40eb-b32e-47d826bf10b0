"use client";

import { motion } from "framer-motion";
import { User, Briefcase, Target, Alert<PERSON><PERSON>gle, <PERSON><PERSON><PERSON>U<PERSON>, MessageCircle, Star, Clock } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface JobInfo {
  title: string;
  company_size: string;
  industry: string;
  responsibilities: string[];
}

interface BuyingProcess {
  research_methods: string[];
  decision_factors: string[];
  timeline: string;
}

interface BuyerPersona {
  name: string;
  age: number;
  gender: string;
  location: string;
  education: string;
  income_level: string;
  marital_status: string;
  job: JobInfo;
  personal_background: string;
  goals: string[];
  challenges: string[];
  buying_process: BuyingProcess;
  objections: string[];
  communication_channels: string[];
  influences: string[];
  quotes: string[];
  typical_day: string;
  brand_affinities: string[];
  avatar_description: string;

  // Avatar fields (optional, generated automatically)
  avatar_url?: string;
  avatar_id?: string;
}

interface PersonaCardProps {
  persona: BuyerPersona;
}

export function PersonaCard({ persona }: PersonaCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
    >
      <Card className="max-w-6xl mx-auto bg-white/95 backdrop-blur-xl border-0 shadow-2xl">
        <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-t-lg">
          <CardTitle className="text-center text-3xl font-bold">
            👤 {persona.name}
          </CardTitle>
          <p className="text-center text-blue-100 mt-2">
            Perfil Detallado de Buyer Persona Generado por IA
          </p>
        </CardHeader>
        <CardContent className="p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl">
              <h4 className="text-xl font-bold text-blue-800 mb-4 flex items-center gap-2">
                <User className="h-5 w-5" />
                Información Básica
              </h4>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span><strong>Edad:</strong> {persona.age} años</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span><strong>Ubicación:</strong> {persona.location}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span><strong>Educación:</strong> {persona.education}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span><strong>Ingresos:</strong> {persona.income_level}</span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl">
              <h4 className="text-xl font-bold text-blue-800 mb-4 flex items-center gap-2">
                <Briefcase className="h-5 w-5" />
                Información Profesional
              </h4>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span><strong>Cargo:</strong> {persona.job.title}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span><strong>Industria:</strong> {persona.job.industry}</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span><strong>Tamaño de Empresa:</strong> {persona.job.company_size}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl">
              <h4 className="text-xl font-bold text-green-800 mb-4 flex items-center gap-2">
                <Target className="h-5 w-5" />
                Objetivos y Metas
              </h4>
              <div className="flex flex-wrap gap-3">
                {persona.goals.map((goal, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Badge
                      variant="outline"
                      className="bg-green-100 text-green-800 border-green-300 px-3 py-1 text-sm"
                    >
                      {goal}
                    </Badge>
                  </motion.div>
                ))}
              </div>
            </div>

            <div className="bg-gradient-to-br from-red-50 to-orange-50 p-6 rounded-xl">
              <h4 className="text-xl font-bold text-red-800 mb-4 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Desafíos y Puntos de Dolor
              </h4>
              <div className="flex flex-wrap gap-3">
                {persona.challenges.map((challenge, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Badge
                      variant="secondary"
                      className="bg-red-100 text-red-800 border-red-300 px-3 py-1 text-sm"
                    >
                      {challenge}
                    </Badge>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Buying Process & Decision Making */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl">
              <h4 className="text-xl font-bold text-purple-800 mb-4 flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Proceso de Compra
              </h4>
              <div className="space-y-4">
                <div>
                  <h5 className="font-semibold text-purple-700 mb-2">Métodos de Investigación:</h5>
                  <div className="flex flex-wrap gap-2">
                    {persona.buying_process.research_methods.map((method, index) => (
                      <Badge key={index} variant="outline" className="bg-purple-100 text-purple-800 border-purple-300 text-xs">
                        {method}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h5 className="font-semibold text-purple-700 mb-2">Factores de Decisión:</h5>
                  <div className="flex flex-wrap gap-2">
                    {persona.buying_process.decision_factors.map((factor, index) => (
                      <Badge key={index} variant="outline" className="bg-purple-100 text-purple-800 border-purple-300 text-xs">
                        {factor}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h5 className="font-semibold text-purple-700 mb-1">Timeline de Compra:</h5>
                  <p className="text-sm text-gray-700">{persona.buying_process.timeline}</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-xl">
              <h4 className="text-xl font-bold text-orange-800 mb-4 flex items-center gap-2">
                <MessageCircle className="h-5 w-5" />
                Comunicación y Canales
              </h4>
              <div className="space-y-4">
                <div>
                  <h5 className="font-semibold text-orange-700 mb-2">Canales Preferidos:</h5>
                  <div className="flex flex-wrap gap-2">
                    {persona.communication_channels.map((channel, index) => (
                      <Badge key={index} variant="outline" className="bg-orange-100 text-orange-800 border-orange-300 text-xs">
                        {channel}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h5 className="font-semibold text-orange-700 mb-2">Influencias Clave:</h5>
                  <div className="flex flex-wrap gap-2">
                    {persona.influences.map((influence, index) => (
                      <Badge key={index} variant="outline" className="bg-orange-100 text-orange-800 border-orange-300 text-xs">
                        {influence}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Objections & Brand Affinities */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
            <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-6 rounded-xl">
              <h4 className="text-xl font-bold text-yellow-800 mb-4 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Objeciones Comunes
              </h4>
              <div className="space-y-2">
                {persona.objections.map((objection, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-gray-700">{objection}</p>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-6 rounded-xl">
              <h4 className="text-xl font-bold text-indigo-800 mb-4 flex items-center gap-2">
                <Star className="h-5 w-5" />
                Marcas Preferidas
              </h4>
              <div className="flex flex-wrap gap-2">
                {persona.brand_affinities.map((brand, index) => (
                  <Badge key={index} variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-300 text-xs">
                    {brand}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Typical Day */}
          <div className="mt-8 bg-gradient-to-r from-gray-50 to-gray-100 p-6 rounded-xl">
            <h4 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Un Día Típico
            </h4>
            <p className="text-gray-700 leading-relaxed">{persona.typical_day}</p>
          </div>

          {/* Quote */}
          {persona.quotes && persona.quotes.length > 0 && (
            <div className="mt-8 bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl border-l-4 border-blue-500">
              <blockquote className="text-lg italic text-gray-700">
                "{persona.quotes[0]}"
              </blockquote>
              <cite className="text-sm text-gray-500 mt-2 block">- {persona.name}</cite>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
