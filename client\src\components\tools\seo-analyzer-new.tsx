"use client";

import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2, ExternalLink, Copy } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useQuery } from "@tanstack/react-query";

// Definición de tipos
interface SEOResult {
  status: string;
  error_message?: string;
  url?: string;
  basic_info?: {
    title: string;
    title_length: number;
    meta_description: string | null;
    meta_description_length: number;
    h1_tags: string[];
    h1_count: number;
    canonical_url: string | null;
    language: string | null;
    has_viewport: boolean;
    viewport_content: string | null;
    meta_robots: string | null;
  };
  open_graph?: Record<string, string | null>;
  twitter_card?: Record<string, string | null>;
  technical_audit?: {
    is_https: boolean;
    status_code: number;
    response_time: number;
  };
  content_analysis?: {
    word_count: number;
    headings_structure: Record<string, string[]>;
    images: {
      total: number;
      without_alt: number;
      without_alt_percentage: number;
    };
    links: {
      total: number;
      internal_count: number;
      external_count: number;
      internal_links: string[];
      external_links: string[];
    };
    top_keywords: {
      word: string;
      count: number;
    }[];
  };
  seo_checks?: Record<string, boolean>;
  preview_data?: {
    title: string;
    description: string;
    url: string;
    og_title: string;
    og_description: string;
    og_image: string;
    twitter_title: string;
    twitter_description: string;
    twitter_image: string;
  };
}

export default function SEOAnalyzer() {
  const [url, setUrl] = useState("");
  const [isValidUrl, setIsValidUrl] = useState(true);
  const { toast } = useToast();

  // Consulta para el análisis SEO
  const { data, isLoading, isError, error, refetch } = useQuery<SEOResult>({
    queryKey: ["seo-analysis", url],
    queryFn: async () => {
      if (!url.trim()) {
        throw new Error("URL requerida para el análisis");
      }

      const response = await fetch("/api/seo/analyze", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error_message || "Error al procesar el análisis SEO",
        );
      }

      return response.json();
    },
    enabled: false, // No ejecutar automáticamente
    retry: 1,
  });

  // Validar URL y ejecutar análisis
  const handleAnalyze = () => {
    // Validación simple de URL
    if (!url.trim()) {
      setIsValidUrl(false);
      toast({
        title: "URL requerida",
        description: "Por favor, ingresa una URL para analizar",
        variant: "destructive",
      });
      return;
    }

    // Verificar formato de URL
    const urlPattern =
      /^https?:\/\/[a-zA-Z0-9-_.]+\.[a-zA-Z]{2,}(?:\/[^\s]*)?$/;
    if (!urlPattern.test(url)) {
      setIsValidUrl(false);
      toast({
        title: "Formato de URL inválido",
        description:
          "Por favor ingresa una URL válida (ejemplo: https://www.ejemplo.com)",
        variant: "destructive",
      });
      return;
    }

    setIsValidUrl(true);
    refetch();
  };

  // Obtener puntuación SEO general (porcentaje de verificaciones aprobadas)
  const getSEOScore = (): number => {
    if (!data?.seo_checks) return 0;

    const checks = data.seo_checks;
    const totalChecks = Object.keys(checks).length;

    if (totalChecks === 0) return 0;

    // Contar el número de verificaciones aprobadas (true)
    const passedChecks = Object.values(checks).filter(
      (value) => value === true,
    ).length;

    // Calcular el porcentaje
    return Math.round((passedChecks / totalChecks) * 100);
  };

  // Renderizar el estado de las verificaciones SEO
  const renderCheckStatus = (isOk: boolean) => {
    return isOk ? (
      <CheckCircle2 className="h-5 w-5 text-green-500" />
    ) : (
      <AlertCircle className="h-5 w-5 text-red-500" />
    );
  };

  // Descripción legible de las verificaciones SEO
  const getCheckDescription = (key: string, value: boolean): string => {
    const descriptions: Record<string, { ok: string; error: string }> = {
      title_present: {
        ok: "El título de la página está presente.",
        error: "La página no tiene un título definido.",
      },
      title_length_ok: {
        ok: "El título tiene una longitud adecuada (≤70 caracteres).",
        error: "El título es demasiado largo (>70 caracteres) o está vacío.",
      },
      description_present: {
        ok: "La meta descripción está presente.",
        error: "No se encontró meta descripción en la página.",
      },
      description_length_ok: {
        ok: "La meta descripción tiene una longitud adecuada (≤160 caracteres).",
        error:
          "La meta descripción es demasiado larga (>160 caracteres) o está vacía.",
      },
      h1_present_once: {
        ok: "La página tiene exactamente una etiqueta H1.",
        error: "La página no tiene una etiqueta H1 o tiene múltiples.",
      },
      images_alt_ok: {
        ok: "La mayoría de las imágenes tienen atributos alt (texto alternativo).",
        error: "Muchas imágenes no tienen atributos alt (texto alternativo).",
      },
      og_tags_present: {
        ok: "Las etiquetas Open Graph están correctamente implementadas.",
        error: "Faltan etiquetas Open Graph o están incompletas.",
      },
      twitter_tags_present: {
        ok: "Las etiquetas Twitter Card están correctamente implementadas.",
        error: "Faltan etiquetas Twitter Card o están incompletas.",
      },
      https_ok: {
        ok: "La página utiliza HTTPS (conexión segura).",
        error: "La página no utiliza HTTPS (conexión insegura).",
      },
      canonical_present: {
        ok: "La URL canónica está definida.",
        error: "No se encontró una URL canónica definida.",
      },
    };

    // Si hay una descripción disponible para esta verificación, usarla
    if (descriptions[key]) {
      return value ? descriptions[key].ok : descriptions[key].error;
    }

    // Descripción genérica para verificaciones no especificadas
    return value
      ? `La verificación "${key}" ha sido aprobada.`
      : `La verificación "${key}" ha fallado.`;
  };

  // Copiar al portapapeles
  const copyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast({
          title: "Copiado al portapapeles",
          description: "El texto ha sido copiado correctamente",
        });
      })
      .catch(() => {
        toast({
          title: "Error al copiar",
          description: "No se pudo copiar al portapapeles",
          variant: "destructive",
        });
      });
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="flex flex-col md:flex-row items-start gap-6">
        {/* Panel de entrada */}
        <Card className="w-full md:w-2/5">
          <CardHeader>
            <CardTitle>Analizador SEO On-Page</CardTitle>
            <CardDescription>
              Ingresa una URL para analizar el rendimiento SEO de la página
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="url">URL del sitio web</Label>
                <Input
                  id="url"
                  placeholder="https://ejemplo.com"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  className={isValidUrl ? "" : "border-red-500"}
                />
                {!isValidUrl && (
                  <p className="text-sm text-red-500">
                    Por favor, ingresa una URL válida (debe comenzar con http://
                    o https://)
                  </p>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleAnalyze}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? "Analizando..." : "Analizar SEO"}
            </Button>
          </CardFooter>
        </Card>

        {/* Panel de resultados */}
        <Card className="w-full md:w-3/5">
          {isLoading ? (
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center space-y-4 py-12">
                <h3 className="text-lg font-medium">Analizando sitio web...</h3>
                <Progress value={45} className="w-2/3" />
                <p className="text-sm text-muted-foreground">
                  Este proceso puede tomar unos momentos dependiendo del tamaño
                  del sitio
                </p>
              </div>
            </CardContent>
          ) : isError ? (
            <CardContent className="pt-6">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>
                  {error instanceof Error
                    ? error.message
                    : "Ocurrió un error al analizar la URL"}
                </AlertDescription>
              </Alert>
            </CardContent>
          ) : data && data.status === "success" ? (
            <>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Resultado del análisis</CardTitle>
                    <CardDescription>
                      <a
                        href={data.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-blue-600 hover:underline"
                      >
                        {data.url} <ExternalLink className="ml-1 h-3 w-3" />
                      </a>
                    </CardDescription>
                  </div>
                  <div className="flex flex-col items-center">
                    <div className="text-3xl font-bold">{getSEOScore()}</div>
                    <div className="text-xs text-muted-foreground">
                      Puntuación SEO
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="general">
                  <TabsList className="grid grid-cols-4 mb-4">
                    <TabsTrigger value="general">General</TabsTrigger>
                    <TabsTrigger value="content">Contenido</TabsTrigger>
                    <TabsTrigger value="meta">Meta Tags</TabsTrigger>
                    <TabsTrigger value="preview">
                      Previsualizaciones
                    </TabsTrigger>
                  </TabsList>

                  {/* Pestaña General */}
                  <TabsContent value="general" className="space-y-4">
                    <div className="space-y-4">
                      {data.seo_checks &&
                        Object.entries(data.seo_checks).map(([key, value]) => (
                          <div
                            key={key}
                            className="flex items-start space-x-2 p-2 rounded-md border"
                          >
                            <div className="mt-0.5">
                              {renderCheckStatus(Boolean(value))}
                            </div>
                            <div>
                              <h4 className="text-sm font-medium">
                                {key
                                  .split("_")
                                  .map(
                                    (word) =>
                                      word.charAt(0).toUpperCase() +
                                      word.slice(1),
                                  )
                                  .join(" ")}
                              </h4>
                              <p className="text-sm text-muted-foreground">
                                {getCheckDescription(key, Boolean(value))}
                              </p>
                            </div>
                          </div>
                        ))}
                    </div>
                  </TabsContent>

                  {/* Pestaña Contenido */}
                  <TabsContent value="content" className="space-y-4">
                    {data.content_analysis && (
                      <>
                        <div className="grid grid-cols-2 gap-4">
                          <Card>
                            <CardHeader className="p-4 pb-2">
                              <CardTitle className="text-base">
                                Estadísticas de contenido
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 pt-0">
                              <ul className="space-y-2">
                                <li className="flex justify-between">
                                  <span className="text-muted-foreground">
                                    Recuento de palabras:
                                  </span>
                                  <span className="font-medium">
                                    {data.content_analysis.word_count}
                                  </span>
                                </li>
                                <li className="flex justify-between">
                                  <span className="text-muted-foreground">
                                    Imágenes totales:
                                  </span>
                                  <span className="font-medium">
                                    {data.content_analysis.images.total}
                                  </span>
                                </li>
                                <li className="flex justify-between">
                                  <span className="text-muted-foreground">
                                    Imágenes sin alt:
                                  </span>
                                  <span className="font-medium">
                                    {data.content_analysis.images.without_alt}
                                  </span>
                                </li>
                              </ul>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardHeader className="p-4 pb-2">
                              <CardTitle className="text-base">
                                Enlaces
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 pt-0">
                              <ul className="space-y-2">
                                <li className="flex justify-between">
                                  <span className="text-muted-foreground">
                                    Enlaces totales:
                                  </span>
                                  <span className="font-medium">
                                    {data.content_analysis.links.total}
                                  </span>
                                </li>
                                <li className="flex justify-between">
                                  <span className="text-muted-foreground">
                                    Enlaces internos:
                                  </span>
                                  <span className="font-medium">
                                    {data.content_analysis.links.internal_count}
                                  </span>
                                </li>
                                <li className="flex justify-between">
                                  <span className="text-muted-foreground">
                                    Enlaces externos:
                                  </span>
                                  <span className="font-medium">
                                    {data.content_analysis.links.external_count}
                                  </span>
                                </li>
                              </ul>
                            </CardContent>
                          </Card>
                        </div>

                        {/* Sección de encabezados */}
                        <Card>
                          <CardHeader className="p-4 pb-2">
                            <CardTitle className="text-base">
                              Estructura de encabezados
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="p-4 pt-0">
                            {data.basic_info?.h1_tags &&
                            data.basic_info.h1_tags.length > 0 ? (
                              <div className="space-y-2">
                                <h4 className="text-sm font-medium">
                                  Etiquetas H1 ({data.basic_info.h1_tags.length}
                                  )
                                </h4>
                                <ScrollArea className="h-24 w-full rounded-md border p-2">
                                  <ul className="space-y-1">
                                    {data.basic_info.h1_tags.map((tag, i) => (
                                      <li key={i} className="text-sm">
                                        {tag}
                                      </li>
                                    ))}
                                  </ul>
                                </ScrollArea>
                              </div>
                            ) : (
                              <p className="text-sm text-muted-foreground">
                                No se encontraron etiquetas H1
                              </p>
                            )}

                            {data.content_analysis.headings_structure &&
                              Object.entries(
                                data.content_analysis.headings_structure,
                              )
                                .filter(([_, headings]) => headings.length > 0)
                                .map(([level, headings]) => (
                                  <div key={level} className="mt-4 space-y-2">
                                    <h4 className="text-sm font-medium">
                                      Etiquetas {level.toUpperCase()} (
                                      {headings.length})
                                    </h4>
                                    <ScrollArea className="h-24 w-full rounded-md border p-2">
                                      <ul className="space-y-1">
                                        {headings.map((heading, i) => (
                                          <li key={i} className="text-sm">
                                            {heading}
                                          </li>
                                        ))}
                                      </ul>
                                    </ScrollArea>
                                  </div>
                                ))}
                          </CardContent>
                        </Card>

                        {/* Palabras clave */}
                        <Card>
                          <CardHeader className="p-4 pb-2">
                            <div className="flex justify-between items-center">
                              <CardTitle className="text-base">
                                Palabras clave principales
                              </CardTitle>
                            </div>
                          </CardHeader>
                          <CardContent className="p-4 pt-0">
                            {data.content_analysis.top_keywords &&
                            data.content_analysis.top_keywords.length > 0 ? (
                              <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                  {data.content_analysis.top_keywords
                                    .slice(0, 6)
                                    .map((keyword, i) => (
                                      <div
                                        key={i}
                                        className="flex justify-between items-center p-2 rounded-md border"
                                      >
                                        <span>{keyword.word}</span>
                                        <div className="flex items-center space-x-2">
                                          <span className="text-xs font-medium">
                                            {keyword.count}x
                                          </span>
                                        </div>
                                      </div>
                                    ))}
                                </div>

                                {data.content_analysis
                                  .keyword_opportunities && (
                                  <div className="mt-6">
                                    <h4 className="text-sm font-medium mb-3">
                                      Oportunidades de Palabras Clave
                                    </h4>
                                    <div className="space-y-2">
                                      {data.content_analysis.keyword_opportunities.map(
                                        (opportunity, i) => (
                                          <div
                                            key={i}
                                            className="p-2 rounded-md border bg-muted/10"
                                          >
                                            <div className="flex items-center justify-between">
                                              <span className="font-medium">
                                                {opportunity.word}
                                              </span>
                                              <span className="text-xs text-muted-foreground">
                                                {opportunity.type ===
                                                "low_frequency"
                                                  ? "↑"
                                                  : "↓"}
                                              </span>
                                            </div>
                                            <p className="text-sm text-muted-foreground mt-1">
                                              {opportunity.suggestion}
                                            </p>
                                          </div>
                                        ),
                                      )}
                                    </div>
                                  </div>
                                )}
                              </div>
                            ) : (
                              <p className="text-sm text-muted-foreground">
                                No se encontraron palabras clave destacadas
                              </p>
                            )}
                          </CardContent>
                        </Card>
                      </>
                    )}
                  </TabsContent>

                  {/* Pestaña Meta Tags */}
                  <TabsContent value="meta" className="space-y-4">
                    <Card>
                      <CardHeader className="p-4 pb-2">
                        <div className="flex justify-between">
                          <CardTitle className="text-base">
                            Meta Tags Básicos
                          </CardTitle>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() =>
                              copyToClipboard(
                                JSON.stringify(data.basic_info, null, 2),
                              )
                            }
                          >
                            <Copy className="h-4 w-4 mr-1" /> Copiar
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="space-y-3">
                          <div>
                            <h4 className="text-sm font-medium">Título</h4>
                            <p className="text-sm border p-2 rounded-md">
                              {data.basic_info?.title || "No definido"}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              Longitud: {data.basic_info?.title_length || 0}{" "}
                              caracteres (Recomendado: 50-60)
                            </p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium">
                              Meta Descripción
                            </h4>
                            <p className="text-sm border p-2 rounded-md">
                              {data.basic_info?.meta_description ||
                                "No definida"}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              Longitud:{" "}
                              {data.basic_info?.meta_description_length || 0}{" "}
                              caracteres (Recomendado: 140-160)
                            </p>
                          </div>

                          <div>
                            <h4 className="text-sm font-medium">Robots</h4>
                            <p className="text-sm border p-2 rounded-md">
                              {data.basic_info?.meta_robots || "No definido"}
                            </p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium">Canonical</h4>
                            <p className="text-sm border p-2 rounded-md">
                              {data.basic_info?.canonical_url || "No definido"}
                            </p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium">Idioma</h4>
                            <p className="text-sm border p-2 rounded-md">
                              {data.basic_info?.language || "No definido"}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader className="p-4 pb-2">
                          <CardTitle className="text-base">
                            Open Graph
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="p-4 pt-0">
                          {data.open_graph &&
                          Object.entries(data.open_graph).filter(
                            ([_, v]) => v !== null,
                          ).length > 0 ? (
                            <ScrollArea className="h-56 w-full">
                              <div className="space-y-2">
                                {Object.entries(data.open_graph)
                                  .filter(([_, v]) => v !== null)
                                  .map(([key, value]) => (
                                    <div key={key} className="space-y-1">
                                      <h4 className="text-xs font-medium text-muted-foreground">
                                        og:{key}
                                      </h4>
                                      <p className="text-sm border p-1 rounded-md break-all">
                                        {value}
                                      </p>
                                    </div>
                                  ))}
                              </div>
                            </ScrollArea>
                          ) : (
                            <p className="text-sm text-muted-foreground">
                              No se encontraron etiquetas Open Graph
                            </p>
                          )}
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="p-4 pb-2">
                          <CardTitle className="text-base">
                            Twitter Card
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="p-4 pt-0">
                          {data.twitter_card &&
                          Object.entries(data.twitter_card).filter(
                            ([_, v]) => v !== null,
                          ).length > 0 ? (
                            <ScrollArea className="h-56 w-full">
                              <div className="space-y-2">
                                {Object.entries(data.twitter_card)
                                  .filter(([_, v]) => v !== null)
                                  .map(([key, value]) => (
                                    <div key={key} className="space-y-1">
                                      <h4 className="text-xs font-medium text-muted-foreground">
                                        twitter:{key}
                                      </h4>
                                      <p className="text-sm border p-1 rounded-md break-all">
                                        {value}
                                      </p>
                                    </div>
                                  ))}
                              </div>
                            </ScrollArea>
                          ) : (
                            <p className="text-sm text-muted-foreground">
                              No se encontraron etiquetas Twitter Card
                            </p>
                          )}
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  {/* Pestaña Previsualizaciones */}
                  <TabsContent value="preview" className="space-y-4">
                    {data.preview_data ? (
                      <>
                        <Card>
                          <CardHeader className="p-4 pb-2">
                            <CardTitle className="text-base">
                              Previsualización en Google
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="p-4 pt-0">
                            <div className="border p-4 rounded-md space-y-1 max-w-2xl">
                              <h3 className="text-xl text-blue-600 hover:underline cursor-pointer">
                                {data.preview_data.title}
                              </h3>
                              <p className="text-green-700 text-sm">
                                {data.preview_data.url}
                              </p>
                              <p className="text-sm text-gray-600">
                                {data.preview_data.description}
                              </p>
                            </div>
                          </CardContent>
                        </Card>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <Card>
                            <CardHeader className="p-4 pb-2">
                              <CardTitle className="text-base">
                                Open Graph (Facebook)
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 pt-0">
                              <div className="border p-4 rounded-md space-y-1">
                                <h3 className="font-medium">
                                  {data.preview_data.og_title}
                                </h3>
                                <p className="text-sm text-gray-600">
                                  {data.preview_data.og_description}
                                </p>
                                {data.preview_data.og_image && (
                                  <div className="mt-2 p-2 bg-gray-100 text-center rounded">
                                    <p className="text-xs text-gray-500">
                                      Imagen disponible
                                    </p>
                                  </div>
                                )}
                              </div>
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader className="p-4 pb-2">
                              <CardTitle className="text-base">
                                Twitter Card
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 pt-0">
                              <div className="border p-4 rounded-md space-y-1">
                                <h3 className="font-medium">
                                  {data.preview_data.twitter_title}
                                </h3>
                                <p className="text-sm text-gray-600">
                                  {data.preview_data.twitter_description}
                                </p>
                                {data.preview_data.twitter_image && (
                                  <div className="mt-2 p-2 bg-gray-100 text-center rounded">
                                    <p className="text-xs text-gray-500">
                                      Imagen disponible
                                    </p>
                                  </div>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </>
                    ) : (
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>
                          No hay previsualizaciones disponibles
                        </AlertTitle>
                        <AlertDescription>
                          No se pudieron generar previsualizaciones para esta
                          URL. Asegúrate de que la página tenga meta tags
                          correctamente implementadas.
                        </AlertDescription>
                      </Alert>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </>
          ) : null}
        </Card>
      </div>
    </div>
  );
}
