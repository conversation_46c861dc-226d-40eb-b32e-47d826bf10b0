import React from "react";
import { motion } from "framer-motion";
import { Globe, Briefcase, ArrowRight, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface Step1BrandInputProps {
  brandUrl: string;
  brandDescription: string;
  isAnalyzing: boolean;
  analysisError: string;
  onBrandUrlChange: (value: string) => void;
  onBrandDescriptionChange: (value: string) => void;
  onContinue: () => void;
  onExpressMode?: () => void;
}

const Step1BrandInput: React.FC<Step1BrandInputProps> = ({
  brandUrl,
  brandDescription,
  isAnalyzing,
  analysisError,
  onBrandUrlChange,
  onBrandDescriptionChange,
  onContinue,
  onExpressMode,
}) => {
  const canContinue = brandUrl.trim() || brandDescription.trim();
  const hasAnalysisError = analysisError && analysisError.includes("No pude acceder");

  const handleContinueAnyway = () => {
    // Force continue even if website analysis failed
    if (brandDescription.trim()) {
      onContinue();
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8"
      >
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Cuéntanos sobre tu marca
          </h2>
          <p className="text-lg text-gray-600">
            Ingresa la URL de tu sitio web o describe tu marca
          </p>

          {/* Express Mode Option */}
          {onExpressMode && (
            <div className="mt-6 p-4 bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 rounded-xl border border-[#3018ef]/20">
              <div className="flex items-center justify-center mb-3">
                <Zap className="w-5 h-5 text-[#3018ef] mr-2" />
                <span className="text-sm font-semibold text-gray-700">¿Tienes prisa?</span>
              </div>
              <Button
                onClick={onExpressMode}
                variant="outline"
                className="border-2 border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white font-semibold"
              >
                <Zap className="w-4 h-4 mr-2" />
                Modo Express - Generar Rápido
              </Button>
              <p className="text-xs text-gray-500 mt-2">
                Crea posts en segundos con solo un tema
              </p>
            </div>
          )}
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">
              <Globe className="w-4 h-4 inline mr-2" />
              URL de tu sitio web
            </label>
            <Input
              type="url"
              placeholder="https://tu-sitio-web.com"
              value={brandUrl}
              onChange={(e) => onBrandUrlChange(e.target.value)}
              className="h-12 text-lg"
            />
          </div>

          {analysisError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">!</span>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{analysisError}</p>
                </div>
              </div>
            </div>
          )}

          <div className="text-center text-gray-500 font-medium">O</div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">
              <Briefcase className="w-4 h-4 inline mr-2" />
              Describe tu marca
            </label>
            <textarea
              placeholder="Ej: Somos una empresa de marketing digital que ayuda a startups a crecer..."
              value={brandDescription}
              onChange={(e) => onBrandDescriptionChange(e.target.value)}
              rows={4}
              className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent resize-none text-lg"
            />
          </div>

          <div className="space-y-3">
            <Button
              onClick={onContinue}
              disabled={!canContinue || isAnalyzing}
              className="w-full h-12 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#2614d4] hover:to-[#c23350] text-white font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="flex items-center justify-center">
                {isAnalyzing ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Preparando tu experiencia...
                  </>
                ) : (
                  <>
                    Continuar
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </>
                )}
              </div>
            </Button>

            {hasAnalysisError && brandDescription.trim() && (
              <Button
                onClick={handleContinueAnyway}
                variant="outline"
                className="w-full h-10 border-2 border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white font-medium"
              >
                Continuar con descripción manual
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Step1BrandInput;
