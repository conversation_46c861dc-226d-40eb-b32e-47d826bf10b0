"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>les, ToggleLeft, ToggleRight, Zap, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { UseFormReturn } from "react-hook-form";
import { SmartForm } from "./smart-form";

interface PersonaFormProps {
  form: UseFormReturn<any>;
  onSubmit: (values: any) => void;
  isLoading: boolean;
}

export function PersonaForm({ form, onSubmit, isLoading }: PersonaFormProps) {
  const [isSmartMode, setIsSmartMode] = useState(false);

  const handleSmartFormSubmit = (smartData: any) => {
    // Convertir datos del formulario inteligente al formato esperado
    const formattedData = {
      product_description: smartData.product_description || `
Producto: ${smartData.product_name} (${smartData.product_type})
Industria: ${smartData.industry}
Audiencia objetivo: ${smartData.target_audience}
Problema que resuelve: ${smartData.main_problem}
Rango de precio: ${smartData.price_range}
Propuesta de valor única: ${smartData.unique_value || 'No especificada'}
      `.trim()
    };

    onSubmit(formattedData);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="space-y-6"
    >
      {/* Toggle Mode Selector */}
      <div className="w-full max-w-5xl mx-auto mb-6">
        <div className="flex items-center justify-center gap-4 p-4 bg-white/80 backdrop-blur-sm rounded-lg border border-purple-200 shadow-lg">
          <div className="flex items-center gap-3">
            <FileText className={`h-5 w-5 ${!isSmartMode ? 'text-purple-600' : 'text-gray-400'}`} />
            <span className={`font-medium ${!isSmartMode ? 'text-purple-600' : 'text-gray-500'}`}>
              Modo Texto
            </span>
          </div>

          <button
            onClick={() => setIsSmartMode(!isSmartMode)}
            className="relative inline-flex h-8 w-14 items-center rounded-full bg-gradient-to-r from-purple-500 to-blue-500 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
          >
            <motion.div
              className="inline-block h-6 w-6 transform rounded-full bg-white shadow-lg"
              animate={{ x: isSmartMode ? 28 : 4 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
            />
          </button>

          <div className="flex items-center gap-3">
            <Zap className={`h-5 w-5 ${isSmartMode ? 'text-purple-600' : 'text-gray-400'}`} />
            <span className={`font-medium ${isSmartMode ? 'text-purple-600' : 'text-gray-500'}`}>
              Formulario Inteligente
            </span>
            <Badge variant="outline" className="bg-gradient-to-r from-purple-50 to-blue-50 text-purple-700 border-purple-200 text-xs">
              ✨ Nuevo
            </Badge>
          </div>
        </div>
      </div>

      {/* Render Smart Form or Traditional Form */}
      {isSmartMode ? (
        <SmartForm onSubmit={handleSmartFormSubmit} isLoading={isLoading} />
      ) : (
        <Card className="w-full max-w-5xl mx-auto bg-white/95 backdrop-blur-xl border-0 shadow-2xl">
          <CardHeader className="bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-3 text-2xl">
              <div className="p-2 bg-white/20 rounded-lg">
                <Users className="h-6 w-6" />
              </div>
              Información del Producto/Servicio
            </CardTitle>
            <p className="text-purple-100 mt-2">
              Proporciona información detallada sobre tu producto o servicio para generar buyer personas precisas
            </p>
          </CardHeader>
        <CardContent className="p-8">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="product_description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-lg font-semibold text-gray-800">
                      Describe tu producto o servicio en detalle
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Ejemplo: Plataforma de coaching fitness online para profesionales ocupados de 25-45 años. Incluye planes de entrenamiento personalizados, guía nutricional y sesiones 1:1. Precio: $99/mes. Target: profesionales que quieren mantenerse en forma pero tienen tiempo limitado..."
                        className="min-h-[150px] text-base border-2 border-purple-200 focus:border-purple-500 transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-4 text-lg shadow-xl"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        className="mr-3"
                      >
                        <Sparkles className="h-5 w-5" />
                      </motion.div>
                      Generando Personas con IA...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-5 w-5 mr-3" />
                      Generar Buyer Personas con IA
                      <Sparkles className="h-5 w-5 ml-3" />
                    </>
                  )}
                </Button>
              </motion.div>
            </form>
          </Form>
        </CardContent>
      </Card>
      )}
    </motion.div>
  );
}
