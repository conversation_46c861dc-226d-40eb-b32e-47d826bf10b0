/**
 * User History Dashboard for Buyer Persona Generator
 * Shows generation history stored in localStorage (ready for Supabase integration)
 */

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  History, 
  Users, 
  Calendar, 
  Download, 
  Trash2, 
  Eye,
  Clock,
  Star,
  Filter,
  Search
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { PersonaHistoryManager } from "../utils/history-manager";
import { GenerationResult } from "../types";

interface HistoryItem {
  id: string;
  result: GenerationResult;
  timestamp: string;
  personaCount: number;
  productName?: string;
  hasPremiumData: boolean;
}

interface HistoryDashboardProps {
  onLoadHistory: (result: GenerationResult) => void;
  onClose: () => void;
}

export function HistoryDashboard({ onLoadHistory, onClose }: HistoryDashboardProps) {
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<'date' | 'personas' | 'name'>('date');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHistory();
  }, []);

  const loadHistory = () => {
    setLoading(true);
    try {
      const history = PersonaHistoryManager.getAllHistory();
      setHistoryItems(history);
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteItem = (id: string) => {
    PersonaHistoryManager.deleteHistoryItem(id);
    loadHistory();
  };

  const handleClearAll = () => {
    if (window.confirm('¿Estás seguro de que quieres eliminar todo el historial?')) {
      PersonaHistoryManager.clearAllHistory();
      loadHistory();
    }
  };

  const handleLoadItem = (item: HistoryItem) => {
    onLoadHistory(item.result);
    onClose();
  };

  // Filter and sort items
  const filteredItems = historyItems
    .filter(item => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        item.productName?.toLowerCase().includes(query) ||
        item.result.buyer_personas.some(p => p.name.toLowerCase().includes(query)) ||
        item.result.request_id.toLowerCase().includes(query)
      );
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
        case 'personas':
          return b.personaCount - a.personaCount;
        case 'name':
          return (a.productName || '').localeCompare(b.productName || '');
        default:
          return 0;
      }
    });

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Hace menos de 1 hora';
    if (diffInHours < 24) return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`;
    
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `Hace ${diffInWeeks} semana${diffInWeeks > 1 ? 's' : ''}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando historial...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <History className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Historial de Generaciones</h2>
            <p className="text-sm text-gray-600">
              {historyItems.length} generación{historyItems.length !== 1 ? 'es' : ''} guardada{historyItems.length !== 1 ? 's' : ''}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearAll}
            disabled={historyItems.length === 0}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Limpiar Todo
          </Button>
          <Button variant="outline" size="sm" onClick={onClose}>
            Cerrar
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Buscar por producto, persona o ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="date">Más reciente</option>
            <option value="personas">Más personas</option>
            <option value="name">Nombre A-Z</option>
          </select>
        </div>
      </div>

      {/* History Items */}
      {filteredItems.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">📋</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {searchQuery ? 'No se encontraron resultados' : 'No hay historial disponible'}
            </h3>
            <p className="text-gray-600">
              {searchQuery 
                ? 'Intenta con otros términos de búsqueda'
                : 'Genera tu primer buyer persona para ver el historial aquí'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          <AnimatePresence>
            {filteredItems.map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-gray-900">
                            {item.productName || `Generación ${item.id.slice(0, 8)}`}
                          </h3>
                          
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              <Users className="h-3 w-3 mr-1" />
                              {item.personaCount} persona{item.personaCount !== 1 ? 's' : ''}
                            </Badge>
                            
                            {item.hasPremiumData && (
                              <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white text-xs">
                                <Star className="h-3 w-3 mr-1" />
                                Premium
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {formatDate(item.timestamp)}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {getTimeAgo(item.timestamp)}
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-2">
                          {item.result.buyer_personas.slice(0, 3).map((persona, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              {persona.name}
                            </Badge>
                          ))}
                          {item.result.buyer_personas.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{item.result.buyer_personas.length - 3} más
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleLoadItem(item)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Ver
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteItem(item.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}
    </div>
  );
}
