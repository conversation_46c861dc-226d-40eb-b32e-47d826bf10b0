import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
// Importar la imagen de assets directamente
import seoAgentImage from "../../assets/images/seox-agent.png";

// Configuración del agente
const AGENT_CONFIG = {
  name: "SEOX",
  title: "Experto en Optimización SEO",
  description:
    "Asistente de IA especializado en análisis avanzado de SEO y optimización de ranking en buscadores. Creado por el equipo de ingenieros SEO de RankMaster™.",
};

// Mensajes predefinidos del agente
const AGENT_MESSAGES = [
  `¡Hola! Soy ${AGENT_CONFIG.name}, tu ${AGENT_CONFIG.title}. Estoy aquí para potenciar tu visibilidad en Google y otros motores de búsqueda.`,
  "He analizado tu sitio web y encontrado varias oportunidades de mejora. ¡Revisa las recomendaciones prioritarias!",
  "¿Sabías que el 93% de las experiencias en línea comienzan con un motor de búsqueda? Optimizar tu SEO es clave para captar tráfico cualificado.",
  "Recuerda que Google prioriza la experiencia del usuario. Mejora tu velocidad de carga para aumentar tu ranking y conversiones.",
  "Las meta etiquetas bien optimizadas pueden aumentar tu CTR hasta en un 30%. ¡No descuides este aspecto tan importante!",
  "El contenido original y de calidad sigue siendo el rey del SEO. Genera valor real para tus usuarios y Google te recompensará.",
  "Los enlaces internos ayudan a distribuir la autoridad de tu dominio. Úsalos estratégicamente para potenciar páginas clave.",
  "Un buen SEO técnico es la base de una estrategia exitosa. Comienza con los fundamentos para escalar posiciones rápidamente.",
];

interface SEOAgentProps {
  analysisComplete?: boolean;
  seoScore?: number;
  numIssues?: number;
}

const SEOAgent: React.FC<SEOAgentProps> = ({
  analysisComplete = false,
  seoScore = 0,
  numIssues = 0,
}) => {
  const [currentMessage, setCurrentMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [isExpanded, setIsExpanded] = useState(true);

  // Seleccionar un mensaje según el contexto del análisis
  useEffect(() => {
    if (analysisComplete) {
      // Crear mensaje personalizado basado en la puntuación
      let customMessage = "";

      if (seoScore < 30) {
        customMessage = `He analizado tu sitio y tiene una puntuación SEO baja (${seoScore}/100). Las prioridades más urgentes son mejorar las meta etiquetas y estructura de encabezados. ¡Te ayudaré a optimizarlo!`;
      } else if (seoScore < 60) {
        customMessage = `Tu sitio tiene una puntuación de ${seoScore}/100. Hay oportunidades significativas para mejorar, especialmente en la estructura del contenido y las etiquetas Open Graph para redes sociales.`;
      } else if (seoScore < 80) {
        customMessage = `¡Bien! Tu puntuación SEO es de ${seoScore}/100. Para mejorar aún más, considera optimizar la velocidad de carga y añadir más enlaces internos estructurados para distribuir la autoridad.`;
      } else {
        customMessage = `¡Excelente trabajo! Tu sitio tiene una puntuación SEO de ${seoScore}/100. Solo necesitas pequeños ajustes para alcanzar la perfección. Revisa las recomendaciones para los últimos detalles.`;
      }

      // Si hay un número específico de problemas, mencionarlos
      if (numIssues > 0) {
        customMessage += ` He detectado ${numIssues} ${numIssues === 1 ? "problema" : "problemas"} para resolver.`;
      }

      showNewMessage(customMessage);
    } else {
      showNewMessage(AGENT_MESSAGES[0]); // Mensaje de bienvenida
    }
  }, [analysisComplete, seoScore, numIssues]);

  // Efecto de escritura para el mensaje
  const showNewMessage = (message: string) => {
    setIsTyping(true);
    setCurrentMessage("");

    let i = 0;
    const typingInterval = setInterval(() => {
      if (i < message.length) {
        setCurrentMessage((prev) => prev + message.charAt(i));
        i++;
      } else {
        clearInterval(typingInterval);
        setIsTyping(false);
      }
    }, 30);
  };

  // Mostrar un consejo contextual basado en el análisis
  const handleGetTip = () => {
    // Si tenemos un análisis completo, dar un consejo específico basado en la puntuación
    if (analysisComplete) {
      let customTip = "";

      // Consejos específicos basados en la puntuación SEO
      if (seoScore < 30) {
        const lowScoreTips = [
          "Empieza por asegurarte que cada página tenga un título único y descriptivo de menos de 60 caracteres. Es lo primero que ven los usuarios en los resultados de búsqueda.",
          "Las meta descripciones son cruciales cuando la puntuación es baja. Escribe una de 155-160 caracteres que resuma la página e incluya palabras clave relevantes.",
          "Verifica que tu sitio tenga una estructura clara de encabezados (H1, H2, H3). Cada página debe tener exactamente un H1 y usar H2/H3 para subsecciones.",
          "Implementa HTTPS inmediatamente. Los sitios no seguros son penalizados por Google y generan desconfianza en los usuarios.",
        ];
        customTip =
          lowScoreTips[Math.floor(Math.random() * lowScoreTips.length)];
      } else if (seoScore < 60) {
        const mediumScoreTips = [
          "Optimiza las imágenes con etiquetas alt descriptivas y compresión para mejorar la velocidad de carga y la accesibilidad.",
          "Añade marcado de datos estructurados (Schema.org) para mejorar cómo Google muestra tu contenido en los resultados de búsqueda.",
          "Mejora tu estrategia de enlaces internos conectando páginas relacionadas para distribuir la autoridad y mejorar la navegación.",
          "Verifica que tu sitio sea responsive y se vea bien en dispositivos móviles. Google prioriza la indexación mobile-first.",
        ];
        customTip =
          mediumScoreTips[Math.floor(Math.random() * mediumScoreTips.length)];
      } else if (seoScore < 80) {
        const goodScoreTips = [
          "Mejora tu rendimiento técnico optimizando el tiempo de carga. Comprime archivos, utiliza caching y considera un CDN.",
          "Amplía tu contenido existente con información valiosa adicional. El contenido extenso y detallado suele posicionarse mejor.",
          "Implementa etiquetas canónicas para evitar problemas de contenido duplicado que puedan diluir tu autoridad SEO.",
          "Mejora tus CTAs (llamadas a la acción) para reducir el porcentaje de rebote e incrementar el tiempo de permanencia.",
        ];
        customTip =
          goodScoreTips[Math.floor(Math.random() * goodScoreTips.length)];
      } else {
        const excellentScoreTips = [
          "Considera implementar AMP (Accelerated Mobile Pages) para mejorar aún más la experiencia en dispositivos móviles.",
          "Refina tu estrategia de palabras clave de cola larga para capturar tráfico más específico y con mayor intención de conversión.",
          "Optimiza para búsqueda por voz incorporando preguntas naturales y respuestas directas en tu contenido.",
          "Mantén tu contenido actualizado. Revisa y actualiza tus artículos más importantes al menos cada 6 meses.",
        ];
        customTip =
          excellentScoreTips[
            Math.floor(Math.random() * excellentScoreTips.length)
          ];
      }

      showNewMessage(customTip);
    } else {
      // Si no hay análisis, mostrar un consejo general
      const randomIndex =
        Math.floor(Math.random() * (AGENT_MESSAGES.length - 1)) + 1;
      showNewMessage(AGENT_MESSAGES[randomIndex]);
    }
  };

  return (
    <div
      className={`fixed bottom-4 right-4 z-50 transition-all duration-300 ${isExpanded ? "w-80" : "w-16"}`}
    >
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.8 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="shadow-lg border-blue-200 dark:border-blue-800 bg-gradient-to-br from-white via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-950 dark:to-indigo-950">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className="relative">
                    <motion.div
                      animate={{
                        y: [0, -5, 0],
                      }}
                      transition={{
                        repeat: Infinity,
                        duration: 3,
                        ease: "easeInOut",
                      }}
                      className="w-12 h-12 rounded-full overflow-hidden border-2 border-blue-300 dark:border-blue-700 shadow-md"
                    >
                      <img
                        src={seoAgentImage}
                        alt="SEO Boost Agent"
                        className="w-full h-full object-cover"
                      />
                    </motion.div>
                    {isTyping && (
                      <div className="absolute -bottom-1 -right-1 bg-blue-600 text-white rounded-full w-4 h-4 flex items-center justify-center text-[8px] animate-pulse">
                        ...
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-sm font-semibold text-blue-700 dark:text-blue-300 mb-1 flex items-center">
                      {AGENT_CONFIG.name}
                      <span className="ml-2 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-[10px] px-1.5 py-0.5 rounded-full">
                        IA
                      </span>
                    </h4>
                    <p className="text-[10px] text-blue-600 dark:text-blue-400 -mt-1 mb-1 font-medium">
                      {AGENT_CONFIG.title}
                    </p>
                    <div className="min-h-[60px] text-sm">{currentMessage}</div>
                    <div className="flex gap-2 mt-2 justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs px-2 py-0 h-7"
                        onClick={handleGetTip}
                        disabled={isTyping}
                      >
                        Quiero otro consejo
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsExpanded(!isExpanded)}
        className={`absolute ${isExpanded ? "top-2 right-2" : "bottom-0 right-0"} bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg z-10 flex items-center justify-center transition-all duration-200`}
        style={{
          width: isExpanded ? "24px" : "48px",
          height: isExpanded ? "24px" : "48px",
        }}
      >
        {isExpanded ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        ) : (
          <img
            src={seoAgentImage}
            alt="SEO Boost Agent"
            className="w-full h-full p-1 rounded-full"
          />
        )}
      </motion.button>
    </div>
  );
};

export default SEOAgent;
