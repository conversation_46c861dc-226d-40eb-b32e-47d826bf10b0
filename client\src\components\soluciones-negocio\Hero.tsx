import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, Play } from "lucide-react";
import { <PERSON> } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

const Hero: React.FC = () => {
  const { t } = useLanguage();

  return (
    <section className="pt-40 pb-24 px-4 sm:px-6 lg:px-8 relative overflow-hidden bg-white">
      <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef]/5 via-white to-[#dd3a5a]/5 z-0"></div>

      {/* Elementos decorativos con estilo Emma */}
      <motion.div
        className="absolute top-40 right-[10%] w-20 h-20 rounded-3xl bg-gradient-to-br from-[#3018ef] to-[#3018ef]/80 backdrop-blur-sm shadow-2xl"
        animate={{
          y: [0, -20, 0],
          rotate: [0, 10, 0]
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      />

      <motion.div
        className="absolute bottom-20 left-[10%] w-16 h-16 rounded-3xl bg-gradient-to-br from-[#dd3a5a] to-[#dd3a5a]/80 backdrop-blur-sm shadow-xl"
        animate={{
          y: [0, 15, 0],
          rotate: [0, -8, 0]
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1
        }}
      />

      <motion.div
        className="absolute top-60 left-[15%] w-12 h-12 rounded-3xl bg-white/20 backdrop-blur-md border border-white/30 shadow-xl"
        animate={{
          y: [0, 10, 0],
          x: [0, 10, 0]
        }}
        transition={{ 
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 2
        }}
      />
      
      <div className="container mx-auto relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-2xl"
          >
            <motion.div
              className="inline-block bg-white/20 backdrop-blur-md text-[#3018ef] font-bold text-sm px-4 py-2 rounded-full mb-6 border border-white/30 shadow-xl"
              whileHover={{ scale: 1.05 }}
            >
              {t('soluciones_negocio.hero.badge')}
            </motion.div>
            <h1 className="text-5xl sm:text-6xl font-black mb-6 leading-tight text-gray-900">
              {t('soluciones_negocio.hero.title_part1')} <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]">{t('soluciones_negocio.hero.title_highlight')}</span> {t('soluciones_negocio.hero.title_part2')}
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              {t('soluciones_negocio.hero.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/demo">
                <motion.button
                  className="bg-[#dd3a5a] text-white font-bold py-4 px-8 rounded-3xl shadow-2xl hover:bg-[#c73351] transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="flex items-center justify-center gap-2">
                    {t('soluciones_negocio.hero.start_now')} <Rocket size={20} />
                  </span>
                </motion.button>
              </Link>
              <Link href="/demo">
                <motion.button
                  className="bg-white/20 backdrop-blur-md text-[#3018ef] font-bold py-4 px-8 rounded-3xl border border-white/30 shadow-xl hover:bg-white/30 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="flex items-center justify-center gap-2">
                    {t('soluciones_negocio.hero.watch_demo')} <Play size={20} />
                  </span>
                </motion.button>
              </Link>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="relative">
              <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-3xl p-1">
                <div className="bg-white/90 backdrop-blur-md rounded-3xl p-6 shadow-2xl">
                  <div className="bg-gray-900 rounded-2xl p-2 flex items-center mb-6">
                    <div className="flex space-x-2 mr-auto">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-white/80 text-sm">Emma AI Marketing Assistant</div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-white font-bold">E</span>
                      </div>
                      <div className="bg-gray-100 rounded-2xl rounded-tl-none p-4 text-gray-800">
                        <p>{t('soluciones_negocio.hero.chat.emma_greeting')}</p>
                      </div>
                    </div>

                    <div className="flex items-start justify-end">
                      <div className="bg-[#3018ef] rounded-2xl rounded-tr-none p-4 text-white">
                        <p>{t('soluciones_negocio.hero.chat.user_request')}</p>
                      </div>
                      <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center ml-3 flex-shrink-0">
                        <span className="text-white font-bold">U</span>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-white font-bold">E</span>
                      </div>
                      <div className="bg-gray-100 rounded-2xl rounded-tl-none p-4 text-gray-800">
                        <p>{t('soluciones_negocio.hero.chat.emma_response')}</p>
                        <div className="mt-3 space-y-2">
                          <div className="bg-gray-200 rounded-lg p-2 text-sm">
                            <p className="font-bold text-blue-600">{t('soluciones_negocio.hero.chat.tasks_in_progress')}</p>
                            <ul className="mt-1 space-y-1">
                              <li className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-green-400 mr-2"></div>
                                <span>{t('soluciones_negocio.hero.chat.analyzing_audience')}</span>
                              </li>
                              <li className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-yellow-400 mr-2"></div>
                                <span>{t('soluciones_negocio.hero.chat.generating_subjects')}</span>
                              </li>
                              <li className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-blue-400 mr-2"></div>
                                <span>{t('soluciones_negocio.hero.chat.designing_sequence')}</span>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-white font-bold">E</span>
                      </div>
                      <div className="bg-gray-100 rounded-xl rounded-tl-none p-4 text-gray-800">
                        <p className="font-bold text-green-600">{t('soluciones_negocio.hero.chat.campaign_success')}</p>
                        <p className="mt-2">{t('soluciones_negocio.hero.chat.campaign_description')}</p>
                        <div className="mt-3 flex space-x-2">
                          <button className="bg-blue-600 text-white text-sm py-1 px-3 rounded-lg border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">{t('soluciones_negocio.hero.chat.view_campaign')}</button>
                          <button className="bg-gray-700 text-white text-sm py-1 px-3 rounded-lg border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">{t('soluciones_negocio.hero.chat.edit')}</button>
                          <button className="bg-gray-700 text-white text-sm py-1 px-3 rounded-lg border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">{t('soluciones_negocio.hero.chat.schedule')}</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Decorative elements */}
            <motion.div
              className="absolute -top-6 -right-6 w-20 h-20 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full opacity-20 blur-xl"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.2, 0.4, 0.2]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
            <motion.div
              className="absolute -bottom-6 -left-6 w-20 h-20 bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] rounded-full opacity-20 blur-xl"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.2, 0.4, 0.2]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                repeatType: "reverse",
                delay: 0.5
              }}
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
