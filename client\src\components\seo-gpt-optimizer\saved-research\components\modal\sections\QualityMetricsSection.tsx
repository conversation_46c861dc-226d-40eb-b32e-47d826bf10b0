/**
 * SEO & GPT Optimizer™ - Quality Metrics Section
 * Component for displaying research quality metrics
 */

import React from 'react';

interface QualityMetricsSectionProps {
  data?: any;
}

const QualityMetricsSection: React.FC<QualityMetricsSectionProps> = ({ data }) => {
  if (!data) {
    return null;
  }

  return (
    <div className="mb-6">
      <h3 className="text-lg font-bold text-gray-900 mb-3">Métricas de Calidad</h3>
      <div className="bg-indigo-50 rounded-xl p-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-indigo-600">
              {(data.data_completeness * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Completitud de Datos</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-indigo-600">
              {(data.source_diversity * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Diversidad de Fuentes</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QualityMetricsSection;
