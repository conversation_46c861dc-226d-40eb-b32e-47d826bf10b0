import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import LanguageSelector from './LanguageSelector';

const LandingTranslationTest: React.FC = () => {
  const { t, currentLanguage, changeLanguage } = useLanguage();

  const handleForceChange = () => {
    const newLang = currentLanguage === 'es' ? 'en' : 'es';
    console.log('🚀 FORCE CHANGE:', currentLanguage, '->', newLang);

    // Clear localStorage first
    localStorage.removeItem('emma-language');
    console.log('🗑️ Cleared localStorage');

    // Force change
    changeLanguage(newLang);

    // Force page refresh if needed
    setTimeout(() => {
      console.log('🔄 Checking if change worked...');
      const saved = localStorage.getItem('emma-language');
      console.log('💾 Saved language:', saved);
    }, 100);
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-white/90 backdrop-blur-sm p-4 rounded-lg shadow-lg border">
      <div className="flex items-center gap-3 mb-3">
        <span className="text-sm font-medium">Idioma:</span>
        <LanguageSelector variant="toggle" />
      </div>

      <button
        onClick={handleForceChange}
        className="w-full mb-3 px-3 py-2 bg-[#3018ef] text-white text-sm rounded-lg hover:bg-[#2516d6] transition-colors"
      >
        🔄 FORZAR CAMBIO A {currentLanguage === 'es' ? 'EN' : 'ES'}
      </button>
      
      <div className="text-xs space-y-1 text-gray-600 max-w-xs">
        <p><strong>Actual:</strong> {currentLanguage.toUpperCase()}</p>
        <p><strong>Hero:</strong> {t('landing.hero_title')}</p>
        <p><strong>Profesionales:</strong> {t('landing.professionals_ai')}</p>
        <p><strong>Soluciones:</strong> {t('landing.solutions')}</p>
        <p><strong>Blog:</strong> {t('navigation.blog')}</p>
        <p><strong>Planes:</strong> {t('landing.plans')}</p>
      </div>
      
      <div className="mt-2 text-xs text-gray-400">
        Prueba de traducción en vivo
      </div>
    </div>
  );
};

export default LandingTranslationTest;
