import { useState, useCallback } from 'react';

// Supported languages
export type Language = 'es' | 'en';

// Translation type structure
type TranslationKeys = {
  navigation: {
    home: string;
    dashboard: string;
    tools: string;
    agents: string;
    blog: string;
    login: string;
    register: string;
    profile: string;
    logout: string;
    professionals: string;
    solutions: string;
    plans: string;
    calculator: string;
  };
  dashboard: {
    welcome: string;
    tools: string;
    recent: string;
    favorites: string;
    create: string;
    edit: string;
    delete: string;
    save: string;
    cancel: string;
    new: string;
    open: string;
  };
  landing: {
    hero_title: string;
    hero_subtitle: string;
    get_started: string;
    learn_more: string;
    features: string;
    pricing: string;
    testimonials: string;
    try_now: string;
    free_trial: string;
    professionals_ai: string;
    ai_powered_marketing: string;
    hero_make_your: string;
    hero_marketing: string;
    view_demo: string;
    solutions: string;
    plans: string;
    calculator: string;
    create_content: string;
    ai_powered: string;
    digital_marketing: string;
    content_creation: string;
    // Hero section
    hero_prefix: string;
    hero_word: string;
    hero_rotating_words: string[];
    start_now: string;
    see_demo: string;
    // Platform features
    platform_heading: string;
    platform_description: string;
    marketplace_agents: string;
    marketplace_description: string;
    editor: string;
    editor_description: string;
    emma_ai: string;
    emma_ai_description: string;
    visual_studio: string;
    visual_studio_description: string;
    marketing_tools: string;
    marketing_tools_description: string;
    ads_central: string;
    ads_central_description: string;
    // Solution section
    traditional_problems: string;
    ai_solution: string;
    expensive_agencies: string;
    slow_delivery: string;
    limited_creativity: string;
    no_data_insights: string;
    instant_content: string;
    unlimited_creativity: string;
    real_time_optimization: string;
    affordable_pricing: string;
    // Marketing Tools section (English only)
    specialized_tools: string;
    specialized_tools_description: string;
    specialized_tools_subtitle: string;
    design_tools: string;
    design_tools_title: string;
    design_tools_content: string;
    visual_complexity_analyzer: string;
    color_palette_generator: string;
    interactive_mood_board: string;
    audience_tools: string;
    audience_tools_title: string;
    audience_tools_content: string;
    buyer_persona_generator: string;
    focus_group_simulator: string;
    seo_tools: string;
    seo_tools_title: string;
    seo_tools_content: string;
    seo_analyzer: string;
    seo_gpt_optimizer: string;
    seo_ecommerce_optimizer: string;
    title_analyzer: string;
    // Agent Showcase section (English only)
    specialized_ai_team: string;
    meet_virtual_team: string;
    virtual_marketing_team: string;
    agents_work_247: string;
    team_capabilities: string;
    specialization: string;
    specialization_desc: string;
    speed: string;
    speed_desc: string;
    collaboration: string;
    collaboration_desc: string;
    specialized_agents: string;
    availability: string;
    average_rating: string;
    total_reviews: string;
    need_more_specialists: string;
    agents_marketplace_description: string;
    analytics_data: string;
    analytics_desc: string;
    design_creative: string;
    design_desc: string;
    email_marketing: string;
    email_desc: string;
    explore_all_agents: string;
    // Platform Showcase section (English only)
    all_tools_one_place: string;
    discover_professional_tools: string;
    // Proof Results section (English only)
    real_results: string;
    emma_studio_numbers: string;
    numbers_that_speak: string;
    thousands_companies_transforming: string;
    real_platform_statistics: string;
    images_generated: string;
    images_generated_desc: string;
    blogs_optimized: string;
    blogs_optimized_desc: string;
    brands_positioned: string;
    brands_positioned_desc: string;
    buyer_personas_created: string;
    buyer_personas_desc: string;
    color_palettes: string;
    color_palettes_desc: string;
    seo_analysis_performed: string;
    seo_analysis_desc: string;
    posts_generated: string;
    posts_generated_desc: string;
    titles_optimized: string;
    titles_optimized_desc: string;
    join_thousands_companies: string;
    // Pricing Comparison section (English only)
    pricing_revolution: string;
    why_pay_96k: string;
    better_results: string;
    for_1188: string;
    compare_prices_description: string;
    obsolete: string;
    traditional_agency: string;
    month: string;
    year: string;
    obsolete_model_description: string;
    // Testimonials section (English only)
    real_client_testimonials: string;
    what_clients_say: string;
    clients: string;
    about_emma_studio: string;
    over_1000_companies: string;
    // FAQ section (English only)
    frequently_asked_questions: string;
    everything_need_to_know: string;
    about_emma_studio_faq: string;
    resolve_common_doubts: string;
    // FAQ Questions and Answers (English only)
    faq_q1: string;
    faq_a1: string;
    faq_q2: string;
    faq_a2: string;
    faq_q3: string;
    faq_a3: string;
    faq_q4: string;
    faq_a4: string;
    faq_q5: string;
    faq_a5: string;
    faq_q6: string;
    faq_a6: string;
    faq_q7: string;
    faq_a7: string;
    faq_q8: string;
    faq_a8: string;
    faq_q9: string;
    faq_a9: string;
    faq_q10: string;
    faq_a10: string;
    // FAQ CTA section
    cant_find_answer: string;
    support_team_available: string;
    contact_support: string;
    // Final CTA section (English only)
    digital_transformation_starts: string;
    ready_to_revolutionize: string;
    revolutionize: string;
    your_marketing: string;
    join_1000_companies: string;
    // Footer section (English only)
    footer_description: string;
    platform: string;
    resources: string;
    company: string;
    ai_agents: string;
    tools: string;
    pricing_footer: string;
    marketplace: string;
    help_center: string;
    tutorials: string;
    api: string;
    integrations: string;
    about_us: string;
    contact: string;
    terms_of_service: string;
    privacy_policy: string;
    // Buttons and CTAs (English only)
    start_now_cta: string;
    see_live_demo: string;
    subscribe: string;
    stay_updated: string;
    receive_updates: string;
    your_email: string;
    ready_to_revolutionize_footer: string;
    start_now_arrow: string;
    // Pricing section additional
    most_popular: string;
    view_plans: string;
    roi_calculator: string;
    see_how_much_save: string;
    annual_average_savings: string;
    time_saved_per_year: string;
    average_roi_increase: string;
    calculate_personalized_savings: string;
    // Solution section additional
    digital_marketing_revolution: string;
    emma_does_work_agency: string;
    emma_agency_complete: string;
    per_month: string;
    first_virtual_agency: string;
    transparent_pricing: string;
    transparent_pricing_desc: string;
    instant_speed: string;
    instant_speed_desc: string;
    superior_quality: string;
    superior_quality_desc: string;
    infinite_scalability: string;
    infinite_scalability_desc: string;
    pricing_benefit: string;
    pricing_comparison: string;
    speed_benefit: string;
    speed_comparison: string;
    quality_benefit: string;
    quality_comparison: string;
    scalability_benefit: string;
    scalability_comparison: string;
    marketing_broken_emma_fixes: string;
    explore_tool: string;
    // Marketing Tools section
    ai_toolbox_marketers: string;
    advanced_tools_marketing: string;
    explore_all_tools: string;

  };
  tools: {
    image_generator: string;
    video_creator: string;
    content_writer: string;
    seo_optimizer: string;
    ad_creator: string;
    logo_generator: string;
    background_remover: string;
    style_transfer: string;
  };
  common: {
    loading: string;
    error: string;
    success: string;
    warning: string;
    info: string;
    close: string;
    open: string;
    search: string;
    filter: string;
    sort: string;
    export: string;
    import: string;
    download: string;
    upload: string;
    copy: string;
    paste: string;
    cut: string;
    undo: string;
    redo: string;
    back: string;
    next: string;
    previous: string;
    continue: string;
  };
  auth: {
    email: string;
    password: string;
    confirm_password: string;
    forgot_password: string;
    remember_me: string;
    sign_in: string;
    sign_up: string;
    sign_out: string;
    create_account: string;
    already_have_account: string;
    dont_have_account: string;
  };
  forms: {
    required: string;
    invalid_email: string;
    password_too_short: string;
    passwords_dont_match: string;
    submit: string;
    reset: string;
    clear: string;
    generate: string;
    preview: string;
  };
  seo: {
    title: string;
    description: string;
    keywords: string;
    meta_description: string;
    analyze: string;
    optimize: string;
    score: string;
  };
  emma: {
    brand_name: string;
    tagline: string;
    professionals: string;
    solutions: string;
    plans: string;
    calculator: string;
    get_started: string;
    try_free: string;
    learn_more: string;
  };
};



// Detect browser language - but prioritize user choice
const detectBrowserLanguage = (): Language => {
  const browserLang = navigator.language.toLowerCase();
  if (browserLang.startsWith('es')) return 'es';
  return 'es'; // Default to Spanish since it's Emma's primary language
};

// Get saved language from localStorage - prioritize user choice over browser
const getSavedLanguage = (): Language => {
  try {
    const saved = localStorage.getItem('emma-language') as Language;
    if (saved && ['es', 'en'].includes(saved)) {
      return saved;
    }
    // Only use browser detection if no user preference exists
    const detected = detectBrowserLanguage();
    return detected;
  } catch {
    return 'es';
  }
};

// Save language to localStorage
const saveLanguage = (lang: Language): void => {
  try {
    localStorage.setItem('emma-language', lang);
  } catch {
    // Ignore localStorage errors
  }
};

// Import JSON translations
import esTranslations from '@/locales/es.json';
import enTranslations from '@/locales/en.json';

// Static translations object - loaded from JSON files
const translations: Record<Language, any> = {
  es: esTranslations,
  en: enTranslations
};

export const useTranslation = () => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>(getSavedLanguage);
  const [forceUpdate, setForceUpdate] = useState(0);

  // Change language function
  const changeLanguage = useCallback((newLang: Language) => {
    if (newLang !== currentLanguage) {
      setCurrentLanguage(newLang);
      saveLanguage(newLang);
      setForceUpdate(prev => prev + 1); // Force re-render
    }
  }, [currentLanguage]);

  // Translation function with nested key support
  const t = useCallback((key: string): any => {
    const currentTranslations = translations[currentLanguage] || translations.es;
    const keys = key.split('.');
    let value: any = currentTranslations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Return key if translation not found
      }
    }

    // Return the value as-is (could be string, array, etc.)
    return value;
  }, [currentLanguage, forceUpdate]); // Include forceUpdate to trigger re-renders

  return {
    currentLanguage,
    changeLanguage,
    t,
    isLoading: false, // No longer async
    availableLanguages: ['es', 'en'] as Language[],
  };
};

export default useTranslation;
