"use client"

import { motion } from "framer-motion"
import { useState } from "react"
import { useLanguage } from "@/contexts/LanguageContext"

export function EmmaConceptSection() {
  const { t, currentLanguage } = useLanguage()
  const [activeTab, setActiveTab] = useState(0)

  const tabs = [
    {
      id: "gran-idea",
      title: currentLanguage === 'en' ? "💡 The Big Idea" : "💡 La Gran Idea",
      content: {
        headline: currentLanguage === 'en' ? "Marketing Today vs Emma AI" : "El Marketing Hoy vs Emma AI",
        description: currentLanguage === 'en'
          ? "Marketing today is a mess: multiple tools, freelancers, agencies, coordination... Emma AI is everything you need in one place."
          : "El marketing hoy es un desmadre: múltiples herramientas, freelancers, agencias, coordinación... Emma AI es todo lo que necesitas en un solo lugar.",
        comparison: [
          {
            traditional: currentLanguage === 'en' ? "🔧 10+ separate tools" : "🔧 10+ herramientas separadas",
            emma: currentLanguage === 'en' ? "⚡ Everything integrated in Emma" : "⚡ Todo integrado en Emma"
          },
          {
            traditional: currentLanguage === 'en' ? "💰 $5,000-15,000/month total" : "💰 $5,000-15,000/mes total",
            emma: currentLanguage === 'en' ? "💎 $49-199/month (95% cheaper)" : "💎 $49-199/mes (95% más barato)"
          },
          {
            traditional: currentLanguage === 'en' ? "📅 Multiple contracts and payments" : "📅 Múltiples contratos y pagos",
            emma: currentLanguage === 'en' ? "🆓 One subscription, no contracts" : "🆓 Una sola suscripción, sin contratos"
          },
          {
            traditional: currentLanguage === 'en' ? "⏰ Coordinate everyone's schedules" : "⏰ Coordinar horarios de todos",
            emma: currentLanguage === 'en' ? "🌙 Available 24/7 when you need it" : "🌙 Disponible 24/7 cuando lo necesites"
          }
        ]
      }
    },
    {
      id: "software-servicio",
      title: currentLanguage === 'en' ? "⚡ Emma is Software" : "⚡ Emma es Software",
      content: {
        headline: currentLanguage === 'en' ? "Everything You Need for Your Marketing" : "Todo lo que Necesitas para tu Marketing",
        description: currentLanguage === 'en'
          ? "Emma is everything you need to handle your marketing: strategy, content, design, ads, SEO... all in one place."
          : "Emma es todo lo que necesitas para llevar tu marketing: estrategia, contenido, diseño, anuncios, SEO... todo en un solo lugar.",
        features: [
          {
            icon: "💬",
            title: currentLanguage === 'en' ? "Conversational" : "Conversacional",
            desc: currentLanguage === 'en' ? "Talk to Emma like ChatGPT" : "Hablas con Emma como con ChatGPT"
          },
          {
            icon: "🖱️",
            title: currentLanguage === 'en' ? "Interactive" : "Interactivo",
            desc: currentLanguage === 'en' ? "Click, adjust and see live results" : "Clicas, ajustas y ves resultados live"
          },
          {
            icon: "🎯",
            title: currentLanguage === 'en' ? "Specialized" : "Especializado",
            desc: currentLanguage === 'en' ? "Each agent is an expert in their area" : "Cada agente es experto en su área"
          },
          {
            icon: "⚡",
            title: currentLanguage === 'en' ? "Instant" : "Instantáneo",
            desc: currentLanguage === 'en' ? "Results in minutes, not weeks" : "Resultados en minutos, no semanas"
          }
        ]
      }
    }
  ]

  return (
    <section className="py-12 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #3018ef 2px, transparent 2px),
                           radial-gradient(circle at 75% 75%, #dd3a5a 2px, transparent 2px)`,
          backgroundSize: '50px 50px'
        }} />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-6xl mx-auto">
          
          {/* Tab Navigation */}
          <motion.div
            className="flex justify-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-gray-200/50">
              {tabs.map((tab, index) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(index)}
                  className={`px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300 ${
                    activeTab === index
                      ? 'bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white shadow-lg'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100/50'
                  }`}
                >
                  {tab.title}
                </button>
              ))}
            </div>
          </motion.div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            className="bg-white/90 backdrop-blur-sm rounded-3xl p-6 sm:p-8 shadow-xl border border-gray-200/50"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            
            {/* Header */}
            <div className="text-center mb-8">
              <h2 className="text-3xl sm:text-4xl font-black text-gray-900 mb-4">
                <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                  {tabs[activeTab].content.headline}
                </span>
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {tabs[activeTab].content.description}
              </p>
            </div>

            {/* Content Based on Active Tab */}
            {activeTab === 0 && (
              <div className="grid md:grid-cols-2 gap-6">
                
                {/* Traditional */}
                <motion.div
                  className="bg-red-50 border-2 border-red-200 rounded-2xl p-6"
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <div className="text-center mb-4">
                    <span className="bg-red-500 text-white px-4 py-2 rounded-full font-bold text-sm">
                      😵‍💫 {currentLanguage === 'en' ? 'Marketing Today (A Mess)' : 'Marketing Hoy (Un Desmadre)'}
                    </span>
                  </div>
                  <div className="space-y-3">
                    {tabs[0].content.comparison.map((item, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <span className="text-red-500 text-sm">•</span>
                        <span className="text-gray-700 text-sm">{item.traditional}</span>
                      </div>
                    ))}
                  </div>
                </motion.div>

                {/* Emma */}
                <motion.div
                  className="bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-[#3018ef]/30 rounded-2xl p-6"
                  initial={{ opacity: 0, x: 30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <div className="text-center mb-4">
                    <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white px-4 py-2 rounded-full font-bold text-sm">
                      ✅ Emma AI
                    </span>
                  </div>
                  <div className="space-y-3">
                    {tabs[0].content.comparison.map((item, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <span className="text-[#3018ef] text-sm">•</span>
                        <span className="text-gray-700 text-sm">{item.emma}</span>
                      </div>
                    ))}
                  </div>
                </motion.div>

              </div>
            )}

            {activeTab === 1 && (
              <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {tabs[1].content.features.map((feature, index) => (
                  <motion.div
                    key={index}
                    className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-5 shadow-lg border border-gray-200/50 text-center"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
                  >
                    <span className="text-3xl mb-3 block">{feature.icon}</span>
                    <h3 className="text-lg font-bold text-gray-800 mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {feature.desc}
                    </p>
                  </motion.div>
                ))}
              </div>
            )}

            {/* Bottom Analogy - Only show on first tab */}
            {activeTab === 0 && (
              <motion.div
                className="mt-8 text-center bg-gradient-to-r from-gray-50 to-white rounded-2xl p-6 border border-gray-200/50"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  🧠 {currentLanguage === 'en' ? 'Simple Analogy:' : 'Analogía Simple:'}
                </h3>
                <p className="text-base text-gray-700 leading-relaxed max-w-2xl mx-auto">
                  <strong>
                    {currentLanguage === 'en'
                      ? 'Using Emma is like playing CEO with a team that never gets tired.'
                      : 'Usar Emma es como jugar al CEO con un equipo que nunca se cansa.'}
                  </strong><br />
                  {currentLanguage === 'en'
                    ? 'You just give the order. Emma turns it into '
                    : 'Solo das la orden. Emma la convierte en '}
                  <strong>
                    {currentLanguage === 'en'
                      ? 'ads, designs, copy and results'
                      : 'anuncios, diseños, copys y resultados'}
                  </strong>.
                  {currentLanguage === 'en'
                    ? ' Everything you need to handle your marketing, integrated in one place.'
                    : ' Todo lo que necesitas para llevar tu marketing, integrado en un solo lugar.'}
                </p>
              </motion.div>
            )}

          </motion.div>

        </div>
      </div>
    </section>
  )
}
