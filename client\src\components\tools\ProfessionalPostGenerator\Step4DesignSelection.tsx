import React, { useState } from "react";
import { motion } from "framer-motion";
import { ArrowLeft, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import ProgressIndicator from "./components/ProgressIndicator";
import IntelligentEmmaMessage from "./components/IntelligentEmmaMessage";
import { BrandAnalysis } from "./hooks/useBrandData";

interface Theme {
  name: string;
  image: string;
  category: string;
  description: string;
}

interface Step4DesignSelectionProps {
  selectedTheme: string;
  analysisComplete: boolean;
  brandAnalysis: BrandAnalysis | null;
  onThemeSelect: (themeName: string) => void;
  onContentTypeSelect: (contentType: string) => void;
  onBack: () => void;
  onNext: () => void;
  // Datos completos de todos los pasos
  brandData?: {
    brandUrl: string;
    brandDescription: string;
    businessName: string;
    brandColor: string;
    voice: string;
    topics: string[];
    ctas: string[];
  };
}

interface ContentType {
  id: string;
  name: string;
  platform: string;
  icon: string;
}

const Step4DesignSelection: React.FC<Step4DesignSelectionProps> = ({
  selectedTheme,
  analysisComplete,
  brandAnalysis,
  onThemeSelect,
  onContentTypeSelect,
  onBack,
  onNext,
  brandData,
}) => {
  const [showAllThemes, setShowAllThemes] = useState(false);
  const [selectedContentType, setSelectedContentType] = useState("instagram_posts");

  const contentTypes: ContentType[] = [
    { id: "instagram_posts", name: "Instagram Posts", platform: "Instagram", icon: "📸" },
    { id: "instagram_stories", name: "Instagram Stories", platform: "Instagram", icon: "📱" },
    { id: "facebook_posts", name: "Facebook Posts", platform: "Facebook", icon: "👥" },
    { id: "linkedin_posts", name: "LinkedIn Posts", platform: "LinkedIn", icon: "💼" },
    { id: "twitter_posts", name: "X Posts", platform: "X (Twitter)", icon: "🐦" }
  ];
  const themes: Theme[] = [
    // EDUCATIVO/PROFESIONAL
    { name: "Balance", image: "/api/placeholder/120/80", category: "Educativo", description: "Tips empresariales y estadísticas" },
    { name: "Interface", image: "/api/placeholder/120/80", category: "Educativo", description: "Tutoriales tech y procesos" },
    { name: "Fonts", image: "/api/placeholder/120/80", category: "Educativo", description: "Quotes profesionales y frases de líderes" },
    { name: "Educational", image: "/api/placeholder/120/80", category: "Educativo", description: "Contenido educativo y aprendizaje" },
    { name: "Informativo", image: "/api/placeholder/120/80", category: "Educativo", description: "Noticias relevantes de tu industria" },

    // MOTIVACIONAL/INSPIRACIONAL
    { name: "Motivational", image: "/api/placeholder/120/80", category: "Motivacional", description: "Frases inspiradoras y superación" },
    { name: "Success", image: "/api/placeholder/120/80", category: "Motivacional", description: "Historias de éxito y logros" },
    { name: "Mindset", image: "/api/placeholder/120/80", category: "Motivacional", description: "Mentalidad y crecimiento personal" },

    // ENTRETENIMIENTO/VIRAL
    { name: "Meme", image: "/api/placeholder/120/80", category: "Entretenimiento", description: "Humor y contenido relatable" },
    { name: "Comic", image: "/api/placeholder/120/80", category: "Entretenimiento", description: "Ilustraciones divertidas y storytelling" },
    { name: "Fun Facts", image: "/api/placeholder/120/80", category: "Entretenimiento", description: "Datos curiosos y ¿sabías que?" },

    // HUMANO/PERSONAL
    { name: "Influencer", image: "/api/placeholder/120/80", category: "Personal", description: "Behind the scenes y personal branding" },
    { name: "Story", image: "/api/placeholder/120/80", category: "Personal", description: "Narrativas y experiencias reales" },
    { name: "Behind Scenes", image: "/api/placeholder/120/80", category: "Personal", description: "Detrás de cámaras del equipo" },

    // REDES ESPECÍFICAS
    { name: "Tweet", image: "/api/placeholder/120/80", category: "Redes", description: "Texto-heavy, hilos y micro-content" },
    { name: "Instagram Story", image: "/api/placeholder/120/80", category: "Redes", description: "Vertical e interactivo" },
    { name: "LinkedIn Post", image: "/api/placeholder/120/80", category: "Redes", description: "Profesional y networking" },

    // PRODUCTO/VENTAS
    { name: "Simply Image", image: "/api/placeholder/120/80", category: "Producto", description: "Product showcase y diseño limpio" },
    { name: "Before/After", image: "/api/placeholder/120/80", category: "Producto", description: "Transformaciones y resultados" },
    { name: "Announcement", image: "/api/placeholder/120/80", category: "Producto", description: "Lanzamientos y novedades" }
  ];

  // Mostrar solo los primeros 6 themes por defecto
  const displayedThemes = showAllThemes ? themes : themes.slice(0, 6);

  // Función para generar prompt específico por template
  const generateTemplatePrompt = (themeName: string, brandInfo: any, contentType: string) => {
    const basePrompts = {
      // EDUCATIVO/PROFESIONAL
      "Balance": `Crea posts educativos y profesionales sobre ${brandInfo.businessName}. Enfócate en tips empresariales, estadísticas relevantes de la industria, y consejos prácticos. Mantén un tono equilibrado entre profesional y accesible.`,

      "Interface": `Genera posts tipo tutorial y tech para ${brandInfo.businessName}. Incluye procesos paso a paso, explicaciones técnicas simplificadas, y contenido que eduque sobre tecnología o metodologías.`,

      "Fonts": `Crea posts con quotes profesionales y frases inspiradoras de líderes para ${brandInfo.businessName}. Enfócate en citas motivacionales del mundo empresarial y frases que reflejen los valores de la marca.`,

      "Educational": `Desarrolla contenido puramente educativo para ${brandInfo.businessName}. Crea posts que enseñen algo nuevo, expliquen conceptos complejos de forma simple, y aporten valor educativo real.`,

      "Informativo": `Genera posts informativos con noticias relevantes de la industria de ${brandInfo.businessName}. Incluye tendencias del sector, actualizaciones importantes, y análisis de mercado.`,

      // MOTIVACIONAL/INSPIRACIONAL
      "Motivational": `Crea posts motivacionales e inspiradores para ${brandInfo.businessName}. Enfócate en frases que inspiren acción, historias de superación, y contenido que motive a la audiencia.`,

      "Success": `Desarrolla posts sobre historias de éxito relacionadas con ${brandInfo.businessName}. Incluye casos de éxito, logros alcanzados, y testimonios inspiradores.`,

      "Mindset": `Genera contenido sobre mentalidad y crecimiento personal para ${brandInfo.businessName}. Enfócate en desarrollo personal, cambio de perspectiva, y mindset empresarial.`,

      // ENTRETENIMIENTO/VIRAL
      "Meme": `Crea posts con humor y contenido relatable para ${brandInfo.businessName}. Usa memes relevantes a la industria, situaciones divertidas del día a día empresarial, y humor inteligente.`,

      "Comic": `Desarrolla posts con ilustraciones divertidas y storytelling visual para ${brandInfo.businessName}. Crea narrativas visuales, historias cortas con moraleja, y contenido que entretenga.`,

      "Fun Facts": `Genera posts con datos curiosos y "¿sabías que?" relacionados con ${brandInfo.businessName} y su industria. Incluye estadísticas sorprendentes y hechos interesantes.`,

      // HUMANO/PERSONAL
      "Influencer": `Crea posts estilo personal branding y behind the scenes para ${brandInfo.businessName}. Muestra el lado humano de la marca, historias personales, y contenido auténtico.`,

      "Story": `Desarrolla posts narrativos con experiencias reales de ${brandInfo.businessName}. Cuenta historias de la empresa, anécdotas significativas, y experiencias que conecten emocionalmente.`,

      "Behind Scenes": `Genera contenido detrás de cámaras del equipo de ${brandInfo.businessName}. Muestra el proceso de trabajo, el día a día del equipo, y la cultura empresarial.`,

      // REDES ESPECÍFICAS
      "Tweet": `Crea posts estilo Twitter con texto-heavy, hilos y micro-content para ${brandInfo.businessName}. Enfócate en mensajes concisos, hilos informativos, y contenido fácil de compartir.`,

      "Instagram Story": `Desarrolla contenido vertical e interactivo para ${brandInfo.businessName}. Crea posts optimizados para stories, con elementos visuales llamativos y CTAs claros.`,

      "LinkedIn Post": `Genera posts profesionales optimizados para networking de ${brandInfo.businessName}. Enfócate en contenido B2B, insights de industria, y networking profesional.`,

      // PRODUCTO/VENTAS
      "Simply Image": `Crea posts de product showcase con diseño limpio para ${brandInfo.businessName}. Enfócate en mostrar productos/servicios de forma elegante y minimalista.`,

      "Before/After": `Desarrolla posts mostrando transformaciones y resultados de ${brandInfo.businessName}. Incluye casos antes/después, mejoras logradas, y resultados tangibles.`,

      "Announcement": `Genera posts de lanzamientos y novedades para ${brandInfo.businessName}. Anuncia nuevos productos, servicios, o actualizaciones importantes de la empresa.`
    };

    const platformSpecific = {
      "Instagram": "Optimiza para Instagram con hashtags relevantes, formato cuadrado, y estilo visual atractivo.",
      "Facebook": "Adapta para Facebook con texto más largo, enfoque en engagement, y formato que invite a comentarios.",
      "LinkedIn": "Profesionaliza para LinkedIn con tono B2B, networking focus, y contenido de valor empresarial.",
      "X (Twitter)": "Simplifica para X con mensajes concisos, hashtags trending, y formato fácil de retwittear."
    };

    return `${basePrompts[themeName] || basePrompts["Balance"]} ${platformSpecific[contentType] || platformSpecific["Instagram"]}`;
  };

  // Función para generar posts - usar el workflow principal
  const handleGeneratePosts = () => {
    console.log("🎯 Step4DesignSelection - handleGeneratePosts called!");

    if (!brandData) {
      console.error("❌ No brand data available");
      return;
    }

    console.log("🚀 Proceeding to post generation with data:", {
      brandData,
      selectedTheme,
      selectedContentType
    });

    console.log("📞 Calling onNext() to proceed to PostGenerator...");
    // Llamar a onNext para continuar con el workflow principal
    onNext();
    console.log("✅ onNext() called successfully");
  };

  return (
    <div className="max-w-6xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden"
      >
        <ProgressIndicator currentStep={4} totalSteps={4} />

        {/* Content Type Selector */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 px-8 py-6 border-b border-gray-200">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Tipo de Contenido</h3>
            <p className="text-sm text-gray-600">Selecciona el formato de contenido que quieres generar</p>
          </div>

          <div className="flex flex-wrap gap-3">
            {contentTypes.map((type) => (
              <button
                key={type.id}
                onClick={() => {
                  setSelectedContentType(type.id);
                  onContentTypeSelect(type.id);
                }}
                className={`flex items-center px-4 py-2 rounded-lg border-2 transition-all ${
                  selectedContentType === type.id
                    ? 'border-purple-500 bg-purple-500 text-white shadow-md'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-purple-300 hover:bg-purple-50'
                }`}
              >
                <span className="text-lg mr-2">{type.icon}</span>
                <div className="text-left">
                  <div className="font-medium text-sm">{type.name}</div>
                  <div className="text-xs opacity-75">{type.platform}</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="flex">
          {/* Panel izquierdo */}
          <div className="w-1/2 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Selección de Diseño</h2>
              <p className="text-gray-600 mb-4">Personaliza el aspecto visual de tus publicaciones para crear una imagen de marca única y reconocible.</p>
              
              <IntelligentEmmaMessage
                step={4}
                brandData={brandData}
                brandAnalysis={brandAnalysis}
                analysisComplete={analysisComplete}
                color="green"
                fallbackMessage={`¡Excelente! Vamos a seleccionar el diseño perfecto para tus publicaciones.`}
              />
            </div>

            {/* Themes */}
            <div className="mb-8">
              <div className="flex items-center mb-4">
                <h3 className="text-gray-900 font-semibold">Temas de Diseño</h3>
                <HelpCircle className="w-4 h-4 text-gray-400 ml-2" />
              </div>
              
              <div className="grid grid-cols-3 gap-3 mb-4">
                {displayedThemes.map((theme) => (
                  <div 
                    key={theme.name}
                    className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                      selectedTheme === theme.name 
                        ? 'border-purple-500 ring-2 ring-purple-200' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => onThemeSelect(theme.name)}
                  >
                    {/* Checkbox */}
                    <div className="absolute top-2 left-2 z-10">
                      <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                        selectedTheme === theme.name 
                          ? 'bg-purple-500 border-purple-500' 
                          : 'bg-white border-gray-300'
                      }`}>
                        {selectedTheme === theme.name && (
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        )}
                      </div>
                    </div>
                    
                    {/* Category badge */}
                    <div className="absolute top-2 right-2 z-10">
                      <div className="bg-purple-500 text-white text-xs px-2 py-1 rounded-full">
                        {theme.category}
                      </div>
                    </div>

                    {/* Theme preview */}
                    <div className="aspect-[3/2] bg-gradient-to-br from-gray-100 to-gray-200 flex flex-col items-center justify-center p-2">
                      <div className="text-xs font-semibold text-gray-700 mb-1">{theme.name}</div>
                      <div className="text-xs text-gray-500 text-center leading-tight">{theme.description}</div>
                    </div>
                  </div>
                ))}
              </div>

              <button
                onClick={() => setShowAllThemes(!showAllThemes)}
                className="text-purple-600 hover:text-purple-800 text-sm font-medium"
              >
                {showAllThemes ? `Mostrar menos (${themes.length - 6} ocultos)` : `Mostrar todos los temas (${themes.length - 6} más)`}
              </button>
            </div>
          </div>

          {/* Panel derecho - Preview */}
          <div className="w-1/2 bg-gradient-to-br from-purple-400 to-purple-600 p-8 flex items-center justify-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 w-full max-w-sm">
              {/* Phone mockup */}
              <div className="bg-white/20 rounded-xl p-4">
                {/* Header */}
                <div className="bg-purple-500 rounded-t-lg h-16 mb-4 flex items-center justify-center">
                  <div className="w-16 h-4 bg-white/30 rounded"></div>
                </div>
                
                {/* Content area */}
                <div className="bg-white/30 rounded-lg h-32 mb-4 flex items-center justify-center">
                  <div className="w-20 h-8 bg-white/40 rounded"></div>
                </div>
                
                {/* Bottom section */}
                <div className="space-y-2">
                  <div className="bg-white/30 rounded h-2 w-full"></div>
                  <div className="bg-white/30 rounded h-2 w-3/4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer con botones */}
        <div className="bg-gray-50 px-8 py-4 border-t border-gray-200 flex justify-between">
          <Button
            onClick={onBack}
            variant="outline"
            className="flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Atrás
          </Button>
          <Button
            onClick={handleGeneratePosts}
            className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#2614d4] hover:to-[#c23350] text-white px-8 py-3 font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
          >
            🚀 Generar Posts
          </Button>
        </div>
      </motion.div>
    </div>
  );
};

export default Step4DesignSelection;
