/**
 * Traditional form component for buyer persona generation
 */

import { motion } from "framer-motion";
import { <PERSON>, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";
import { FormData } from "../types";
import { CountrySelector } from "./country-selector";

interface TraditionalFormProps {
  form: UseFormReturn<FormData>;
  onSubmit: (values: FormData) => void;
  isLoading: boolean;
}

export function TraditionalForm({ form, onSubmit, isLoading }: TraditionalFormProps) {
  return (
    <Card className="w-full max-w-5xl mx-auto bg-white/95 backdrop-blur-xl border-0 shadow-2xl">
      <CardHeader className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-3 text-2xl">
          <div className="p-2 bg-white/20 rounded-lg">
            <Users className="h-6 w-6" />
          </div>
          Información del Producto/Servicio
        </CardTitle>
        <p className="text-blue-100 mt-2">
          Proporciona información detallada sobre tu producto o servicio para generar buyer personas precisas
        </p>
      </CardHeader>
      <CardContent className="p-8">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Product Description */}
            <FormField
              control={form.control}
              name="product_description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-lg font-semibold text-gray-800">
                    Describe tu producto o servicio en detalle *
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Ejemplo: Plataforma de coaching fitness online para profesionales ocupados de 25-45 años. Incluye planes de entrenamiento personalizados, guía nutricional y sesiones 1:1. Precio: $99/mes. Target: profesionales que quieren mantenerse en forma pero tienen tiempo limitado..."
                      className="min-h-[150px] text-base border-2 border-[#3018ef]/20 focus:border-[#3018ef] transition-colors"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Optional Fields Row 1 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="industry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Industria (opcional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="ej. Tecnología, Salud, Educación"
                        className="border-2 border-gray-200 focus:border-[#3018ef] transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="target_market"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Mercado objetivo (opcional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="ej. Profesionales, Empresas, Consumidores"
                        className="border-2 border-gray-200 focus:border-[#3018ef] transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Optional Fields Row 2 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="business_goals"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Objetivos de negocio (opcional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="ej. Aumentar ventas, Mejorar retención"
                        className="border-2 border-gray-200 focus:border-[#3018ef] transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="competitors"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Competidores (opcional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="ej. Empresa A, Empresa B"
                        className="border-2 border-gray-200 focus:border-[#3018ef] transition-colors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Country Selection */}
            <FormField
              control={form.control}
              name="target_countries"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <CountrySelector
                      selectedCountries={field.value || []}
                      onChange={field.onChange}
                      label="Países objetivo (opcional)"
                      description="Selecciona los países donde se enfocarán los buyer personas. Esto afectará las características culturales, económicas y de comportamiento."
                      maxSelections={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Number of Personas */}
            <FormField
              control={form.control}
              name="num_personas"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium text-gray-700">
                    Número de personas a generar
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      max={5}
                      placeholder="3"
                      className="w-32 border-2 border-gray-200 focus:border-[#3018ef] transition-colors"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 3)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#3018ef]/90 hover:to-[#dd3a5a]/90 text-white font-semibold py-4 text-lg shadow-xl"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      className="mr-3"
                    >
                      <Sparkles className="h-5 w-5" />
                    </motion.div>
                    Generando Personas con IA...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-5 w-5 mr-3" />
                    Generar Buyer Personas con IA
                    <Sparkles className="h-5 w-5 ml-3" />
                  </>
                )}
              </Button>
            </motion.div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
