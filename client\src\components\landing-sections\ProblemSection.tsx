"use client"

import { useState, useEffect } from "react"
import { DollarSign, Clock, TrendingDown, Users, AlertTriangle, XCircle } from "lucide-react"

interface PainPoint {
  icon: React.ReactNode
  title: string
  description: string
  cost: string
  timeWaste: string
  color: string
}

const painPoints: PainPoint[] = [
  {
    icon: <DollarSign className="w-8 h-8" />,
    title: "Costos Exorbitantes",
    description: "Retainers de $5,000-$15,000/mes + costos ocultos por revisiones y trabajos urgentes",
    cost: "$5,000-$15,000/mes",
    timeWaste: "6-12 meses de contrato",
    color: "from-[#dd3a5a] to-[#c73351]"
  },
  {
    icon: <Clock className="w-8 h-8" />,
    title: "Tiempos Eternos",
    description: "2-4 semanas para campañas simples, horas de juntas y múltiples rondas de revisiones",
    cost: "Oportunidades perdidas",
    timeWaste: "2-4 semanas por proyecto",
    color: "from-[#3018ef] to-[#2614d4]"
  },
  {
    icon: <TrendingDown className="w-8 h-8" />,
    title: "Calidad Inconsistente",
    description: "Tu cuenta manejada por junior staff con enfoques genéricos y disponibilidad limitada",
    cost: "ROI reducido",
    timeWaste: "Resultados mediocres",
    color: "from-[#dd3a5a] to-[#3018ef]"
  },
  {
    icon: <Users className="w-8 h-8" />,
    title: "Escalabilidad Limitada",
    description: "No pueden manejar crecimiento súbito, gaps de habilidades y restricciones geográficas",
    cost: "Crecimiento frenado",
    timeWaste: "Oportunidades perdidas",
    color: "from-[#3018ef] to-[#dd3a5a]"
  }
]

export function ProblemSection() {
  const [isVisible, setIsVisible] = useState(false)
  const [activeCard, setActiveCard] = useState<number | null>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const section = document.getElementById('problem-section')
    if (section) observer.observe(section)

    return () => observer.disconnect()
  }, [])

  return (
    <section
      id="problem-section"
      className="py-32 sm:py-40 bg-white relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <div
            className={`transition-all duration-700 ease-out ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-red-100 text-red-700 mb-6">
              <AlertTriangle className="w-4 h-4 mr-2" />
              Problemas de las Agencias Tradicionales
            </div>
            
            <h2 className="text-3xl sm:text-4xl font-black mb-6 bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] inline-block">
              ¿Cansado de pagar{" "}
              <span className="text-[#dd3a5a]">$5,000/mes</span>{" "}
              por resultados mediocres?
            </h2>
            
            <p className="text-lg sm:text-xl font-bold leading-relaxed bg-white px-6 py-4 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] max-w-3xl mx-auto">
              Las agencias tradicionales están obsoletas. Costos altos, tiempos largos,
              calidad inconsistente y escalabilidad limitada.
            </p>
          </div>
        </div>

        {/* Pain Points Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {painPoints.map((point, index) => (
            <div
              key={index}
              className={`bg-white p-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] transition-all duration-500 hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:-translate-y-1 ${
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
              }`}
              style={{
                transitionDelay: `${index * 100 + 200}ms`,
              }}
            >
              {/* Content */}
              <h3 className="text-xl font-black text-gray-900 mb-4">
                {point.title}
              </h3>

              <p className="text-sm text-gray-600 mb-6 leading-relaxed">
                {point.description}
              </p>

              {/* Cost & Time */}
              <div className="space-y-2">
                <div className="text-sm font-bold text-[#dd3a5a]">
                  {point.cost}
                </div>
                <div className="text-sm text-gray-500">
                  {point.timeWaste}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div
          className={`text-center transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '600ms' }}
        >
          <div className="max-w-4xl mx-auto bg-white p-8 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
            <h3 className="text-2xl font-black text-gray-900 mb-4">
              Es hora de un cambio radical
            </h3>

            <p className="text-lg text-gray-600 mb-8">
              ¿Por qué seguir pagando fortunas por servicios lentos e inconsistentes
              cuando existe una mejor alternativa?
            </p>

            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-[#dd3a5a]/10 p-4 rounded-3xl border border-white/30 shadow-xl backdrop-blur-sm">
                <div className="text-2xl font-black text-[#dd3a5a] mb-2">$60,000+</div>
                <div className="text-sm text-gray-600">Costo anual promedio</div>
              </div>
              <div className="bg-[#3018ef]/10 p-4 rounded-3xl border border-white/30 shadow-xl backdrop-blur-sm">
                <div className="text-2xl font-black text-[#3018ef] mb-2">3-4 semanas</div>
                <div className="text-sm text-gray-600">Por campaña simple</div>
              </div>
              <div className="bg-gradient-to-r from-[#dd3a5a]/10 to-[#3018ef]/10 p-4 rounded-3xl border border-white/30 shadow-xl backdrop-blur-sm">
                <div className="text-2xl font-black bg-gradient-to-r from-[#dd3a5a] to-[#3018ef] bg-clip-text text-transparent mb-2">Horario limitado</div>
                <div className="text-sm text-gray-600">Solo en horas de oficina</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
