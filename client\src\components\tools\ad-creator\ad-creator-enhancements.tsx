/**
 * Ad Creator Enhancements - AI-powered prompt and style enhancements
 */

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Wand2, Palette, Sparkles } from 'lucide-react';

interface AdCreatorEnhancementsProps {
  platform: string;
  currentPrompt: string;
  onPromptEnhance: (enhancedPrompt: string) => void;
  onStyleApply: (style: string) => void;
}

export function AdCreatorEnhancements({
  platform,
  currentPrompt,
  onPromptEnhance,
  onStyleApply
}: AdCreatorEnhancementsProps) {
  const [isEnhancing, setIsEnhancing] = useState(false);

  const styles = [
    { name: "Moderno", color: "bg-blue-100 text-blue-800" },
    { name: "Minimalista", color: "bg-gray-100 text-gray-800" },
    { name: "Vibrant<PERSON>", color: "bg-pink-100 text-pink-800" },
    { name: "Profesional", color: "bg-green-100 text-green-800" },
    { name: "<PERSON><PERSON>", color: "bg-orange-100 text-orange-800" },
    { name: "Elegante", color: "bg-purple-100 text-purple-800" }
  ];

  const handleEnhancePrompt = async () => {
    if (!currentPrompt.trim()) return;

    setIsEnhancing(true);

    try {
      const response = await fetch('/api/v1/content/enhance-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'dev-api-key-for-testing'
        },
        body: JSON.stringify({
          prompt: currentPrompt,
          platform: platform,
          type: 'advertisement',
          enhancement_type: 'optimization'
        })
      });

      if (response.ok) {
        const result = await response.json();
        const enhancedPrompt = result.enhanced_prompt || result.content || result.text;
        if (enhancedPrompt && enhancedPrompt !== currentPrompt) {
          onPromptEnhance(enhancedPrompt);
          setIsEnhancing(false);
          return;
        }
      }
    } catch (error) {
      console.warn('Failed to enhance prompt with AI, using fallback:', error);
    }

    // Fallback enhancement if AI fails
    const enhancements = {
      facebook: "con diseño optimizado para Facebook, colores llamativos y call-to-action efectivo",
      instagram: "con estética Instagram, colores trending y elementos visuales atractivos",
      google: "con diseño profesional para Google Ads, mensaje claro y branding visible",
      linkedin: "con estilo corporativo, diseño profesional y mensaje de valor empresarial",
      youtube: "con thumbnail llamativo, texto grande y elementos que capten atención",
      display: "con diseño web-optimizado, carga rápida y branding claro"
    };

    const enhancement = enhancements[platform as keyof typeof enhancements] ||
      "con diseño profesional y atractivo";

    const enhancedPrompt = `${currentPrompt} ${enhancement}`;
    onPromptEnhance(enhancedPrompt);
    setIsEnhancing(false);
  };

  return (
    <div className="space-y-4">
      {/* Prompt Enhancement */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Wand2 className="w-4 h-4 text-[#3018ef]" />
              <span className="text-sm font-medium">Mejorar Descripción</span>
            </div>
            
            <p className="text-xs text-gray-600">
              Optimiza tu descripción con IA para mejores resultados
            </p>
            
            <Button
              onClick={handleEnhancePrompt}
              disabled={!currentPrompt.trim() || isEnhancing}
              className="w-full text-xs h-8"
              variant="outline"
            >
              {isEnhancing ? (
                <>
                  <Sparkles className="w-3 h-3 mr-1 animate-spin" />
                  Mejorando...
                </>
              ) : (
                <>
                  <Wand2 className="w-3 h-3 mr-1" />
                  Mejorar con IA
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Style Selection */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Palette className="w-4 h-4 text-[#3018ef]" />
              <span className="text-sm font-medium">Estilos Rápidos</span>
            </div>
            
            <p className="text-xs text-gray-600">
              Aplica estilos predefinidos a tu anuncio
            </p>
            
            <div className="grid grid-cols-2 gap-2">
              {styles.map((style) => (
                <Badge
                  key={style.name}
                  className={`${style.color} cursor-pointer hover:opacity-80 justify-center py-1`}
                  onClick={() => onStyleApply(style.name)}
                >
                  {style.name}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Tips */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Sparkles className="w-4 h-4 text-[#3018ef]" />
              <span className="text-sm font-medium">Tips para {platform}</span>
            </div>
            
            <div className="text-xs text-gray-600 space-y-1">
              {platform === 'facebook' && (
                <>
                  <p>• Usa texto claro y directo</p>
                  <p>• Incluye call-to-action visible</p>
                  <p>• Colores contrastantes funcionan mejor</p>
                </>
              )}
              {platform === 'instagram' && (
                <>
                  <p>• Estética visual es clave</p>
                  <p>• Usa colores trending</p>
                  <p>• Considera hashtags relevantes</p>
                </>
              )}
              {platform === 'google' && (
                <>
                  <p>• Mensaje claro y directo</p>
                  <p>• Branding visible</p>
                  <p>• Call-to-action prominente</p>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
