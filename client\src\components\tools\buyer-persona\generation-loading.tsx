"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface GenerationLoadingProps {
  currentStage: number;
  progressValue: number;
  progressMessages: string[];
  onCancel: () => void;
}

export function GenerationLoading({
  currentStage,
  progressValue,
  progressMessages,
  onCancel
}: GenerationLoadingProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="w-full max-w-3xl mx-auto"
    >
      <Card className="bg-white/95 backdrop-blur-xl border-0 shadow-2xl">
        <CardContent className="p-12 text-center">
          <motion.div
            className="mx-auto mb-8 text-8xl"
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 10, -10, 0],
              filter: ["hue-rotate(0deg)", "hue-rotate(360deg)"]
            }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            🤖
          </motion.div>

          <div className="space-y-4 mb-8">
            <h3 className="text-3xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
              IA Generando tus Buyer Personas
            </h3>

            <p className="text-lg font-medium bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
              El Marketing Ya Cambió
            </p>

            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 px-4 py-2 rounded-full border border-[#3018ef]/20">
              <div className="w-2 h-2 bg-[#3018ef] rounded-full animate-pulse"></div>
              <span className="text-[#3018ef] font-medium">Procesamiento Avanzado con IA</span>
            </div>
          </div>

          <motion.p
            key={currentStage}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-xl text-gray-700 mb-6 font-medium"
          >
            {progressMessages[currentStage]}
          </motion.p>

          <div className="space-y-4 mb-8">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-700">Progreso</span>
              <span className="text-lg font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                {progressValue}%
              </span>
            </div>
            <div className="relative">
              <Progress value={progressValue} className="h-4 bg-gray-200" />
              <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full opacity-20 animate-pulse"></div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 border border-[#3018ef]/10 p-6 rounded-xl">
              <p className="text-gray-700 font-medium">
                🧠 Analizando información y creando perfiles detallados
              </p>
              <p className="text-gray-600 text-sm mt-2">
                Nuestra IA está procesando miles de puntos de datos para crear buyer personas precisas
              </p>
            </div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                onClick={onCancel}
                variant="outline"
                className="text-red-600 border-red-300 hover:bg-red-50 hover:border-red-400 px-6 py-2"
              >
                Cancelar Generación
              </Button>
            </motion.div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
