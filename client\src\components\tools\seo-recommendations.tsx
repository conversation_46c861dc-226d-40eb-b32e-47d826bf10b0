"use client";

import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  AlertCircle,
  CheckCircle,
  Code,
  Link2,
  Image as ImageIcon,
  Layout,
  ExternalLink,
  Cpu,
  RefreshCw,
} from "lucide-react";
import { motion } from "framer-motion";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

// Definimos tipos para las recomendaciones
interface Recommendation {
  category: string;
  issue: string;
  importance: string;
  recommendation: string;
  detailed_solution?: string;
}

interface RecommendationProps {
  recommendation: Recommendation;
  onRequestDetails: (rec: Recommendation) => void;
  isExpanded: boolean;
  isLoading: boolean;
}

// Mapa de iconos por categoría
const CATEGORY_ICONS: Record<string, React.ReactNode> = {
  "Meta Tags": <Code className="h-5 w-5" />,
  Estructura: <Layout className="h-5 w-5" />,
  Enlaces: <Link2 className="h-5 w-5" />,
  Técnico: <Cpu className="h-5 w-5" />,
  Rendimiento: <RefreshCw className="h-5 w-5" />,
  Contenido: <Layout className="h-5 w-5" />,
  "Accesibilidad y SEO": <CheckCircle className="h-5 w-5" />,
  "Social Media": <ExternalLink className="h-5 w-5" />,
  Mobile: <CheckCircle className="h-5 w-5" />,
  Seguridad: <AlertCircle className="h-5 w-5" />,
  Imágenes: <ImageIcon className="h-5 w-5" />,
};

// Mapa de colores por importancia
const IMPORTANCE_COLORS: Record<string, string> = {
  crítica: "bg-red-500 hover:bg-red-600 text-white",
  alta: "bg-orange-500 hover:bg-orange-600 text-white",
  media: "bg-yellow-500 hover:bg-yellow-600 text-white",
  baja: "bg-blue-500 hover:bg-blue-600 text-white",
};

// Mapa de background colors para las cards
const IMPORTANCE_BG_COLORS: Record<string, string> = {
  crítica: "bg-red-50 dark:bg-red-950",
  alta: "bg-orange-50 dark:bg-orange-950",
  media: "bg-yellow-50 dark:bg-yellow-950",
  baja: "bg-blue-50 dark:bg-blue-950",
};

// Componente para una recomendación individual
const RecommendationCard: React.FC<RecommendationProps> = ({
  recommendation,
  onRequestDetails,
  isExpanded,
  isLoading,
}) => {
  const icon = CATEGORY_ICONS[recommendation.category] || (
    <AlertCircle className="h-5 w-5" />
  );
  const bgColor = IMPORTANCE_BG_COLORS[recommendation.importance] || "";
  const borderColor =
    recommendation.importance === "crítica"
      ? "border-red-200 dark:border-red-800"
      : recommendation.importance === "alta"
        ? "border-orange-200 dark:border-orange-800"
        : recommendation.importance === "media"
          ? "border-yellow-200 dark:border-yellow-800"
          : "border-blue-200 dark:border-blue-800";

  return (
    <Card
      className={`mb-6 shadow-md overflow-hidden transition-all border ${borderColor} ${bgColor}`}
    >
      <div className="relative">
        {/* Indicador de importancia */}
        <div className="absolute top-0 right-0">
          <span
            className={`mr-4 mt-4 py-1 px-2 inline-flex items-center rounded-full border text-xs font-semibold ${IMPORTANCE_COLORS[recommendation.importance]}`}
          >
            {recommendation.importance}
          </span>
        </div>

        <CardHeader className="pb-2 pt-6">
          <div className="flex items-start gap-4">
            <div
              className={`p-2.5 rounded-full ${
                recommendation.importance === "crítica"
                  ? "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300"
                  : recommendation.importance === "alta"
                    ? "bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300"
                    : recommendation.importance === "media"
                      ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300"
                      : "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"
              } flex items-center justify-center shrink-0`}
            >
              {icon}
            </div>
            <div>
              <CardTitle className="text-lg font-semibold">
                {recommendation.issue}
              </CardTitle>
              <CardDescription className="text-sm mt-1 font-medium">
                {recommendation.category}
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pb-4 text-sm">
          <div className="ml-16">
            <p className="text-base">{recommendation.recommendation}</p>

            {isExpanded && recommendation.detailed_solution && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                transition={{ duration: 0.3 }}
                className="mt-4"
              >
                <Separator className="my-4" />
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
                  <h4 className="text-base font-semibold mb-3 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2"
                    >
                      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                    </svg>
                    Solución detallada
                  </h4>
                  <div className="prose prose-sm max-w-full dark:prose-invert">
                    <div className="whitespace-pre-line text-base leading-relaxed">
                      {recommendation.detailed_solution}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </CardContent>

        <CardFooter className="pb-6 pt-0">
          <div className="w-full ml-16">
            {!recommendation.detailed_solution && !isLoading ? (
              <Button
                variant="outline"
                className="w-full bg-white dark:bg-gray-800 border-blue-200 dark:border-blue-900 hover:bg-blue-50 dark:hover:bg-blue-950 text-blue-600 dark:text-blue-400"
                onClick={() => onRequestDetails(recommendation)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2"
                >
                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                </svg>
                Generar solución detallada con IA
              </Button>
            ) : isLoading ? (
              <Button variant="outline" className="w-full" disabled>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="mr-2"
                >
                  <RefreshCw className="h-5 w-5" />
                </motion.div>
                Generando solución con IA...
              </Button>
            ) : null}
          </div>
        </CardFooter>
      </div>
    </Card>
  );
};

// Componente principal de recomendaciones SEO
interface SEORecommendationsProps {
  recommendations: Recommendation[];
  url: string;
}

// Función para generar soluciones específicas basadas en el problema
const generateSpecificSolution = (rec: Recommendation): string => {
  const issue = rec.issue.toLowerCase();
  const category = rec.category.toLowerCase();

  // Soluciones específicas para títulos
  if (issue.includes('título') && issue.includes('largo')) {
    const currentLength = issue.match(/\((\d+) caracteres\)/)?.[1] || 'desconocido';
    return `
      <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
        <h4 class="font-bold text-red-800 mb-2">🚨 Problema Crítico: Título Demasiado Largo</h4>
        <p class="text-red-700">Tu título actual tiene <strong>${currentLength} caracteres</strong>. Google corta títulos después de 60 caracteres, por lo que parte de tu título no se ve en los resultados de búsqueda.</p>
      </div>

      <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4">
        <h4 class="font-bold text-green-800 mb-2">✅ Solución Específica</h4>
        <ol class="text-green-700 space-y-2">
          <li><strong>1. Identifica palabras clave principales:</strong> ¿Cuáles son las 2-3 palabras más importantes de tu título?</li>
          <li><strong>2. Reescribe manteniendo lo esencial:</strong> Pon las palabras clave al inicio y elimina palabras innecesarias como "el", "de", "para"</li>
          <li><strong>3. Mantén 50-60 caracteres:</strong> Usa un contador de caracteres mientras escribes</li>
          <li><strong>4. Incluye tu marca al final:</strong> Si tienes espacio, agrega " | Tu Marca"</li>
        </ol>
      </div>

      <div class="bg-blue-50 border border-blue-200 p-4 mb-4">
        <h4 class="font-bold text-blue-800 mb-2">📝 Ejemplo Práctico</h4>
        <div class="space-y-2">
          <div>
            <span class="text-red-600 font-medium">❌ Antes (${currentLength} chars):</span>
            <code class="block bg-red-100 p-2 mt-1 text-sm">Tu título actual muy largo que se corta en Google...</code>
          </div>
          <div>
            <span class="text-green-600 font-medium">✅ Después (55 chars):</span>
            <code class="block bg-green-100 p-2 mt-1 text-sm">Título Optimizado con Keywords | Tu Marca</code>
          </div>
        </div>
      </div>

      <div class="bg-yellow-50 border border-yellow-200 p-4">
        <h4 class="font-bold text-yellow-800 mb-2">🎯 Impacto Esperado</h4>
        <ul class="text-yellow-700 space-y-1">
          <li>• <strong>+15-25% CTR:</strong> Títulos completos reciben más clics</li>
          <li>• <strong>Mejor ranking:</strong> Google entiende mejor tu contenido</li>
          <li>• <strong>Más profesional:</strong> Tu sitio se ve más confiable</li>
        </ul>
      </div>`;
  }

  // Soluciones específicas para meta descripción
  if (issue.includes('meta descripción') && issue.includes('faltante')) {
    return `
      <div class="bg-orange-50 border-l-4 border-orange-500 p-4 mb-4">
        <h4 class="font-bold text-orange-800 mb-2">⚠️ Oportunidad Perdida: Sin Meta Descripción</h4>
        <p class="text-orange-700">Google está generando automáticamente el snippet de tu página, pero tú puedes controlarlo para atraer más clics.</p>
      </div>

      <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4">
        <h4 class="font-bold text-green-800 mb-2">✅ Cómo Crear una Meta Descripción Perfecta</h4>
        <ol class="text-green-700 space-y-2">
          <li><strong>1. Longitud ideal:</strong> 140-160 caracteres (usa un contador)</li>
          <li><strong>2. Incluye tu palabra clave principal:</strong> La que usarías para buscar tu página</li>
          <li><strong>3. Agrega una llamada a la acción:</strong> "Descubre", "Aprende", "Obtén", "Solicita"</li>
          <li><strong>4. Menciona un beneficio específico:</strong> ¿Qué gana el usuario al visitar tu página?</li>
        </ol>
      </div>

      <div class="bg-blue-50 border border-blue-200 p-4 mb-4">
        <h4 class="font-bold text-blue-800 mb-2">📝 Plantilla y Ejemplo</h4>
        <div class="space-y-3">
          <div>
            <span class="font-medium text-blue-800">Plantilla:</span>
            <code class="block bg-blue-100 p-2 mt-1 text-sm">[Acción] [Beneficio específico] [Palabra clave]. [Llamada a la acción] [Incentivo].</code>
          </div>
          <div>
            <span class="font-medium text-green-600">Ejemplo (156 chars):</span>
            <code class="block bg-green-100 p-2 mt-1 text-sm">Descubre los mejores banquetes para tu evento especial. Menús personalizados y servicio premium. ¡Solicita cotización gratuita hoy!</code>
          </div>
          <div>
            <span class="font-medium text-blue-800">Código HTML:</span>
            <code class="block bg-gray-100 p-2 mt-1 text-sm">&lt;meta name="description" content="Tu meta descripción aquí"&gt;</code>
          </div>
        </div>
      </div>

      <div class="bg-yellow-50 border border-yellow-200 p-4">
        <h4 class="font-bold text-yellow-800 mb-2">🎯 Impacto Esperado</h4>
        <ul class="text-yellow-700 space-y-1">
          <li>• <strong>+20-30% CTR:</strong> Descripciones atractivas generan más clics</li>
          <li>• <strong>Control total:</strong> Tú decides qué ve el usuario en Google</li>
          <li>• <strong>Mejor conversión:</strong> Atraes visitantes más interesados</li>
        </ul>
      </div>`;
  }

  // Soluciones específicas para imágenes sin alt
  if (issue.includes('imágenes sin texto alternativo') || issue.includes('sin alt')) {
    const imageCount = issue.match(/(\d+) de \d+ imágenes/)?.[1] || issue.match(/(\d+) imágenes/)?.[1] || 'varias';
    return `
      <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
        <h4 class="font-bold text-red-800 mb-2">🖼️ Problema: ${imageCount} Imágenes Sin Alt Text</h4>
        <p class="text-red-700">Google no puede "ver" estas imágenes, perdiendo oportunidades de ranking en búsqueda de imágenes y afectando la accesibilidad.</p>
      </div>

      <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4">
        <h4 class="font-bold text-green-800 mb-2">✅ Cómo Escribir Alt Text Perfecto</h4>
        <ol class="text-green-700 space-y-2">
          <li><strong>1. Describe lo que ves:</strong> ¿Qué muestra exactamente la imagen?</li>
          <li><strong>2. Incluye contexto:</strong> ¿Cómo se relaciona con el contenido?</li>
          <li><strong>3. Usa palabras clave naturalmente:</strong> Si es relevante para el tema</li>
          <li><strong>4. Mantén 10-15 palabras:</strong> Descriptivo pero conciso</li>
          <li><strong>5. Evita "imagen de" o "foto de":</strong> Es redundante</li>
        </ol>
      </div>

      <div class="bg-blue-50 border border-blue-200 p-4 mb-4">
        <h4 class="font-bold text-blue-800 mb-2">📝 Ejemplos Específicos</h4>
        <div class="space-y-3">
          <div>
            <span class="text-red-600 font-medium">❌ Malo:</span>
            <code class="block bg-red-100 p-2 mt-1 text-sm">alt="imagen1" o alt=""</code>
          </div>
          <div>
            <span class="text-yellow-600 font-medium">⚠️ Regular:</span>
            <code class="block bg-yellow-100 p-2 mt-1 text-sm">alt="banquete"</code>
          </div>
          <div>
            <span class="text-green-600 font-medium">✅ Excelente:</span>
            <code class="block bg-green-100 p-2 mt-1 text-sm">alt="Mesa elegante preparada para banquete de boda con centros florales"</code>
          </div>
        </div>
      </div>

      <div class="bg-purple-50 border border-purple-200 p-4 mb-4">
        <h4 class="font-bold text-purple-800 mb-2">🔧 Herramienta de Emma</h4>
        <p class="text-purple-700">Emma detectó exactamente cuáles imágenes necesitan alt text. Ve a la pestaña "Imágenes Problemáticas" para ver capturas específicas y código HTML listo para copiar.</p>
      </div>

      <div class="bg-yellow-50 border border-yellow-200 p-4">
        <h4 class="font-bold text-yellow-800 mb-2">🎯 Impacto Esperado</h4>
        <ul class="text-yellow-700 space-y-1">
          <li>• <strong>+15-25% tráfico de imágenes:</strong> Mejor ranking en Google Imágenes</li>
          <li>• <strong>Mejor accesibilidad:</strong> Usuarios con discapacidades visuales</li>
          <li>• <strong>SEO general mejorado:</strong> Google entiende mejor tu contenido</li>
        </ul>
      </div>`;
  }

  // Solución genérica mejorada para otros casos
  return `
    <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
      <h4 class="font-bold text-blue-800 mb-2">🔍 Problema Detectado: ${rec.issue}</h4>
      <p class="text-blue-700">Este problema puede afectar tu posicionamiento en Google y la experiencia de tus usuarios.</p>
    </div>

    <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4">
      <h4 class="font-bold text-green-800 mb-2">✅ Solución Recomendada</h4>
      <p class="text-green-700 mb-2"><strong>Categoría:</strong> ${rec.category}</p>
      <p class="text-green-700"><strong>Acción:</strong> ${rec.recommendation}</p>
    </div>

    <div class="bg-yellow-50 border border-yellow-200 p-4">
      <h4 class="font-bold text-yellow-800 mb-2">📞 ¿Necesitas Ayuda?</h4>
      <p class="text-yellow-700">Si no estás seguro de cómo implementar esta mejora, Emma puede ayudarte con análisis más detallados. Vuelve a analizar tu sitio después de hacer cambios para verificar las mejoras.</p>
    </div>`;
};

export default function SEORecommendations({
  recommendations: initialRecommendations,
  url,
}: SEORecommendationsProps) {
  // Ahora usamos un estado local para las recomendaciones que podemos modificar
  const [recommendations, setRecommendations] = useState<Recommendation[]>(
    initialRecommendations,
  );
  const [expandedRecs, setExpandedRecs] = useState<string[]>([]);
  const [loadingRec, setLoadingRec] = useState<string | null>(null);

  // Función para solicitar más detalles sobre una recomendación
  const requestDetailedRecommendation = async (rec: Recommendation) => {
    setLoadingRec(rec.issue);
    console.log("Solicitando detalles para:", rec.issue);

    try {
      // Simulación de la solución detallada por problemas con la API de Emma AI
      // Esta es una solución temporal para que funcione el botón mientras se resuelve el problema de la API
      const detailedSolutions: Record<string, string> = {
        "Falta una meta descripción": `<p><strong>Problema:</strong> La meta descripción es tu oportunidad de "vender" tu página en los resultados de Google. Sin ella, Google genera automáticamente un snippet que puede no ser atractivo.</p>

        <p><strong>Impacto:</strong> Una buena meta descripción puede aumentar tu CTR (Click Through Rate) hasta un 30%, lo que indirectamente mejora tu ranking.</p>

        <p><strong>Solución paso a paso:</strong></p>
        <ul>
          <li><strong>Longitud óptima:</strong> 150-160 caracteres (Emma SEO te mostrará el conteo exacto).</li>
          <li><strong>Incluye palabras clave:</strong> Usa las palabras clave principales que Emma SEO identificó en tu análisis.</li>
          <li><strong>Agrega llamada a la acción:</strong> Palabras como "Descubre", "Aprende", "Solicita", "Obtén" aumentan clics.</li>
          <li><strong>Hazla única:</strong> Cada página debe tener una meta descripción diferente y específica.</li>
        </ul>

        <p><strong>Ejemplo optimizado:</strong></p>
        <pre>&lt;meta name="description" content="Descubre nuestros servicios de diseño web profesional optimizados para SEO. Creamos sitios responsive que convierten visitantes en clientes. ¡Solicita presupuesto gratis hoy!"&gt;</pre>

        <p><strong>Verificación:</strong> Después de implementar, usa Emma SEO para verificar que la meta descripción se detecta correctamente y programa análisis regulares para mantener todas tus páginas optimizadas.</p>`,

        "El título de la página es demasiado largo": `<p><strong>Problema:</strong> Los títulos demasiado largos (>60 caracteres) se truncan en resultados de búsqueda, reduciendo su efectividad.</p>
        <ul>
          <li>Reduce el título a 50-60 caracteres manteniendo las palabras clave importantes al inicio.</li>
          <li>Asegúrate que comunique claramente el contenido de la página.</li>
          <li>Mantén un formato consistente en todo el sitio (Marca | Palabra clave principal | Descriptor).</li>
        </ul>
        <p><strong>Ejemplo:</strong></p>
        <pre>&lt;head&gt;
  &lt;title&gt;Diseño Web Profesional | PolygonAG - Soluciones Digitales&lt;/title&gt;
&lt;/head&gt;</pre>
        <p><strong>Herramientas:</strong> SERP Simulator, Moz Title Tag Preview para visualizar cómo se verá en Google.</p>`,

        "No se ha encontrado un encabezado H1": `<p><strong>Problema:</strong> El encabezado H1 es un factor importante para SEO y ayuda a los motores de búsqueda a entender el tema principal.</p>
        <ul>
          <li>Añade exactamente un H1 por página que incluya la palabra clave principal.</li>
          <li>El H1 debe ser descriptivo y reflejar el contenido de la página.</li>
          <li>Asegúrate que sea visible para los usuarios, no solo para bots.</li>
        </ul>
        <p><strong>Ejemplo:</strong></p>
        <pre>&lt;h1 class="main-heading"&gt;Servicios de Diseño Web Profesional para Empresas&lt;/h1&gt;</pre>
        <p><strong>Herramientas:</strong> Lighthouse de Google, Web Developer Extension para verificar la estructura de encabezados.</p>`,

        "La página carga lentamente": `<p><strong>Problema:</strong> La velocidad de carga afecta directamente al SEO y a la experiencia de usuario, aumentando el abandono.</p>
        <ul>
          <li>Optimiza y comprime imágenes (WebP, lazy loading).</li>
          <li>Minimiza y combina archivos CSS/JS, implementa caching del navegador.</li>
          <li>Considera un CDN para distribuir contenido estático globalmente.</li>
        </ul>
        <p><strong>Ejemplo:</strong></p>
        <pre>&lt;img src="imagen.webp" loading="lazy" alt="Descripción optimizada" width="800" height="600"&gt;

&lt;!-- Agregar en el head --&gt;
&lt;link rel="preload" href="styles.css" as="style"&gt;
&lt;link rel="preload" href="important-script.js" as="script"&gt;</pre>
        <p><strong>Herramientas:</strong> PageSpeed Insights, GTmetrix, WebPageTest para análisis detallado.</p>`,

        "Faltan atributos alt en imágenes": `<p><strong>Problema:</strong> Los atributos alt son esenciales para accesibilidad y SEO de imágenes. Google usa esta información para entender el contenido visual y mejorar tu ranking en búsqueda de imágenes.</p>
        <ul>
          <li><strong>Identifica las imágenes:</strong> Emma SEO ya detectó las imágenes específicas sin alt text en tu sitio.</li>
          <li><strong>Escribe descripciones útiles:</strong> Describe lo que muestra la imagen en 10-15 palabras, incluyendo palabras clave naturalmente.</li>
          <li><strong>Prioriza por impacto:</strong> Comienza con imágenes en páginas principales y productos/servicios importantes.</li>
          <li><strong>Evita keyword stuffing:</strong> Usa descripciones naturales que realmente ayuden a usuarios con discapacidades visuales.</li>
        </ul>
        <p><strong>Ejemplo práctico:</strong></p>
        <pre>&lt;img src="diseno-web-responsivo.jpg" alt="Diseño web responsivo para e-commerce en dispositivos múltiples" width="600" height="400"&gt;</pre>
        <p><strong>Impacto esperado:</strong> Mejora en ranking de búsqueda de imágenes (15-25%), mejor accesibilidad, y posible mejora en SEO general (5-10%).</p>
        <p><strong>Seguimiento:</strong> Vuelve a analizar tu sitio con Emma SEO en 1-2 semanas para verificar que los cambios se implementaron correctamente.</p>`,

        "Los enlaces internos son insuficientes": `<p><strong>Problema:</strong> Los enlaces internos distribuyen autoridad y ayudan a la navegación e indexación.</p>
        <ul>
          <li>Añade enlaces internos contextuales usando anchor text descriptivo.</li>
          <li>Crea una estructura lógica conectando contenido relacionado.</li>
          <li>Implementa enlaces en el contenido principal, no solo en menús.</li>
        </ul>
        <p><strong>Ejemplo:</strong></p>
        <pre>&lt;p&gt;Nuestro &lt;a href="/servicios/diseno-web" title="Servicios de diseño web profesional"&gt;servicio de diseño web&lt;/a&gt; incluye optimización SEO.&lt;/p&gt;</pre>
        <p><strong>Herramientas:</strong> Emma SEO analiza automáticamente la estructura de enlaces internos de todo tu sitio y te muestra oportunidades de mejora específicas.</p>`,

        // Nuevas soluciones mejoradas para los problemas más comunes
        "Página sin título - impacto crítico en SEO": `<div class="solution-container bg-red-50 border-l-4 border-red-500 p-4 mb-4">
          <h4 class="text-red-800 font-bold mb-2">🚨 PROBLEMA CRÍTICO - ACCIÓN INMEDIATA REQUERIDA</h4>
          <p class="text-red-700 mb-3">Tu página no tiene título. Esto es como tener una tienda sin letrero - Google no puede entender de qué trata tu página.</p>

          <div class="bg-white p-4 rounded-lg mb-4">
            <h5 class="font-semibold mb-2">✅ SOLUCIÓN PASO A PASO:</h5>
            <ol class="list-decimal list-inside space-y-2">
              <li><strong>Agrega inmediatamente:</strong> <code>&lt;title&gt;Tu Título Aquí&lt;/title&gt;</code> en la sección &lt;head&gt;</li>
              <li><strong>Longitud óptima:</strong> 50-60 caracteres (Google corta después de 60)</li>
              <li><strong>Incluye palabra clave principal:</strong> Al inicio del título</li>
              <li><strong>Hazlo único:</strong> Cada página debe tener un título diferente</li>
              <li><strong>Incluye tu marca:</strong> Al final del título</li>
            </ol>
          </div>

          <div class="bg-green-50 p-3 rounded-lg mb-4">
            <h5 class="font-semibold text-green-800 mb-2">💡 EJEMPLOS POR INDUSTRIA:</h5>
            <pre class="text-sm"><code>Restaurante: "Restaurante Italiano Auténtico en Madrid | Casa Bella"
Dentista: "Dentista en Barcelona - Citas Urgentes | Clínica Dental Pro"
Tienda: "Ropa Deportiva Premium - Envío Gratis 24h | SportMax"
Abogado: "Abogado Divorcios Madrid - Consulta Gratuita | Bufete Legal"</code></pre>
          </div>

          <div class="bg-blue-50 p-3 rounded-lg">
            <h5 class="font-semibold text-blue-800 mb-2">📈 IMPACTO ESPERADO:</h5>
            <ul class="list-disc list-inside text-blue-700">
              <li>Mejora inmediata en rankings de Google</li>
              <li>Aumento del CTR (Click Through Rate) hasta 40%</li>
              <li>Mejor comprensión del contenido por parte de buscadores</li>
              <li>Aparición correcta en resultados de búsqueda</li>
            </ul>
          </div>
        </div>`,

        "Meta descripción faltante - oportunidad perdida": `<div class="solution-container bg-orange-50 border-l-4 border-orange-500 p-4 mb-4">
          <h4 class="text-orange-800 font-bold mb-2">💸 DINERO PERDIDO CADA DÍA</h4>
          <p class="text-orange-700 mb-3">Sin meta descripción pierdes clics potenciales. Es tu "anuncio gratuito" en Google que puede aumentar visitas hasta 30%.</p>

          <div class="bg-white p-4 rounded-lg mb-4">
            <h5 class="font-semibold mb-2">✅ FÓRMULA PARA META DESCRIPCIÓN PERFECTA:</h5>
            <ol class="list-decimal list-inside space-y-2">
              <li><strong>Longitud ideal:</strong> 140-160 caracteres (Google muestra hasta 160)</li>
              <li><strong>Problema + Solución:</strong> "¿Necesitas X? Ofrecemos Y"</li>
              <li><strong>Beneficio específico:</strong> Números, porcentajes, resultados</li>
              <li><strong>Urgencia:</strong> "Limitado", "Gratis", "24h", "Inmediato"</li>
              <li><strong>Llamada a la acción:</strong> "Descubre", "Obtén", "Solicita"</li>
            </ol>
          </div>

          <div class="bg-green-50 p-3 rounded-lg mb-4">
            <h5 class="font-semibold text-green-800 mb-2">💡 PLANTILLAS PROBADAS:</h5>
            <pre class="text-sm"><code>Servicio: "¿Necesitas [servicio]? Nuestros expertos te ayudan a [beneficio]. +1000 clientes satisfechos. ¡Consulta gratis!"

Producto: "Compra [producto] con [beneficio único]. Envío gratis en 24h. Garantía 2 años. ¡Oferta limitada hasta agotar stock!"

Blog: "Aprende [tema] en [tiempo]. Guía completa con 15 consejos prácticos. Descarga gratis + checklist exclusivo."

Local: "[Servicio] en [ciudad]. Atención 24/7, presupuesto sin compromiso. Más de 500 trabajos realizados. ¡Llama ya!"</code></pre>
          </div>

          <div class="bg-blue-50 p-3 rounded-lg">
            <h5 class="font-semibold text-blue-800 mb-2">🎯 CÓDIGO PARA IMPLEMENTAR:</h5>
            <pre class="text-sm"><code>&lt;meta name="description" content="Tu meta descripción optimizada aquí - incluye beneficio clave y llamada a la acción"&gt;</code></pre>
          </div>
        </div>`,

        "Falta etiqueta H1 - estructura deficiente": `<div class="solution-container bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-4">
          <h4 class="text-yellow-800 font-bold mb-2">🏗️ ESTRUCTURA DEFICIENTE</h4>
          <p class="text-yellow-700 mb-3">Sin H1, tu página es como un libro sin título de capítulo. Google no puede identificar el tema principal.</p>

          <div class="bg-white p-4 rounded-lg mb-4">
            <h5 class="font-semibold mb-2">✅ CÓMO CREAR EL H1 PERFECTO:</h5>
            <ol class="list-decimal list-inside space-y-2">
              <li><strong>Una sola etiqueta H1:</strong> Solo una por página (muy importante)</li>
              <li><strong>Palabra clave principal:</strong> Inclúyela de forma natural</li>
              <li><strong>Descriptivo y claro:</strong> Debe explicar el contenido principal</li>
              <li><strong>Diferente al título:</strong> Puede ser más largo y descriptivo</li>
              <li><strong>Visible para usuarios:</strong> No solo para bots</li>
            </ol>
          </div>

          <div class="bg-green-50 p-3 rounded-lg mb-4">
            <h5 class="font-semibold text-green-800 mb-2">💡 ESTRUCTURA JERÁRQUICA CORRECTA:</h5>
            <pre class="text-sm"><code>&lt;h1&gt;Título Principal - Solo UNO por página&lt;/h1&gt;
  &lt;h2&gt;Sección Principal 1&lt;/h2&gt;
    &lt;h3&gt;Subsección 1.1&lt;/h3&gt;
    &lt;h3&gt;Subsección 1.2&lt;/h3&gt;
  &lt;h2&gt;Sección Principal 2&lt;/h2&gt;
    &lt;h3&gt;Subsección 2.1&lt;/h3&gt;
      &lt;h4&gt;Detalle específico&lt;/h4&gt;</code></pre>
          </div>

          <div class="bg-blue-50 p-3 rounded-lg">
            <h5 class="font-semibold text-blue-800 mb-2">🎯 EJEMPLOS ANTES VS DESPUÉS:</h5>
            <pre class="text-sm"><code>❌ ANTES: &lt;h1&gt;Bienvenido&lt;/h1&gt; (genérico, sin valor SEO)

✅ DESPUÉS:
&lt;h1&gt;Abogado Especialista en Divorcios Madrid - Consulta Gratuita&lt;/h1&gt;
&lt;h1&gt;Curso de Inglés Online - Aprende en 3 Meses Garantizado&lt;/h1&gt;
&lt;h1&gt;Reparación iPhone Barcelona - Servicio en 30 Minutos&lt;/h1&gt;</code></pre>
          </div>
        </div>`,
      };

      // Generar solución específica basada en el problema real
      const detailedSolution = detailedSolutions[rec.issue] || generateSpecificSolution(rec);

      console.log("Solución detallada generada para:", rec.issue);

      // Actualizar la recomendación con la solución detallada
      const updatedRecs = recommendations.map((r) =>
        r.issue === rec.issue
          ? { ...r, detailed_solution: detailedSolution }
          : r,
      );

      // Actualizar el estado de las recomendaciones
      setRecommendations(updatedRecs);

      // Marcar como expandida
      setExpandedRecs([...expandedRecs, rec.issue]);
    } catch (error) {
      console.error("Error al obtener recomendación detallada:", error);
    } finally {
      setLoadingRec(null);
    }
  };

  // Agrupar recomendaciones por categoría
  const recommendationsByCategory = recommendations.reduce(
    (acc, rec) => {
      if (!acc[rec.category]) {
        acc[rec.category] = [];
      }
      acc[rec.category].push(rec);
      return acc;
    },
    {} as Record<string, Recommendation[]>,
  );

  // Agrupar por importancia
  const recommendationsByImportance = recommendations.reduce(
    (acc, rec) => {
      if (!acc[rec.importance]) {
        acc[rec.importance] = [];
      }
      acc[rec.importance].push(rec);
      return acc;
    },
    {} as Record<string, Recommendation[]>,
  );

  // Ordenar las categorías e importancias para mostrar
  const sortedCategories = Object.keys(recommendationsByCategory).sort();
  const importanceOrder = ["crítica", "alta", "media", "baja"];

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 p-6 rounded-xl border border-blue-100 dark:border-blue-900 shadow-sm">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h3 className="text-xl font-semibold flex items-center text-blue-800 dark:text-blue-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="22"
                height="22"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M19.439 7.85c-.049.322.059.648.289.878l1.568 1.568c.47.47.706 1.087.706 1.704s-.235 1.233-.706 1.704l-1.611 1.611a.98.98 0 0 1-.837.276c-.47-.07-.802-.48-.743-.95l.329-2.588a1.5 1.5 0 0 0-1.616-1.616l-2.588.329c-.47.059-.88-.273-.95-.743a.98.98 0 0 1 .276-.837l1.611-1.611a2.5 2.5 0 0 1 1.704-.706 2.5 2.5 0 0 1 1.704.706l1.568 1.568c.23.23.556.338.878.289z"></path>
                <path d="M7.967 16.150c.049-.322-.059-.648-.289-.878l-1.568-1.568c-.47-.47-.706-1.087-.706-1.704s.235-1.233.706-1.704l1.611-1.611a.98.98 0 0 1 .837-.276c.47.07.802.48.743.95l-.329 2.588a1.5 1.5 0 0 0 1.616 1.616l2.588-.329c.47-.059.88.273.95.743a.98.98 0 0 1-.276.837l-1.611 1.611a2.5 2.5 0 0 1-1.704.706 2.5 2.5 0 0 1-1.704-.706l-1.568-1.568c-.23-.23-.556-.338-.878-.289z"></path>
              </svg>
              Recomendaciones de optimización SEO
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-400 mt-1">
              Hemos analizado tu sitio y encontrado las siguientes oportunidades
              de mejora
            </p>
          </div>

          <div className="flex items-center gap-2">
            <div className="bg-white dark:bg-gray-800 p-2 rounded-lg shadow-sm border border-blue-100 dark:border-blue-900">
              <div className="flex items-center gap-2">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {recommendations.length}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 leading-tight">
                  {recommendations.length === 1 ? "Problema" : "Problemas"}
                  <br />
                  detectados
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-2 rounded-lg shadow-sm border border-blue-100 dark:border-blue-900">
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1">
                  <span className="h-3 w-3 rounded-full bg-red-500 inline-block"></span>
                  <span className="text-xs">
                    {recommendationsByImportance["crítica"]?.length || 0}{" "}
                    críticas
                  </span>
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <span className="h-3 w-3 rounded-full bg-orange-500 inline-block"></span>
                  <span className="text-xs">
                    {recommendationsByImportance["alta"]?.length || 0} altas
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Tabs defaultValue="importance" className="w-full">
        <div className="flex justify-between items-center mb-2">
          <TabsList className="mb-4 bg-white dark:bg-gray-900 p-1 shadow-md border border-gray-200 dark:border-gray-800">
            <TabsTrigger
              value="importance"
              className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z"></path>
              </svg>
              Por importancia
            </TabsTrigger>
            <TabsTrigger
              value="category"
              className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                <rect width="7" height="7" x="3" y="14" rx="1"></rect>
              </svg>
              Por categoría
            </TabsTrigger>
            <TabsTrigger
              value="all"
              className="data-[state=active]:bg-blue-600 data-[state=active]:text-white"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <line x1="8" x2="21" y1="6" y2="6"></line>
                <line x1="8" x2="21" y1="12" y2="12"></line>
                <line x1="8" x2="21" y1="18" y2="18"></line>
                <line x1="3" x2="3.01" y1="6" y2="6"></line>
                <line x1="3" x2="3.01" y1="12" y2="12"></line>
                <line x1="3" x2="3.01" y1="18" y2="18"></line>
              </svg>
              Todas las recomendaciones
            </TabsTrigger>
          </TabsList>

          <div className="text-sm text-blue-600 dark:text-blue-400 flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-1"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" x2="12" y1="15" y2="3"></line>
            </svg>
            Exportar reporte
          </div>
        </div>

        {/* Vista por importancia */}
        <TabsContent value="importance">
          <div className="space-y-8">
            {importanceOrder.map((importance) => {
              const recs = recommendationsByImportance[importance];
              if (!recs || recs.length === 0) return null;

              return (
                <div key={importance} className="space-y-3">
                  <h4 className="font-semibold capitalize flex items-center text-lg px-2 py-1 border-b">
                    <span
                      className={`w-4 h-4 rounded-full mr-2 ${
                        importance === "crítica"
                          ? "bg-red-500"
                          : importance === "alta"
                            ? "bg-orange-500"
                            : importance === "media"
                              ? "bg-yellow-500"
                              : "bg-blue-500"
                      }`}
                    ></span>
                    Prioridad {importance} ({recs.length})
                  </h4>
                  {recs.map((rec) => (
                    <RecommendationCard
                      key={rec.issue}
                      recommendation={rec}
                      onRequestDetails={requestDetailedRecommendation}
                      isExpanded={expandedRecs.includes(rec.issue)}
                      isLoading={loadingRec === rec.issue}
                    />
                  ))}
                </div>
              );
            })}
          </div>
        </TabsContent>

        {/* Vista por categoría */}
        <TabsContent value="category">
          <Accordion type="multiple" className="w-full">
            {sortedCategories.map((category) => (
              <AccordionItem
                key={category}
                value={category}
                className="border rounded-lg mb-4 shadow-sm bg-white dark:bg-gray-900"
              >
                <AccordionTrigger className="hover:no-underline px-4 py-3 text-base">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex items-center justify-center">
                      {CATEGORY_ICONS[category] || (
                        <AlertCircle className="h-5 w-5" />
                      )}
                    </div>
                    <div>
                      <span className="font-medium">{category}</span>
                      <Badge
                        variant="outline"
                        className="ml-2 bg-blue-50 dark:bg-blue-950 text-xs"
                      >
                        {recommendationsByCategory[category].length}
                      </Badge>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="pt-2 space-y-3">
                    {recommendationsByCategory[category].map((rec) => (
                      <RecommendationCard
                        key={rec.issue}
                        recommendation={rec}
                        onRequestDetails={requestDetailedRecommendation}
                        isExpanded={expandedRecs.includes(rec.issue)}
                        isLoading={loadingRec === rec.issue}
                      />
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </TabsContent>

        {/* Vista de todas */}
        <TabsContent value="all">
          <Card className="border-0 shadow-none bg-transparent">
            <CardContent className="p-0">
              <ScrollArea className="h-[600px] pr-4">
                <div className="space-y-4">
                  {recommendations.map((rec) => (
                    <RecommendationCard
                      key={rec.issue}
                      recommendation={rec}
                      onRequestDetails={requestDetailedRecommendation}
                      isExpanded={expandedRecs.includes(rec.issue)}
                      isLoading={loadingRec === rec.issue}
                    />
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
