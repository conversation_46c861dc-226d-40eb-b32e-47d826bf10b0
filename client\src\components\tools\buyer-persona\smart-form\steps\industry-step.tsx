"use client";

import { motion } from "framer-motion";
import { Label } from "@/components/ui/label";
import { INDUSTRIES } from '../constants';
import { SmartFormData } from '../types';

interface IndustryStepProps {
  formData: SmartFormData;
  updateFormData: (field: keyof SmartFormData, value: string) => void;
}

export function IndustryStep({ formData, updateFormData }: IndustryStepProps) {
  return (
    <div>
      <Label className="text-base font-medium text-gray-700 mb-4 block">
        Selecciona tu industria principal
      </Label>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {INDUSTRIES.map((industry) => (
          <motion.button
            key={industry.id}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => updateFormData("industry", industry.id)}
            className={`
              p-4 rounded-lg border-2 text-center transition-all
              ${formData.industry === industry.id
                ? `border-blue-500 ${industry.color} shadow-md`
                : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
              }
            `}
          >
            <div className="text-3xl mb-2">{industry.icon}</div>
            <div className="font-medium text-sm">{industry.name}</div>
          </motion.button>
        ))}
      </div>
    </div>
  );
}
