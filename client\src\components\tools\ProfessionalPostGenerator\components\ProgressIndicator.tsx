import React from "react";

interface ProgressIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  currentStep,
  totalSteps,
}) => {
  return (
    <div className="bg-gray-50 px-8 py-4 border-b border-gray-200">
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">Step {currentStep}/{totalSteps}</div>
        <div className="flex space-x-2">
          {Array.from({ length: totalSteps }, (_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full ${
                currentStep >= index + 1 ? 'bg-[#3018ef]' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProgressIndicator;
