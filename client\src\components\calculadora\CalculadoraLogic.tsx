// Datos reales de costos de marketing por región
export const costosMarketingPorRegion = {
  MXN: {
    agencia: { min: 25000, max: 80000, promedio: 52500 },
    herramientas: { min: 3000, max: 15000, promedio: 9000 },
    ambos: { min: 28000, max: 95000, promedio: 61500 }
  },
  USD: {
    agencia: { min: 2000, max: 8000, promedio: 5000 },
    herramientas: { min: 200, max: 1500, promedio: 850 },
    ambos: { min: 2200, max: 9500, promedio: 5850 }
  },
  EUR: {
    agencia: { min: 1800, max: 7200, promedio: 4500 },
    herramientas: { min: 180, max: 1350, promedio: 765 },
    ambos: { min: 1980, max: 8550, promedio: 5265 }
  }
}

// Precios de Emma por región
export const preciosEmma = {
  MXN: { mensual: 2999, anual: 29990 },
  USD: { mensual: 149, anual: 1490 },
  EUR: { mensual: 139, anual: 1390 }
}

// Número total de servicios que Emma incluye
export const TOTAL_SERVICIOS_EMMA = 35

// Datos del mercado de marketing digital
export const estadisticasReales = {
  limitacionesAgencias: {
    tiempoRespuesta: '2-5 días promedio para revisiones y cambios',
    limitesContenido: 'Mayoría de agencias limitan posts mensuales',
    horariosRestringidos: 'Trabajo limitado a horarios de oficina',
    costosExtras: 'Cobros adicionales por cambios urgentes'
  },
  ventajasIA: {
    velocidad: 'Procesamiento instantáneo vs días de espera',
    disponibilidad: '24/7 sin interrupciones vs horarios limitados',
    escalabilidad: 'Capacidad ilimitada sin costos adicionales',
    consistencia: 'Calidad consistente en cada entrega'
  },
  ahorroPromedio: {
    tiempo: 'Reducción significativa en tiempo de producción',
    costos: 'Ahorro considerable vs agencias tradicionales',
    eficiencia: 'Múltiples proyectos simultáneos'
  }
}

export interface CalculadoraInputs {
  moneda: 'MXN' | 'USD' | 'EUR'
  gastoMensual: number
  tipoGasto: 'agencia' | 'herramientas' | 'ambos'
  serviciosActuales: string[]
  limitacionesActuales: string
}

export interface CalculadoraResults {
  ahorroMensual: number
  ahorroAnual: number
  costoEmmaAnual: number
  roiPorcentaje: number
  serviciosIncluidos: number
  eficienciaGanada: number
}

export function calcularAhorros(inputs: CalculadoraInputs): CalculadoraResults {
  const { moneda, gastoMensual } = inputs
  
  // Si no hay gasto, retornar valores en cero
  if (!gastoMensual || gastoMensual <= 0) {
    return {
      ahorroMensual: 0,
      ahorroAnual: 0,
      costoEmmaAnual: preciosEmma[moneda].anual,
      roiPorcentaje: 0,
      serviciosIncluidos: TOTAL_SERVICIOS_EMMA,
      eficienciaGanada: 0
    }
  }

  // Costo de Emma
  const costoEmmaAnual = preciosEmma[moneda].anual
  const costoEmmaMensual = preciosEmma[moneda].mensual
  
  // Calcular ahorro mensual (gasto actual - costo Emma)
  const ahorroMensual = Math.max(0, gastoMensual - costoEmmaMensual)
  const ahorroAnual = ahorroMensual * 12

  // Calcular ROI
  const roiPorcentaje = ahorroAnual > 0 ? (ahorroAnual / costoEmmaAnual) * 100 : 0
  
  // Emma puede manejar 3x más proyectos que una agencia tradicional
  const eficienciaGanada = 3
  
  return {
    ahorroMensual,
    ahorroAnual,
    costoEmmaAnual,
    roiPorcentaje,
    serviciosIncluidos: TOTAL_SERVICIOS_EMMA,
    eficienciaGanada
  }
}