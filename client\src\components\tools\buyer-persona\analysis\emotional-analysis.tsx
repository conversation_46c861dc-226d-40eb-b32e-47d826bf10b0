"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Heart, Zap, CheckCircle, AlertTriangle } from "lucide-react";

interface EmotionalAnalysisProps {
  emotionalData: {
    primary_emotions?: string[];
    stress_triggers?: string[];
    motivation_drivers?: string[];
    decision_making_style?: string;
    communication_tone_preference?: string;
    trust_building_factors?: string[];
    emotional_barriers?: string[];
    excitement_triggers?: string[];
  };
  delay?: number;
}

export function EmotionalAnalysis({ emotionalData, delay = 0.2 }: EmotionalAnalysisProps) {
  if (!emotionalData || Object.keys(emotionalData).length === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay }}
    >
      <Card className="bg-gradient-to-br from-pink-50 to-rose-100 border-pink-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-pink-800">
            <Heart className="h-5 w-5" />
            Análisis Emocional Avanzado
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Emociones Primarias */}
          {emotionalData.primary_emotions && emotionalData.primary_emotions.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Emociones Dominantes
              </h4>
              <div className="flex flex-wrap gap-2">
                {emotionalData.primary_emotions.map((emotion: string, index: number) => (
                  <Badge key={index} className="bg-pink-100 text-pink-800">
                    {emotion}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Motivadores */}
          {emotionalData.motivation_drivers && emotionalData.motivation_drivers.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Motivadores Clave
              </h4>
              <div className="space-y-2">
                {emotionalData.motivation_drivers.map((driver: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">{driver}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Triggers de Estrés */}
          {emotionalData.stress_triggers && emotionalData.stress_triggers.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                Triggers de Estrés
              </h4>
              <div className="space-y-2">
                {emotionalData.stress_triggers.map((trigger: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">{trigger}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Barreras Emocionales */}
          {emotionalData.emotional_barriers && emotionalData.emotional_barriers.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                Barreras Emocionales
              </h4>
              <div className="space-y-2">
                {emotionalData.emotional_barriers.map((barrier: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">{barrier}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Triggers de Emoción */}
          {emotionalData.excitement_triggers && emotionalData.excitement_triggers.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                Triggers de Emoción
              </h4>
              <div className="space-y-2">
                {emotionalData.excitement_triggers.map((trigger: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">{trigger}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Estilo de Comunicación y Toma de Decisiones */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {emotionalData.communication_tone_preference && (
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-semibold text-gray-700 mb-2">Estilo de Comunicación</h4>
                <Badge className="bg-blue-100 text-blue-800">
                  {emotionalData.communication_tone_preference}
                </Badge>
              </div>
            )}

            {emotionalData.decision_making_style && (
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-semibold text-gray-700 mb-2">Estilo de Decisión</h4>
                <Badge className="bg-purple-100 text-purple-800">
                  {emotionalData.decision_making_style}
                </Badge>
              </div>
            )}
          </div>

          {/* Factores de Confianza */}
          {emotionalData.trust_building_factors && emotionalData.trust_building_factors.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-500" />
                Factores de Confianza
              </h4>
              <div className="flex flex-wrap gap-2">
                {emotionalData.trust_building_factors.map((factor: string, index: number) => (
                  <Badge key={index} variant="outline" className="border-blue-200 text-blue-700">
                    {factor}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
