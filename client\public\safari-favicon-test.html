<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safari Favicon Test - Emma Studio</title>
    
    <!-- Safari Favicon Test - Multiple approaches -->
    <link rel="shortcut icon" href="/favicon-32x32.png" type="image/png">
    <link rel="icon" href="/favicon-32x32.png" type="image/png">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon">
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #3018ef, #dd3a5a);
            color: white;
            text-align: center;
        }
        .test-info {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .favicon-test {
            display: inline-block;
            margin: 10px;
            padding: 10px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🍎 Safari Favicon Test - Emma Studio</h1>
    
    <div class="test-info">
        <h2>Testing Safari Favicon Implementation</h2>
        <p>This page tests different favicon approaches for Safari compatibility.</p>
        
        <div class="favicon-test">
            <h3>Current Favicon Files:</h3>
            <p>✅ favicon.ico (32x32 PNG format)</p>
            <p>✅ favicon-32x32.png</p>
            <p>✅ apple-touch-icon.png</p>
        </div>
        
        <div class="favicon-test">
            <h3>Safari Test Instructions:</h3>
            <p>1. Check browser tab for favicon</p>
            <p>2. Add to bookmarks</p>
            <p>3. Check bookmark bar</p>
            <p>4. Clear Safari cache if needed</p>
        </div>
        
        <div class="favicon-test">
            <h3>Direct File Links:</h3>
            <p><a href="/favicon.ico" style="color: white;">favicon.ico</a></p>
            <p><a href="/favicon-32x32.png" style="color: white;">favicon-32x32.png</a></p>
            <p><a href="/apple-touch-icon.png" style="color: white;">apple-touch-icon.png</a></p>
        </div>
    </div>
    
    <p><a href="/" style="color: white; text-decoration: underline;">← Back to Emma Studio</a></p>
</body>
</html>
