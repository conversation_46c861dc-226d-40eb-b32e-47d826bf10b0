/**
 * SEO & GPT Optimizer™ - Research Form Component
 * Form for initiating research with advanced options
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  Settings,
  Globe,
  MessageSquare,
  HelpCircle,
  MapPin,
  Newspaper
} from 'lucide-react';
import { ResearchRequest } from '../../../types/seo-gpt-optimizer';

interface ResearchFormProps {
  onSubmit: (request: ResearchRequest) => void;
  loading: boolean;
  className?: string;
}

const ResearchForm: React.FC<ResearchFormProps> = ({
  onSubmit,
  loading,
  className = ''
}) => {
  const [formData, setFormData] = useState<ResearchRequest>({
    topic: '',
    target_language: 'es',
    include_reddit: true,
    include_quora: true,
    // Geographic defaults
    target_country: 'ES',
    // Additional sources
    include_news: false,
    // Competitive analysis
    competitor_domains: []
  });
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.topic.trim()) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: keyof ResearchRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
            <Search className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Research Engine</h2>
            <p className="text-gray-600 text-sm">Investiga cualquier tema para crear contenido optimizado</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Main Topic Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tema de Investigación *
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.topic}
              onChange={(e) => handleInputChange('topic', e.target.value)}
              placeholder="Ej: beneficios del magnesio para la salud"
              className="w-full px-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 pr-12"
              disabled={loading}
              required
            />
            <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Describe el tema que quieres investigar de forma específica
          </p>
        </div>

        {/* Language and Geographic Targeting */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Globe className="w-4 h-4 inline mr-1" />
              Idioma Objetivo
            </label>
            <select
              value={formData.target_language}
              onChange={(e) => handleInputChange('target_language', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              disabled={loading}
            >
              <option value="es">Español</option>
              <option value="en">English</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="it">Italiano</option>
              <option value="pt">Português</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MapPin className="w-4 h-4 inline mr-1" />
              País Objetivo
            </label>
            <select
              value={formData.target_country}
              onChange={(e) => handleInputChange('target_country', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              disabled={loading}
            >
              <option value="ES">España</option>
              <option value="MX">México</option>
              <option value="AR">Argentina</option>
              <option value="CO">Colombia</option>
              <option value="CL">Chile</option>
              <option value="PE">Perú</option>
              <option value="VE">Venezuela</option>
              <option value="US">Estados Unidos</option>
              <option value="GB">Reino Unido</option>
              <option value="FR">Francia</option>
              <option value="DE">Alemania</option>
              <option value="IT">Italia</option>
              <option value="BR">Brasil</option>
              <option value="GLOBAL">Global</option>
            </select>
          </div>
        </div>



        {/* Advanced Options Toggle */}
        <div>
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200"
            disabled={loading}
          >
            <Settings className="w-4 h-4" />
            Opciones Avanzadas
            <motion.div
              animate={{ rotate: showAdvanced ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <HelpCircle className="w-4 h-4" />
            </motion.div>
          </button>
        </div>

        {/* Advanced Options */}
        <motion.div
          initial={false}
          animate={{ 
            height: showAdvanced ? 'auto' : 0,
            opacity: showAdvanced ? 1 : 0
          }}
          transition={{ duration: 0.3 }}
          className="overflow-hidden"
        >
          <div className="space-y-6 pt-4 border-t border-gray-100">
            {/* Social Sources */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Fuentes de Insights Sociales
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {/* Reddit Option */}
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center gap-3">
                  <MessageSquare className="w-4 h-4 text-orange-500" />
                  <div>
                    <div className="font-medium text-gray-900 text-sm">Reddit</div>
                    <div className="text-xs text-gray-600">Discusiones de comunidad</div>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.include_reddit}
                    onChange={(e) => handleInputChange('include_reddit', e.target.checked)}
                    className="sr-only peer"
                    disabled={loading}
                  />
                  <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* Quora Option */}
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center gap-3">
                  <HelpCircle className="w-4 h-4 text-red-500" />
                  <div>
                    <div className="font-medium text-gray-900 text-sm">Quora</div>
                    <div className="text-xs text-gray-600">Preguntas y respuestas</div>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.include_quora}
                    onChange={(e) => handleInputChange('include_quora', e.target.checked)}
                    className="sr-only peer"
                    disabled={loading}
                  />
                  <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>



              {/* News Option */}
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center gap-3">
                  <Newspaper className="w-4 h-4 text-blue-600" />
                  <div>
                    <div className="font-medium text-gray-900 text-sm">Noticias</div>
                    <div className="text-xs text-gray-600">Artículos recientes</div>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.include_news}
                    onChange={(e) => handleInputChange('include_news', e.target.checked)}
                    className="sr-only peer"
                    disabled={loading}
                  />
                  <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
            </div>



            {/* Competitive Analysis */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Análisis Competitivo (Opcional)
              </h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dominios Competidores
                </label>
                <input
                  type="text"
                  placeholder="ejemplo.com, competidor.es (separados por comas)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
                  disabled={loading}
                  onChange={(e) => {
                    const domains = e.target.value.split(',').map(d => d.trim()).filter(d => d);
                    handleInputChange('competitor_domains', domains);
                  }}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Analizar gaps y oportunidades vs competidores específicos
                </p>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-4">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
                <div className="text-sm text-blue-800">
                  <strong>🚀 Research Engine Completo:</strong> Siempre realiza una investigación exhaustiva
                  combinando múltiples fuentes, análisis geográfico y temporal para obtener insights súper
                  específicos y oportunidades de contenido que tu competencia no está viendo.
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Submit Button - CON LOADING */}
        <motion.button
          type="submit"
          disabled={!formData.topic.trim() || loading}
          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-6 rounded-xl font-medium hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center gap-3"
          whileHover={{ scale: loading ? 1 : 1.02 }}
          whileTap={{ scale: loading ? 1 : 0.98 }}
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              Investigando...
            </>
          ) : (
            <>
              <Search className="w-5 h-5" />
              Iniciar Investigación
            </>
          )}
        </motion.button>

        {/* Info */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            La investigación completa puede tomar entre 30-60 segundos
          </p>
        </div>
      </form>
    </motion.div>
  );
};

export default ResearchForm;
