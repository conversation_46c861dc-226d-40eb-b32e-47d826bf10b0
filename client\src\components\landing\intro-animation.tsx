import { motion, AnimatePresence } from "framer-motion";
import { useState, useEffect } from "react";
import { emmaAiLogo } from "../../assets";

// Duración exacta de 3.33 segundos (3330ms)
const ANIMATION_DURATION = 3330;

export default function IntroAnimation() {
  const [showIntro, setShowIntro] = useState(true);
  const [contentLoaded, setContentLoaded] = useState(false);

  useEffect(() => {
    // Marcamos el contenido como cargado una vez que el componente está montado
    setContentLoaded(true);

    // Forzamos que el preloader se muestre por al menos 3.33 segundos
    const timer = setTimeout(() => {
      setShowIntro(false);
    }, ANIMATION_DURATION);

    // Asegurarnos de limpiar el timer si el componente se desmonta
    return () => clearTimeout(timer);
  }, []);

  return (
    <AnimatePresence>
      {showIntro && (
        <motion.div
          className="fixed inset-0 bg-gradient-to-b from-[#f8f9fa] to-[#e9ecef] flex flex-col items-center justify-center z-[100]"
          initial={{ opacity: 1 }}
          exit={{
            opacity: 0,
            scale: 1.1,
            filter: "blur(10px)",
            transition: {
              duration: 0.8,
              ease: [0.43, 0.13, 0.23, 0.96],
            },
          }}
        >
          {/* Background circles */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(15)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute bg-blue-500/10 rounded-full"
                style={{
                  width: `${Math.random() * 120 + 40}px`,
                  height: `${Math.random() * 120 + 40}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  x: [0, Math.random() * 50 - 25],
                  y: [0, Math.random() * 50 - 25],
                  scale: [1, 1.1, 0.9, 1],
                  opacity: [0.2, 0.5, 0.2],
                }}
                transition={{
                  duration: 4 + Math.random() * 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>

          {/* Grid pattern */}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage:
                "linear-gradient(rgba(59, 130, 246, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.03) 1px, transparent 1px)",
              backgroundSize: "20px 20px",
            }}
          />

          {/* Main content */}
          <div className="z-10 flex flex-col items-center">
            {/* Logo */}
            <motion.div
              initial={{ scale: 0.7, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              transition={{ duration: 0.8, type: "spring", stiffness: 110 }}
              className="mb-8"
            >
              <motion.img
                src={emmaAiLogo}
                alt="Emma AI Logo"
                className="h-56 w-auto" /* Logo más grande */
                animate={{
                  scale: [1, 1.05, 1.02, 1.07, 1],
                  filter: [
                    "drop-shadow(0 0 0px rgba(59, 130, 246, 0.4))",
                    "drop-shadow(0 0 10px rgba(59, 130, 246, 0.6))",
                    "drop-shadow(0 0 20px rgba(59, 130, 246, 0.8))",
                    "drop-shadow(0 0 30px rgba(59, 130, 246, 0.9))",
                    "drop-shadow(0 0 0px rgba(59, 130, 246, 0.4))",
                  ],
                }}
                transition={{
                  duration: ANIMATION_DURATION / 1000, // Duración sincronizada con todo el preloader
                  times: [0, 0.2, 0.5, 0.8, 1],
                  ease: "easeInOut",
                }}
              />
            </motion.div>

            {/* "Wow Effect" Text */}
            <motion.p
              className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{
                opacity: [0, 1, 1, 0],
                y: [20, 0, 0, -20],
                scale: [0.9, 1.1, 1.1, 0.9],
              }}
              transition={{
                duration: 1.5,
                times: [0, 0.1, 0.9, 1],
                delay: 0.5,
              }}
            >
              ¡Wow!
            </motion.p>

            {/* Progress bar */}
            <motion.div
              className="w-80 h-3 bg-gray-200 rounded-full overflow-hidden mt-1"
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: "20rem" }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              <motion.div
                className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-red-500"
                initial={{ width: "0%" }}
                animate={{ width: "100%" }}
                transition={{
                  duration: ANIMATION_DURATION / 1000 - 0.5, // Terminar justo antes del final de la animación
                  ease: [0.4, 0, 0.2, 1], // ease-out cubic
                }}
              />
            </motion.div>

            {/* Text */}
            <motion.p
              className="text-lg font-medium text-gray-700 mt-5"
              initial={{ opacity: 0 }}
              animate={{ opacity: [0, 1, 1] }}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              Preparando tu experiencia...
            </motion.p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
