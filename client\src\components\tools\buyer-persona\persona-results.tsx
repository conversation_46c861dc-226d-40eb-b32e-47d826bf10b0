"use client";

import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { User, Users } from "lucide-react";

interface PersonaResultsProps {
  personaCount: number;
  selectedPersonaIndex: number;
  onPersonaSelect: (index: number) => void;
}

export function PersonaResults({
  personaCount,
  selectedPersonaIndex,
  onPersonaSelect
}: PersonaResultsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-8"
    >
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring" }}
          className="inline-flex items-center gap-3 bg-green-500/10 border border-green-500/20 rounded-full px-6 py-3 mb-6"
        >
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-green-700 font-semibold">Generación IA Completada</span>
        </motion.div>

        <h2 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4">
          ✅ Buyer Personas Generadas
        </h2>
        <p className="text-xl text-gray-700 mb-6">
          Se generaron exitosamente {personaCount} buyer personas detalladas para tu producto/servicio
        </p>
      </div>

      {/* Navegación entre personas - Siempre visible */}
      <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-purple-200">
        <div className="text-center mb-4">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Users className="h-5 w-5 text-purple-600" />
            <span className="text-lg font-semibold text-purple-800">
              Selecciona una Persona ({personaCount} disponibles)
            </span>
          </div>
          <p className="text-sm text-gray-600">
            Haz clic en cualquier persona para ver sus detalles completos
          </p>
        </div>

        <div className="flex justify-center flex-wrap gap-3">
          {Array.from({ length: personaCount }, (_, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant={selectedPersonaIndex === index ? "default" : "outline"}
                size="lg"
                onClick={() => onPersonaSelect(index)}
                className={`min-w-[140px] h-12 font-semibold transition-all duration-200 ${
                  selectedPersonaIndex === index
                    ? "bg-gradient-to-r from-purple-600 to-purple-700 text-white border-0 shadow-lg ring-2 ring-purple-300"
                    : "bg-white border-2 border-purple-200 text-purple-700 hover:bg-purple-50 hover:border-purple-300 shadow-md"
                }`}
              >
                <User className="h-4 w-4 mr-2" />
                Persona {index + 1}
              </Button>
            </motion.div>
          ))}
        </div>

        {personaCount > 1 && (
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              💡 Cada persona representa un segmento único de tu mercado objetivo
            </p>
          </div>
        )}
      </div>
    </motion.div>
  );
}
