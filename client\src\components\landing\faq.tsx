import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Minus } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

export default function FAQ() {
  const { t } = useLanguage();
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const toggleQuestion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const faqs = t('landing.faq.questions');

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            {t('landing.faq.title')}
          </h2>
          <p className="text-xl max-w-2xl mx-auto font-bold">
            {t('landing.faq.subtitle')}
          </p>
        </motion.div>

        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              className="mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
            >
              <motion.button
                className={`w-full p-5 bg-white rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] flex justify-between items-center text-left ${
                  openIndex === index ? "rounded-b-none border-b-0" : ""
                }`}
                onClick={() => toggleQuestion(index)}
                whileHover={{ y: openIndex === index ? 0 : -3 }}
              >
                <span className="font-black text-lg">{faq.question}</span>
                {openIndex === index ? (
                  <div className="bg-black text-white rounded-full p-1">
                    <Minus size={18} />
                  </div>
                ) : (
                  <div className="bg-purple-100 text-purple-600 rounded-full p-1 border-2 border-black">
                    <Plus size={18} />
                  </div>
                )}
              </motion.button>

              <AnimatePresence>
                {openIndex === index && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{
                      opacity: 1,
                      height: "auto",
                      transition: {
                        height: {
                          duration: 0.3,
                        },
                        opacity: {
                          duration: 0.3,
                          delay: 0.1,
                        },
                      },
                    }}
                    exit={{
                      opacity: 0,
                      height: 0,
                      transition: {
                        height: {
                          duration: 0.3,
                        },
                        opacity: {
                          duration: 0.2,
                        },
                      },
                    }}
                    className={`bg-white rounded-b-xl border-3 border-t-0 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden`}
                  >
                    <div className="p-5 bg-purple-50">
                      <p className="text-gray-700">{faq.answer}</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <p className="text-lg mb-6">
            {t('landing.faq.no_answer')}
          </p>
          <motion.button
            className="bg-purple-500 text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-purple-600 transition-all duration-300"
            whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
            whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
          >
            {t('landing.faq.contact_us')}
          </motion.button>
        </motion.div>
      </div>

      {/* Elementos de fondo */}
      <div className="absolute top-40 right-20 w-64 h-64 bg-purple-200 rounded-full filter blur-3xl opacity-20 transform translate-x-1/3"></div>
      <div className="absolute bottom-20 left-20 w-64 h-64 bg-blue-200 rounded-full filter blur-3xl opacity-20 transform -translate-x-1/3"></div>
    </section>
  );
}
