import React from 'react'
import { BarChart3, Clock, Zap, Target } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'

export function EstadisticasReales() {
  const { t } = useLanguage()
  return (
    <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-2xl flex items-center justify-center mx-auto mb-4">
          <BarChart3 className="text-white" size={32} />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          {t('calculadora.stats.comparison_title')}
        </h3>
        <p className="text-gray-600">
          {t('calculadora.stats.comparison_subtitle')}
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        {/* Limitaciones de Agencias Tradicionales */}
        <div>
          <h4 className="text-lg font-bold text-red-700 mb-4 flex items-center gap-2">
            <Clock className="text-red-600" size={20} />
            {t('calculadora.stats.traditional_agencies')}
          </h4>
          <div className="space-y-3">
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="font-semibold text-red-800 text-sm mb-1">
                {t('calculadora.stats.response_time')}
              </div>
              <div className="text-red-700 text-sm">
                {t('calculadora.stats.agency_limitations.response_time')}
              </div>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="font-semibold text-red-800 text-sm mb-1">
                {t('calculadora.stats.content_limits')}
              </div>
              <div className="text-red-700 text-sm">
                {t('calculadora.stats.agency_limitations.content_limits')}
              </div>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="font-semibold text-red-800 text-sm mb-1">
                {t('calculadora.stats.restricted_hours')}
              </div>
              <div className="text-red-700 text-sm">
                {t('calculadora.stats.agency_limitations.restricted_hours')}
              </div>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="font-semibold text-red-800 text-sm mb-1">
                {t('calculadora.stats.extra_costs')}
              </div>
              <div className="text-red-700 text-sm">
                {t('calculadora.stats.agency_limitations.extra_costs')}
              </div>
            </div>
          </div>
        </div>

        {/* Ventajas de Emma IA */}
        <div>
          <h4 className="text-lg font-bold text-green-700 mb-4 flex items-center gap-2">
            <Zap className="text-green-600" size={20} />
            {t('calculadora.stats.emma_ai')}
          </h4>
          <div className="space-y-3">
            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="font-semibold text-green-800 text-sm mb-1">
                {t('calculadora.stats.speed')}
              </div>
              <div className="text-green-700 text-sm">
                {t('calculadora.stats.emma_advantages.speed')}
              </div>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="font-semibold text-green-800 text-sm mb-1">
                {t('calculadora.stats.availability')}
              </div>
              <div className="text-green-700 text-sm">
                {t('calculadora.stats.emma_advantages.availability')}
              </div>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="font-semibold text-green-800 text-sm mb-1">
                {t('calculadora.stats.scalability')}
              </div>
              <div className="text-green-700 text-sm">
                {t('calculadora.stats.emma_advantages.scalability')}
              </div>
            </div>
            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="font-semibold text-green-800 text-sm mb-1">
                {t('calculadora.stats.consistency')}
              </div>
              <div className="text-green-700 text-sm">
                {t('calculadora.stats.emma_advantages.consistency')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Resumen de Ahorros */}
      <div className="mt-8 bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 rounded-2xl p-6">
        <h4 className="text-lg font-bold text-gray-900 mb-4 text-center flex items-center justify-center gap-2">
          <Target className="text-[#3018ef]" size={20} />
          {t('calculadora.stats.real_benefits')}
        </h4>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-[#3018ef] mb-1">
              {t('calculadora.stats.instant')}
            </div>
            <div className="text-sm text-gray-700">
              {t('calculadora.stats.vs_days_wait')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-[#dd3a5a] mb-1">
              {t('calculadora.stats.24_7')}
            </div>
            <div className="text-sm text-gray-700">
              {t('calculadora.stats.vs_office_hours')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-1">
              {t('calculadora.stats.unlimited')}
            </div>
            <div className="text-sm text-gray-700">
              {t('calculadora.stats.vs_monthly_limits')}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-500">
          {t('calculadora.stats.disclaimer')}
        </p>
      </div>
    </div>
  )
}
