import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';
import { FreeGenerationHero } from './FreeGenerationHero';
import { FreeGenerationForm } from './FreeGenerationForm';
import { GeneratedResults } from './GeneratedResults';
import { ImageModal } from './ImageModal';
import { FreeGenerationFeatures } from './FreeGenerationFeatures';
import { useFreeGeneration } from '@/hooks/useFreeGeneration';
import { downloadImage, copyImageUrl } from '@/utils/imageUtils';
export function FreeGenerationContent() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { toast } = useToast();

  const {
    prompt,
    setPrompt,
    isGenerating,
    uploadedImages,
    setUploadedImages,
    generatedResult,
    handleFreeGeneration,
    resetGeneration
  } = useFreeGeneration();

  const handleImageClick = (imageUrl: string) => {
    console.log('🖼️ Image clicked, opening modal for:', imageUrl);
    console.log('🔍 Modal state before:', { isModalOpen, selectedImage });

    setSelectedImage(imageUrl);
    setIsModalOpen(true);

    console.log('✅ Modal should now be open with image:', imageUrl);
  };

  const handleDownload = async (imageUrl: string, filename: string) => {
    console.log('📥 Starting download for:', imageUrl, 'as:', filename);

    const result = await downloadImage(imageUrl, filename);

    if (result.success) {
      if (result.method === 'proxy') {
        toast({
          title: "✅ Descarga completada",
          description: "Tu imagen Emma se ha descargado exitosamente"
        });
      } else if (result.method === 'fetch') {
        toast({
          title: "✅ Descarga completada",
          description: "Tu imagen Emma se ha descargado exitosamente"
        });
      } else {
        toast({
          title: "✅ Descarga iniciada",
          description: "Tu imagen Emma se está descargando"
        });
      }
    } else {
      toast({
        title: "❌ Error al descargar",
        description: "No se pudo descargar la imagen. Intenta de nuevo.",
        variant: "destructive"
      });
    }
  };

  const handleCopyUrl = async (imageUrl: string) => {
    const result = await copyImageUrl(imageUrl);
    
    if (result.success) {
      toast({
        title: "✅ URL copiada",
        description: "La URL de la imagen se ha copiado al portapapeles"
      });
    } else {
      toast({
        title: "❌ Error al copiar",
        description: "No se pudo copiar la URL automáticamente",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      {/* Hero Section profesional estilo Emma */}
      <FreeGenerationHero />

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-8"
        >
          <FreeGenerationForm
            prompt={prompt}
            setPrompt={setPrompt}
            uploadedImages={uploadedImages}
            setUploadedImages={setUploadedImages}
            isGenerating={isGenerating}
            onGenerate={handleFreeGeneration}
          />

          {/* Generated Result */}
          {generatedResult && (
            <GeneratedResults
              result={generatedResult}
              onImageClick={handleImageClick}
              onDownload={handleDownload}
              onReset={resetGeneration}
            />
          )}

          {/* Features Premium */}
          {!generatedResult && <FreeGenerationFeatures />}
        </motion.div>
      </div>

      {/* Modal para ampliar imágenes */}
      <ImageModal
        isOpen={isModalOpen}
        onClose={() => {
          console.log('🔒 Closing modal');
          setIsModalOpen(false);
          setSelectedImage(null);
        }}
        imageUrl={selectedImage}
        onDownload={handleDownload}
        onCopyUrl={() => {}} // Dummy function since we removed the button
      />
    </div>
  );
}
