/**
 * Enhanced type definitions for Smart Form components
 * Following TypeScript best practices and strict typing
 */

import { LucideIcon } from "lucide-react";

// Core form data interface with strict typing
export interface SmartFormData {
  // Product information
  product_type: string;
  product_name: string;
  industry: string;
  target_audience: string;
  main_problem: string;
  price_range: string;
  unique_value: string;

  // Geographic and market data
  geographic_scope: string;
  target_country: string;
  target_regions: string[];
  sales_channels: string[];

  // Audience insights
  audience_knowledge: string;
  existing_audience: string;
  business_sizes: string[];

  // Problem and decision context
  problem_urgency: string;
  decision_maker: string;
}

// Props for the main SmartForm component
export interface SmartFormProps {
  onSubmit: (data: SmartFormData) => void;
  isLoading: boolean;
}

// Form step configuration
export interface FormStep {
  id: string;
  title: string;
  subtitle: string;
  icon: LucideIcon;
  color: string;
}

// Generic option interface for form selections
export interface SelectOption {
  id: string;
  name: string;
  icon?: string;
  description?: string;
  color?: string;
  type?: "B2C" | "B2B";
  range?: string;
}

// Country interface with enhanced properties
export interface Country {
  code: string;
  name: string;
  flag: string;
  region: string;
}

// Step component props interface
export interface StepComponentProps {
  formData: SmartFormData;
  updateFormData: (field: keyof SmartFormData, value: string) => void;
  updateArrayField: (field: keyof SmartFormData, value: string) => void;
  userCountry: Country;
}

// Validation interfaces
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface FieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

// Form state management
export interface FormState {
  currentStep: number;
  formData: SmartFormData;
  isLoading: boolean;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
}

// Enhanced submission data
export interface EnhancedFormSubmission extends SmartFormData {
  product_description: string;
  validation_score: number;
  completion_time_seconds: number;
  user_agent: string;
  timestamp: string;
}
