import React from "react";
import { motion } from "framer-motion";
import { 
  Zap, 
  ShoppingBag, 
  Users, 
  Briefcase, 
  Heart, 
  Star,
  TrendingUp,
  Gift,
  Calendar,
  Megaphone
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface QuickTemplate {
  id: string;
  title: string;
  description: string;
  prompt: string;
  icon: React.ElementType;
  category: string;
  tags: string[];
  color: string;
}

interface QuickTemplatesProps {
  platform: string;
  onTemplateSelect: (prompt: string) => void;
}

const QuickTemplates: React.FC<QuickTemplatesProps> = ({
  platform,
  onTemplateSelect
}) => {
  // Plantillas específicas por plataforma
  const platformTemplates: Record<string, QuickTemplate[]> = {
    facebook: [
      {
        id: "product-launch",
        title: "Lanzamiento de Producto",
        description: "Anuncia un nuevo producto con impacto",
        prompt: "Anuncio profesional de lanzamiento de producto, producto destacado en el centro, fondo limpio y moderno, iluminación comercial, colores vibrantes, texto promocional 'NUEVO', calidad de estudio fotográfico",
        icon: ShoppingBag,
        category: "Producto",
        tags: ["nuevo", "lanzamiento", "producto"],
        color: "bg-blue-500"
      },
      {
        id: "service-promo",
        title: "Promoción de Servicio",
        description: "Destaca tus servicios profesionales",
        prompt: "Anuncio profesional de servicios, personas profesionales en ambiente de oficina moderna, colores corporativos, mensaje claro de beneficios, call-to-action visible, estilo comercial limpio",
        icon: Briefcase,
        category: "Servicio",
        tags: ["profesional", "servicio", "corporativo"],
        color: "bg-green-500"
      },
      {
        id: "event-promo",
        title: "Evento Especial",
        description: "Promociona eventos y actividades",
        prompt: "Anuncio dinámico de evento, ambiente festivo y energético, colores llamativos, elementos gráficos de celebración, fecha destacada, diseño que transmite emoción y urgencia",
        icon: Calendar,
        category: "Evento",
        tags: ["evento", "celebración", "fecha"],
        color: "bg-purple-500"
      },
      {
        id: "brand-awareness",
        title: "Reconocimiento de Marca",
        description: "Construye awareness de tu marca",
        prompt: "Anuncio de marca elegante y memorable, logo prominente, colores de marca consistentes, mensaje aspiracional, diseño limpio y profesional, enfoque en valores de marca",
        icon: Star,
        category: "Marca",
        tags: ["marca", "logo", "valores"],
        color: "bg-yellow-500"
      },
      {
        id: "supplements-fitness",
        title: "Suplementos Fitness",
        description: "Anuncios para tiendas de suplementos",
        prompt: "Anuncio profesional de suplementos fitness, producto destacado con envase atractivo, persona atlética en segundo plano, colores energéticos azul y naranja, texto 'TRANSFORMA TU CUERPO', antes y después sutil, call-to-action 'COMPRA AHORA', ambiente de gym",
        icon: TrendingUp,
        category: "Fitness",
        tags: ["suplementos", "fitness", "transformación"],
        color: "bg-orange-500"
      }
    ],
    instagram: [
      {
        id: "lifestyle-product",
        title: "Producto Lifestyle",
        description: "Producto en contexto de vida real",
        prompt: "Post estético de Instagram, producto integrado en lifestyle, iluminación natural suave, composición minimalista, colores armoniosos, ambiente aspiracional y auténtico",
        icon: Heart,
        category: "Lifestyle",
        tags: ["estético", "natural", "aspiracional"],
        color: "bg-pink-500"
      },
      {
        id: "fashion-brand",
        title: "Marca de Moda",
        description: "Contenido fashion y tendencias",
        prompt: "Post de moda trendy, modelo con outfit destacado, fondo estético, iluminación profesional, colores de temporada, composición que sigue tendencias actuales de Instagram",
        icon: Users,
        category: "Moda",
        tags: ["moda", "trendy", "outfit"],
        color: "bg-indigo-500"
      },
      {
        id: "food-beverage",
        title: "Comida y Bebidas",
        description: "Contenido gastronómico atractivo",
        prompt: "Fotografía gastronómica profesional, plato o bebida como protagonista, iluminación cálida, composición apetitosa, colores vibrantes, estilo food photography de alta calidad",
        icon: Gift,
        category: "Gastronomía",
        tags: ["comida", "bebida", "gastronómico"],
        color: "bg-orange-500"
      },
      {
        id: "fitness-wellness",
        title: "Fitness y Bienestar",
        description: "Contenido de salud y ejercicio",
        prompt: "Post motivacional de fitness, persona ejercitándose, ambiente energético, colores dinámicos, mensaje inspiracional, composición que transmite movimiento y vitalidad",
        icon: TrendingUp,
        category: "Fitness",
        tags: ["fitness", "salud", "motivacional"],
        color: "bg-green-600"
      },
      {
        id: "supplements-lifestyle",
        title: "Suplementos Lifestyle",
        description: "Suplementos integrados en estilo de vida",
        prompt: "Post estético de suplementos lifestyle, persona fit tomando batido de proteína, cocina moderna, iluminación natural, colores Instagram trending, producto integrado naturalmente, ambiente saludable y aspiracional",
        icon: Heart,
        category: "Suplementos",
        tags: ["suplementos", "lifestyle", "saludable"],
        color: "bg-purple-500"
      }
    ],
    linkedin: [
      {
        id: "b2b-service",
        title: "Servicio B2B",
        description: "Servicios para empresas",
        prompt: "Anuncio corporativo profesional, ambiente de oficina moderna, personas en traje de negocios, colores sobrios y elegantes, mensaje de valor empresarial, diseño formal y confiable",
        icon: Briefcase,
        category: "B2B",
        tags: ["corporativo", "empresarial", "profesional"],
        color: "bg-blue-700"
      },
      {
        id: "software-tech",
        title: "Software y Tecnología",
        description: "Productos tecnológicos",
        prompt: "Anuncio tecnológico innovador, interfaces digitales, ambiente tech moderno, colores azules y grises, elementos gráficos de software, diseño que transmite innovación",
        icon: Zap,
        category: "Tecnología",
        tags: ["software", "tech", "innovación"],
        color: "bg-cyan-600"
      },
      {
        id: "consulting",
        title: "Consultoría",
        description: "Servicios de consultoría",
        prompt: "Anuncio de consultoría profesional, reunión de negocios, gráficos de crecimiento, ambiente ejecutivo, colores que transmiten confianza y expertise, diseño serio y competente",
        icon: TrendingUp,
        category: "Consultoría",
        tags: ["consultoría", "crecimiento", "expertise"],
        color: "bg-emerald-600"
      },
      {
        id: "recruitment",
        title: "Reclutamiento",
        description: "Ofertas de trabajo",
        prompt: "Anuncio de empleo atractivo, equipo diverso trabajando, ambiente colaborativo, colores que inspiran, mensaje de oportunidad profesional, diseño que atrae talento",
        icon: Users,
        category: "RRHH",
        tags: ["empleo", "equipo", "oportunidad"],
        color: "bg-purple-600"
      }
    ],
    youtube: [
      {
        id: "tutorial-thumb",
        title: "Tutorial/How-to",
        description: "Thumbnail para tutoriales",
        prompt: "Thumbnail llamativo para tutorial, persona con expresión de enseñanza, elementos gráficos educativos, texto grande y legible, colores contrastantes, diseño que promete aprendizaje",
        icon: Star,
        category: "Educativo",
        tags: ["tutorial", "educativo", "aprendizaje"],
        color: "bg-blue-600"
      },
      {
        id: "review-thumb",
        title: "Review/Análisis",
        description: "Thumbnail para reviews",
        prompt: "Thumbnail de review impactante, producto destacado, expresión facial expresiva, elementos de calificación, colores llamativos, diseño que genera curiosidad sobre la opinión",
        icon: Star,
        category: "Review",
        tags: ["review", "análisis", "opinión"],
        color: "bg-yellow-600"
      },
      {
        id: "entertainment",
        title: "Entretenimiento",
        description: "Contenido de entretenimiento",
        prompt: "Thumbnail de entretenimiento dinámico, expresiones exageradas, colores vibrantes y contrastantes, elementos gráficos divertidos, diseño que promete diversión y engagement",
        icon: Heart,
        category: "Entretenimiento",
        tags: ["diversión", "entretenimiento", "viral"],
        color: "bg-red-600"
      },
      {
        id: "news-update",
        title: "Noticias/Actualidad",
        description: "Contenido informativo",
        prompt: "Thumbnail informativo serio, elementos gráficos de noticias, colores que transmiten credibilidad, texto claro y directo, diseño profesional que inspira confianza",
        icon: Megaphone,
        category: "Noticias",
        tags: ["noticias", "información", "actualidad"],
        color: "bg-gray-600"
      }
    ]
  };

  const currentTemplates = platformTemplates[platform] || platformTemplates.facebook;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="w-5 h-5" />
          Plantillas Rápidas
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-3">
          {currentTemplates.map((template, index) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="cursor-pointer hover:shadow-md transition-all border-l-4 border-l-transparent hover:border-l-[#3018ef]"
                    onClick={() => onTemplateSelect(template.prompt)}>
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg ${template.color} text-white`}>
                      <template.icon className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm mb-1">{template.title}</h4>
                      <p className="text-xs text-gray-600 mb-2">{template.description}</p>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="secondary" className="text-xs">
                          {template.category}
                        </Badge>
                        {template.tags.slice(0, 2).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-xs text-blue-700 text-center">
            💡 Haz clic en cualquier plantilla para usarla como base
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickTemplates;
