/**
 * SEO & GPT Optimizer™ - Saved Research Modal
 * Modal component for displaying detailed research view
 */

import React from 'react';
import { Download } from 'lucide-react';
import { SavedResearch } from '../../../../hooks/seo-gpt-optimizer/useSavedResearch';
import { useSavedResearchContext } from '../context/SavedResearchContext';
import { useDataCompleteness } from '../hooks/useDataCompleteness';

import ResearchModalHeader from './modal/ResearchModalHeader';
import ResearchDataCompleteness from './modal/ResearchDataCompleteness';
import ResearchMetrics from './modal/ResearchMetrics';
import ResearchSections from './modal/ResearchSections';

interface SavedResearchModalProps {
  research: SavedResearch;
}

const SavedResearchModal: React.FC<SavedResearchModalProps> = ({ research }) => {
  const { setSelectedResearch, handleExportResearch } = useSavedResearchContext();
  const dataCheck = useDataCompleteness(research);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <ResearchModalHeader
          topic={research.topic}
          savedAt={formatDate(research.savedAt)}
          onClose={() => setSelectedResearch(null)}
        />

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Data Completeness */}
          <ResearchDataCompleteness dataCheck={dataCheck} />

          {/* Metrics */}
          <ResearchMetrics research={research} />

          {/* Research Sections */}
          <ResearchSections research={research} />

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <button
              onClick={() => handleExportResearch(research)}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors duration-200"
            >
              <Download className="w-4 h-4" />
              Descargar PDF
            </button>
            <button
              onClick={() => setSelectedResearch(null)}
              className="flex items-center gap-2 px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200"
            >
              Cerrar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SavedResearchModal;
