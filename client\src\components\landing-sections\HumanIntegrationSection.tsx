"use client"

import { motion } from "framer-motion"
import { useLanguage } from "@/contexts/LanguageContext"

export function HumanIntegrationSection() {
  const { t, currentLanguage } = useLanguage()

  // Función para obtener escenarios de integración traducidos
  const getIntegrationScenarios = (t: (key: string) => string) => [
    {
      icon: "🎨",
      title: t('landing.integration_complex_design_title'),
      description: t('landing.integration_complex_design_desc'),
      solution: t('landing.integration_complex_design_solution')
    },
    {
      icon: "📹",
      title: t('landing.integration_video_production_title'),
      description: t('landing.integration_video_production_desc'),
      solution: t('landing.integration_video_production_solution')
    },
    {
      icon: "📊",
      title: t('landing.integration_deep_analysis_title'),
      description: t('landing.integration_deep_analysis_desc'),
      solution: t('landing.integration_deep_analysis_solution')
    },
    {
      icon: "🎯",
      title: t('landing.integration_custom_strategy_title'),
      description: t('landing.integration_custom_strategy_desc'),
      solution: t('landing.integration_custom_strategy_solution')
    }
  ]

  // Obtener escenarios traducidos
  const integrationScenarios = getIntegrationScenarios(t)

  return (
    <section className="py-12 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-32 h-32 bg-[#3018ef] rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-[#dd3a5a] rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-5xl mx-auto">
          
          {/* Header */}
          <motion.div
            className="text-center mb-10"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl sm:text-4xl font-black text-gray-900 mb-4">
              🤝 <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                {t('landing.integration_title')}
              </span>
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              <span dangerouslySetInnerHTML={{ __html: t('landing.integration_description') }} />
            </p>
          </motion.div>

          {/* Integration Flow */}
          <motion.div
            className="bg-white/90 backdrop-blur-sm rounded-3xl p-6 sm:p-8 shadow-xl border border-gray-200/50 mb-10"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="flex items-center justify-center space-x-4 sm:space-x-8 text-center">
              
              {/* Emma */}
              <div className="flex-1">
                <div className="w-16 h-16 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-white font-black text-xl">E</span>
                </div>
                <p className="text-sm font-semibold text-gray-700">
                  {t('landing.integration_emma_ai')}
                </p>
                <p className="text-xs text-gray-500">
                  {t('landing.integration_does_90')}
                </p>
              </div>

              {/* Arrow */}
              <motion.div
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <span className="text-2xl text-[#3018ef]">→</span>
              </motion.div>

              {/* Human */}
              <div className="flex-1">
                <div className="w-16 h-16 bg-gradient-to-r from-[#10b981] to-[#059669] rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-white font-black text-xl">👤</span>
                </div>
                <p className="text-sm font-semibold text-gray-700">
                  {t('landing.integration_expert')}
                </p>
                <p className="text-xs text-gray-500">
                  {t('landing.integration_when_needed')}
                </p>
              </div>

              {/* Arrow */}
              <motion.div
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
              >
                <span className="text-2xl text-[#dd3a5a]">→</span>
              </motion.div>

              {/* Result */}
              <div className="flex-1">
                <div className="w-16 h-16 bg-gradient-to-r from-[#f59e0b] to-[#d97706] rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-white font-black text-xl">✨</span>
                </div>
                <p className="text-sm font-semibold text-gray-700">
                  {t('landing.integration_result')}
                </p>
                <p className="text-xs text-gray-500">
                  {t('landing.integration_perfect')}
                </p>
              </div>

            </div>
          </motion.div>

          {/* Scenarios Grid */}
          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {integrationScenarios.map((scenario, index) => (
              <motion.div
                key={index}
                className="bg-white/80 backdrop-blur-sm rounded-2xl p-5 shadow-lg border border-gray-200/50 text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
              >
                <span className="text-3xl mb-3 block">{scenario.icon}</span>
                <h3 className="text-lg font-bold text-gray-800 mb-2">
                  {scenario.title}
                </h3>
                <p className="text-sm text-gray-600 mb-3">
                  {scenario.description}
                </p>
                <p className="text-xs font-semibold text-[#3018ef]">
                  ✅ {scenario.solution}
                </p>
              </motion.div>
            ))}
          </div>

          {/* Bottom Message */}
          <motion.div
            className="text-center mt-8"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <p className="text-base text-gray-600 max-w-2xl mx-auto">
              <span dangerouslySetInnerHTML={{ __html: t('landing.integration_best_worlds') }} />
            </p>
          </motion.div>

        </div>
      </div>
    </section>
  )
}
