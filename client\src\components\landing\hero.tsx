import { motion, AnimatePresence } from "framer-motion";
import { Check } from "lucide-react";
import { useState, useEffect, useMemo } from "react";
import { Link } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

export default function Hero() {
  const { t, currentLanguage } = useLanguage();
  const [titleNumber, setTitleNumber] = useState(0);
  const benefitWords = useMemo(
    () => Array.isArray(t('landing.hero.title_words'))
      ? (t('landing.hero.title_words') as unknown) as string[]
      : [],
    [t],
  );

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (titleNumber === benefitWords.length - 1) {
        setTitleNumber(0);
      } else {
        setTitleNumber(titleNumber + 1);
      }
    }, 2500);
    return () => clearTimeout(timeoutId);
  }, [titleNumber, benefitWords]);

  return (
    <section className="relative pt-44 sm:pt-48 lg:pt-52 pb-24 overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="max-w-xl mx-auto md:mx-0"
          >
            <motion.span
              className="inline-block text-sm font-bold bg-blue-100 text-blue-600 px-4 py-1 rounded-full border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] mb-6"
              whileHover={{ scale: 1.05 }}
            >
              {t('landing.ai_powered_marketing')}
            </motion.span>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-black leading-tight mb-6 text-center md:text-left">
              <motion.span
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="block"
              >
                {t('landing.hero.title_prefix')}
              </motion.span>
              <div className="h-[70px] overflow-hidden">
                <AnimatePresence mode="wait">
                  <motion.span
                    key={titleNumber}
                    initial={{ opacity: 0, y: 40 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -40 }}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 20,
                    }}
                    className="block text-purple-600 text-6xl font-black"
                  >
                    {benefitWords[titleNumber]}
                  </motion.span>
                </AnimatePresence>
              </div>
            </h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="text-lg sm:text-xl mb-8 font-medium text-center md:text-left"
            >
              {t('landing.hero.description')}
            </motion.p>

            {/* Benefit cards */}
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mb-8">
              {(t('landing.hero.benefit_cards') as unknown as Array<{title: string, icon: string}>).map((item, index) => (
                <motion.div
                  key={index}
                  className="bg-white p-3 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
                  whileHover={{
                    y: -5,
                    boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
                  }}
                >
                  <div className="text-2xl mb-1">{item.icon}</div>
                  <div className="font-bold text-sm">{item.title}</div>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.9 }}
              className="flex flex-col sm:flex-row gap-4 items-center justify-center md:justify-start"
            >
              <Link href="/auth">
                <motion.button
                  className="bg-blue-500 text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-blue-600 transition-all duration-300"
                  whileHover={{
                    y: -5,
                    boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
                  }}
                  whileTap={{
                    y: 0,
                    boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)",
                  }}
                >
                  {t('landing.hero.cta_primary')}
                </motion.button>
              </Link>
              <motion.button
                className="bg-white text-black font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-gray-100 transition-all duration-300"
                whileHover={{
                  y: -5,
                  boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
                }}
                whileTap={{
                  y: 0,
                  boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)",
                }}
              >
                {t('landing.hero.cta_secondary')} →
              </motion.button>
            </motion.div>


          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="relative w-full max-w-lg mx-auto"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-pink-200 via-purple-200 to-blue-200 rounded-3xl transform rotate-2 scale-105 opacity-70"></div>
            <div className="bg-white p-6 rounded-2xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] relative">
              <div className="aspect-w-16 aspect-h-9 mb-4">
                <div className="w-full h-full bg-gray-200 rounded-xl border-2 border-black flex items-center justify-center">
                  <span className="text-gray-500 font-bold text-center">
                    Visualización de Agentes IA
                  </span>
                </div>
              </div>
              <div className="flex flex-wrap gap-4 mb-4">
                <div className="flex-1 h-10 bg-gray-200 rounded-lg border-2 border-black flex items-center justify-center">
                  <span className="font-bold text-xs text-gray-500">
                    AI Copywriter
                  </span>
                </div>
                <div className="flex-1 h-10 bg-gray-200 rounded-lg border-2 border-black flex items-center justify-center">
                  <span className="font-bold text-xs text-gray-500">
                    AI Designer
                  </span>
                </div>
              </div>
              <div className="h-20 bg-gray-200 rounded-lg border-2 border-black mb-4 flex items-center justify-center">
                <span className="font-bold text-xs text-gray-500">
                  AI Team Dashboard
                </span>
              </div>
              <div className="flex gap-2">
                <div className="w-10 h-10 bg-blue-200 rounded-full border-2 border-black flex items-center justify-center">
                  <Check size={16} className="text-blue-600" />
                </div>
                <div className="w-10 h-10 bg-green-200 rounded-full border-2 border-black flex items-center justify-center">
                  <Check size={16} className="text-green-600" />
                </div>
                <div className="w-10 h-10 bg-yellow-200 rounded-full border-2 border-black flex items-center justify-center">
                  <Check size={16} className="text-yellow-600" />
                </div>
                <div className="w-10 h-10 bg-pink-200 rounded-full border-2 border-black flex items-center justify-center">
                  <Check size={16} className="text-pink-600" />
                </div>
              </div>
            </div>

            <motion.div
              className="absolute -bottom-8 -left-8 md:-bottom-10 md:-left-10 bg-yellow-300 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] p-3 md:p-4 max-w-[160px] md:max-w-[180px]"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1 }}
              whileHover={{
                y: -5,
                boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
              }}
            >
              <div className="font-black text-sm mb-1">Trabajo 24/7</div>
              <div className="text-xs">Sin descansos, vacaciones o turnos</div>
            </motion.div>

            <motion.div
              className="absolute -top-8 -right-8 md:-top-10 md:-right-10 bg-green-300 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] p-3 md:p-4 max-w-[160px] md:max-w-[180px]"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.1 }}
              whileHover={{
                y: -5,
                boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
              }}
            >
              <div className="font-black text-sm mb-1">90% Menos Costo</div>
              <div className="text-xs">Que equipos humanos tradicionales</div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
