"use client";

import { motion } from "framer-motion";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { PRICE_RANGES } from '../constants';
import { SmartFormData } from '../types';

interface PricingStepProps {
  formData: SmartFormData;
  updateFormData: (field: keyof SmartFormData, value: string) => void;
}

export function PricingStep({ formData, updateFormData }: PricingStepProps) {
  return (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium text-gray-700 mb-4 block">
          Rango de precio de tu producto/servicio
        </Label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {PRICE_RANGES.map((price) => (
            <motion.button
              key={price.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => updateFormData("price_range", price.id)}
              className={`
                p-4 rounded-lg border-2 text-center transition-all
                ${formData.price_range === price.id
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : `border-gray-200 hover:border-gray-300 ${price.color}`
                }
              `}
            >
              <div className="font-bold text-lg text-gray-900">{price.name}</div>
              <div className="text-sm text-gray-600">{price.range}</div>
            </motion.button>
          ))}
        </div>
      </div>

      {formData.price_range && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-3"
        >
          <Label htmlFor="unique_value" className="text-base font-medium text-gray-700">
            ¿Cuál es tu propuesta de valor única? (Opcional)
          </Label>
          <Input
            id="unique_value"
            placeholder="Ej: La única plataforma que integra IA para automatizar completamente el proceso..."
            value={formData.unique_value}
            onChange={(e) => updateFormData("unique_value", e.target.value)}
            className="text-lg py-3 border-2 focus:border-blue-500"
          />
          <div className="text-sm text-gray-500">
            💡 ¿Qué te hace diferente de la competencia?
          </div>
        </motion.div>
      )}
    </div>
  );
}
