/**
 * SEO & GPT Optimizer™ - Project Card Component
 * Individual project card with actions and metrics
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  FileText,
  Calendar,
  Target,
  TrendingUp,
  TrendingDown,
  MoreVertical,
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  Play,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

import { SEOGPTProject, ProjectStatus, ContentType } from '../../../types/seo-gpt-optimizer';
import GPTRankMeter from '../shared/GPTRankMeter';

interface ProjectCardProps {
  project: SEOGPTProject;
  onEdit?: (project: SEOGPTProject) => void;
  onDelete?: (projectId: string) => void;
  onDuplicate?: (project: SEOGPTProject) => void;
  onOpen?: (projectId: string) => void;
  onStatusChange?: (projectId: string, status: ProjectStatus) => void;
  className?: string;
}

const statusConfig = {
  [ProjectStatus.DRAFT]: {
    color: 'bg-gray-100 text-gray-700 border-gray-200',
    icon: Edit,
    label: 'Borrador'
  },
  [ProjectStatus.RESEARCHING]: {
    color: 'bg-yellow-100 text-yellow-700 border-yellow-200',
    icon: Clock,
    label: 'Investigando'
  },
  [ProjectStatus.WRITING]: {
    color: 'bg-blue-100 text-blue-700 border-blue-200',
    icon: FileText,
    label: 'Escribiendo'
  },
  [ProjectStatus.OPTIMIZING]: {
    color: 'bg-purple-100 text-purple-700 border-purple-200',
    icon: Target,
    label: 'Optimizando'
  },
  [ProjectStatus.COMPLETED]: {
    color: 'bg-green-100 text-green-700 border-green-200',
    icon: CheckCircle,
    label: 'Completado'
  },
  [ProjectStatus.PUBLISHED]: {
    color: 'bg-emerald-100 text-emerald-700 border-emerald-200',
    icon: ExternalLink,
    label: 'Publicado'
  }
};

const contentTypeIcons = {
  [ContentType.ARTICLE]: FileText,
  [ContentType.GUIDE]: Target,
  [ContentType.TUTORIAL]: Play,
  [ContentType.COMPARISON]: TrendingUp,
  [ContentType.LIST]: CheckCircle,
  [ContentType.FAQ]: AlertCircle
};

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onEdit,
  onDelete,
  onDuplicate,
  onOpen,
  onStatusChange,
  className = ''
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const statusInfo = statusConfig[project.status];
  const StatusIcon = statusInfo.icon;
  const ContentTypeIcon = contentTypeIcons[project.content_type];

  // Calculate score change
  const scoreChange = project.current_gpt_rank_score - (project.best_gpt_rank_score || project.current_gpt_rank_score);
  const hasImprovement = scoreChange > 0;
  const hasDecline = scoreChange < 0;

  // Calculate progress to target
  const progressToTarget = (project.current_gpt_rank_score / project.target_gpt_rank_score) * 100;
  const targetAchieved = project.current_gpt_rank_score >= project.target_gpt_rank_score;

  // Calculate days since last update
  const daysSinceUpdate = Math.floor(
    (new Date().getTime() - new Date(project.updated_at).getTime()) / (1000 * 60 * 60 * 24)
  );

  const handleCardClick = () => {
    if (onOpen) {
      onOpen(project.project_id);
    }
  };

  const handleMenuAction = (action: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(false);

    switch (action) {
      case 'edit':
        onEdit?.(project);
        break;
      case 'delete':
        if (window.confirm('¿Estás seguro de que quieres eliminar este proyecto?')) {
          onDelete?.(project.project_id);
        }
        break;
      case 'duplicate':
        onDuplicate?.(project);
        break;
      case 'open':
        onOpen?.(project.project_id);
        break;
    }
  };

  const handleStatusChange = (newStatus: ProjectStatus, e: React.MouseEvent) => {
    e.stopPropagation();
    onStatusChange?.(project.project_id, newStatus);
  };

  return (
    <motion.div
      className={`bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-md hover:border-gray-200 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleCardClick}
      whileHover={{ y: -2 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4 flex-1">
            {/* Content Type Icon */}
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
              <ContentTypeIcon className="w-6 h-6 text-white" />
            </div>

            {/* Project Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="text-lg font-bold text-gray-900 truncate">{project.title}</h3>
                <span className={`px-3 py-1 rounded-full text-xs font-medium border ${statusInfo.color}`}>
                  <StatusIcon className="w-3 h-3 inline mr-1" />
                  {statusInfo.label}
                </span>
              </div>
              
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{project.topic}</p>
              
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <div className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  <span>
                    {daysSinceUpdate === 0 ? 'Hoy' : 
                     daysSinceUpdate === 1 ? 'Ayer' : 
                     `Hace ${daysSinceUpdate} días`}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="w-3 h-3" />
                  <span>{project.word_count} palabras</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="capitalize">{project.content_type}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Actions Menu */}
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowMenu(!showMenu);
              }}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <MoreVertical className="w-4 h-4" />
            </button>

            {showMenu && (
              <motion.div
                className="absolute right-0 top-full mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-10"
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
              >
                <button
                  onClick={(e) => handleMenuAction('open', e)}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  Abrir proyecto
                </button>
                <button
                  onClick={(e) => handleMenuAction('edit', e)}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                >
                  <Edit className="w-4 h-4" />
                  Editar
                </button>
                <button
                  onClick={(e) => handleMenuAction('duplicate', e)}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                >
                  <Copy className="w-4 h-4" />
                  Duplicar
                </button>
                <hr className="my-2" />
                <button
                  onClick={(e) => handleMenuAction('delete', e)}
                  className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                >
                  <Trash2 className="w-4 h-4" />
                  Eliminar
                </button>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Metrics */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          {/* GPT Rank Score */}
          <div className="flex items-center gap-4">
            <GPTRankMeter
              score={project.current_gpt_rank_score}
              grade={project.current_gpt_rank_score >= 90 ? 'A+' : 
                     project.current_gpt_rank_score >= 80 ? 'A' : 
                     project.current_gpt_rank_score >= 70 ? 'B' : 'C'}
              previousScore={project.best_gpt_rank_score}
              size="sm"
              showDetails={false}
              animated={isHovered}
            />
            
            <div>
              <div className="text-sm font-medium text-gray-900">
                {project.current_gpt_rank_score.toFixed(1)} / 100
              </div>
              <div className="text-xs text-gray-500">GPT Rank Score</div>
              {(hasImprovement || hasDecline) && (
                <div className={`flex items-center gap-1 text-xs ${
                  hasImprovement ? 'text-green-600' : 'text-red-600'
                }`}>
                  {hasImprovement ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                  <span>{hasImprovement ? '+' : ''}{scoreChange.toFixed(1)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Target Progress */}
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900 mb-1">
              Objetivo: {project.target_gpt_rank_score}
            </div>
            <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
              <motion.div
                className={`h-full transition-all duration-500 ${
                  targetAchieved ? 'bg-green-500' : 'bg-blue-500'
                }`}
                initial={{ width: 0 }}
                animate={{ width: `${Math.min(progressToTarget, 100)}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
              />
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {progressToTarget.toFixed(0)}% completado
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-3 bg-gray-50 rounded-xl">
            <div className="text-lg font-bold text-gray-900">{project.content_length || 0}</div>
            <div className="text-xs text-gray-600">Caracteres</div>
          </div>

          <div className="p-3 bg-gray-50 rounded-xl">
            <div className="text-lg font-bold text-gray-900">
              {Math.ceil((project.word_count || 0) / 200)}
            </div>
            <div className="text-xs text-gray-600">Min lectura</div>
          </div>
          
          <div className="p-3 bg-gray-50 rounded-xl">
            <div className="text-lg font-bold text-gray-900">
              {project.seo_score ? project.seo_score.toFixed(0) : '0'}
            </div>
            <div className="text-xs text-gray-600">SEO Score</div>
          </div>
        </div>
      </div>

      {/* Footer Actions */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {/* Status Change Buttons */}
            {project.status === ProjectStatus.DRAFT && (
              <button
                onClick={(e) => handleStatusChange(ProjectStatus.RESEARCHING, e)}
                className="flex items-center gap-1 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-lg text-xs font-medium hover:bg-yellow-200 transition-colors duration-200"
              >
                <Play className="w-3 h-3" />
                Iniciar
              </button>
            )}
            
            {project.status === ProjectStatus.WRITING && (
              <button
                onClick={(e) => handleStatusChange(ProjectStatus.OPTIMIZING, e)}
                className="flex items-center gap-1 px-3 py-1 bg-purple-100 text-purple-700 rounded-lg text-xs font-medium hover:bg-purple-200 transition-colors duration-200"
              >
                <Target className="w-3 h-3" />
                Optimizar
              </button>
            )}
            
            {project.status === ProjectStatus.OPTIMIZING && (
              <button
                onClick={(e) => handleStatusChange(ProjectStatus.COMPLETED, e)}
                className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded-lg text-xs font-medium hover:bg-green-200 transition-colors duration-200"
              >
                <CheckCircle className="w-3 h-3" />
                Completar
              </button>
            )}
          </div>

          <div className="text-xs text-gray-500">
            Creado {new Date(project.created_at).toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Hover Overlay */}
      {isHovered && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        />
      )}
    </motion.div>
  );
};

export default ProjectCard;
