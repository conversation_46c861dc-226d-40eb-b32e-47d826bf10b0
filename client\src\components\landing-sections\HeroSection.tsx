"use client"

import { <PERSON> } from "wouter"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import Floating, { FloatingElement } from "@/components/ui/parallax-floating"
import { useLanguage } from "@/contexts/LanguageContext"
import { useState, useEffect } from "react"

const emmaImages = [
  {
    url: "/Anuncio.png",
    title: "Anuncio Publicitario",
    alt: "Anuncio"
  },
  {
    url: "/Astronauta.png",
    title: "Astronauta",
    alt: "Astronauta"
  },
  {
    url: "/Gato lentes.webp",
    title: "Gato con Lentes",
    alt: "Gato Lentes"
  },
  {
    url: "/Hombre real.jpeg",
    title: "Hombre Real",
    alt: "Hombre Real"
  },
  {
    url: "/Labial.jpg",
    title: "Labial",
    alt: "Labial"
  },
  {
    url: "/PHOTO-2025-06-30-19-02-40.jpg",
    title: "Foto Profesional",
    alt: "Foto Profesional"
  },
  {
    url: "/libro.jpg",
    title: "Libro",
    alt: "Libro"
  },
  {
    url: "/poster.jpg",
    title: "Poster",
    alt: "Poster"
  }
]

export function HeroSection() {
  const { t, currentLanguage } = useLanguage();

  // Palabras rotativas que venden emociones y beneficios para Emma
  const rotatingWords = currentLanguage === 'en' ? [
    { text: "incredible", emoji: "✨" },
    { text: "viral", emoji: "🚀" },
    { text: "successful", emoji: "💎" },
    { text: "unique", emoji: "🌟" },
    { text: "powerful", emoji: "⚡" },
    { text: "premium", emoji: "👑" },
    { text: "professional", emoji: "🎯" },
    { text: "epic", emoji: "🔥" },
    { text: "unstoppable", emoji: "💪" },
    { text: "fast", emoji: "⚡" },
    { text: "automatic", emoji: "🤖" },
    { text: "intelligent", emoji: "🧠" },
    { text: "profitable", emoji: "💰" }
  ] : [
    { text: "increíble", emoji: "✨" },
    { text: "viral", emoji: "🚀" },
    { text: "exitoso", emoji: "💎" },
    { text: "único", emoji: "🌟" },
    { text: "poderoso", emoji: "⚡" },
    { text: "premium", emoji: "👑" },
    { text: "profesional", emoji: "🎯" },
    { text: "épico", emoji: "🔥" },
    { text: "imparable", emoji: "💪" },
    { text: "rápido", emoji: "⚡" },
    { text: "automático", emoji: "🤖" },
    { text: "inteligente", emoji: "🧠" },
    { text: "rentable", emoji: "💰" }
  ];

  // Estado para la rotación de palabras
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  // Efecto para rotar las palabras automáticamente
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentWordIndex((prevIndex) => {
        const nextIndex = prevIndex === rotatingWords.length - 1 ? 0 : prevIndex + 1;
        console.log('🔄 Rotating word:', prevIndex, '->', nextIndex, rotatingWords[nextIndex].text, rotatingWords[nextIndex].emoji);
        return nextIndex;
      });
    }, 2500); // Cambia cada 2.5 segundos

    return () => clearInterval(interval);
  }, [rotatingWords.length]);

  console.log('🎯 Current word index:', currentWordIndex, 'Word:', rotatingWords[currentWordIndex].text, rotatingWords[currentWordIndex].emoji);

  console.log('🎯 HeroSection render - Current language:', currentLanguage);
  console.log('🎯 Hero prefix translation:', t('landing.hero_prefix'));
  console.log('🎯 Hero word translation:', t('landing.hero_word'));
  return (
    <section className="w-full min-h-screen relative bg-white overflow-hidden">
      {/* Floating Images Background */}
      <Floating
        className="absolute inset-0 w-full h-full z-[1] pointer-events-none"
      >
        {/* Floating Images - Balanced Radial Distribution */}
        {/* Top Left Quadrant */}
        <FloatingElement
          depth={0.5}
          className="z-[2] floating-img-1 absolute"
          style={{ top: '10%', left: '5%' }}
        >
          <motion.img
            src={emmaImages[0].url}
            alt={emmaImages[0].alt}
            className="w-20 h-16 sm:w-28 sm:h-20 md:w-32 md:h-24 lg:w-36 lg:h-28 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform -rotate-[8deg] shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          />
        </FloatingElement>

        {/* Top Center */}
        <FloatingElement
          depth={1}
          className="z-[2] floating-img-2"
          style={{ top: '8%', left: '50%', transform: 'translateX(-50%)' }}
        >
          <motion.img
            src={emmaImages[1].url}
            alt={emmaImages[1].alt}
            className="w-24 h-18 sm:w-32 sm:h-24 md:w-40 md:h-30 lg:w-48 lg:h-36 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform rotate-[5deg] shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          />
        </FloatingElement>

        {/* Bottom Left */}
        <FloatingElement
          depth={4}
          className="z-[2] floating-img-3"
          style={{ bottom: '15%', left: '12%' }}
        >
          <motion.img
            src={emmaImages[2].url}
            alt={emmaImages[2].alt}
            className="w-28 h-28 sm:w-36 sm:h-36 md:w-44 md:h-44 lg:w-52 lg:h-52 object-cover -rotate-[12deg] hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 }}
          />
        </FloatingElement>

        {/* Top Right Quadrant */}
        <FloatingElement
          depth={2}
          className="z-[2] floating-img-4"
          style={{ top: '15%', right: '8%' }}
        >
          <motion.img
            src={emmaImages[3].url}
            alt={emmaImages[3].alt}
            className="w-20 h-16 sm:w-28 sm:h-22 md:w-36 md:h-28 lg:w-44 lg:h-32 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[15deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.1 }}
          />
        </FloatingElement>

        {/* Bottom Right */}
        <FloatingElement
          depth={1}
          className="z-[2] floating-img-5"
          style={{ bottom: '15%', right: '12%' }}
        >
          <motion.img
            src={emmaImages[4].url}
            alt={emmaImages[4].alt}
            className="w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-56 lg:h-56 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[8deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.3 }}
          />
        </FloatingElement>

        {/* Middle Left */}
        <FloatingElement
          depth={3}
          className="z-[2] floating-img-6"
          style={{ top: '50%', left: '4%', transform: 'translateY(-50%)' }}
        >
          <motion.img
            src={emmaImages[5].url}
            alt={emmaImages[5].alt}
            className="w-18 h-14 sm:w-24 sm:h-18 md:w-32 md:h-24 lg:w-36 lg:h-28 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[12deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
          />
        </FloatingElement>

        {/* Middle Right */}
        <FloatingElement
          depth={2.5}
          className="z-[2] floating-img-7"
          style={{ top: '50%', right: '4%', transform: 'translateY(-50%)' }}
        >
          <motion.img
            src={emmaImages[6].url}
            alt={emmaImages[6].alt}
            className="w-20 h-16 sm:w-28 sm:h-20 md:w-36 md:h-28 lg:w-40 lg:h-32 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl -rotate-[10deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.7 }}
          />
        </FloatingElement>

        {/* Bottom Center */}
        <FloatingElement
          depth={1.5}
          className="z-[2] floating-img-8"
          style={{ bottom: '8%', left: '50%', transform: 'translateX(-50%)' }}
        >
          <motion.img
            src={emmaImages[7].url}
            alt={emmaImages[7].alt}
            className="w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl rotate-[6deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.9 }}
          />
        </FloatingElement>

        {/* Top Left Inner */}
        <FloatingElement
          depth={3.5}
          className="z-[2] floating-img-9"
          style={{ top: '25%', left: '20%' }}
        >
          <motion.video
            autoPlay
            loop
            muted
            playsInline
            className="w-20 h-20 sm:w-28 sm:h-28 md:w-36 md:h-36 lg:w-44 lg:h-44 object-cover hover:scale-105 duration-200 cursor-pointer transition-transform shadow-2xl -rotate-[18deg] rounded-xl pointer-events-auto"
            style={{
              backfaceVisibility: 'hidden',
              WebkitBackfaceVisibility: 'hidden',
              transform: 'translateZ(0)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2.1 }}
          >
            <source src="/video gato brincando.mp4" type="video/mp4" />
          </motion.video>
        </FloatingElement>
      </Floating>

      {/* Main Content - HERO ARRIBA */}
      <div className="absolute inset-0 flex flex-col justify-center items-center z-10 pt-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 md:px-8 text-center">
        <motion.div
          className="text-center mb-8 sm:mb-12"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 30 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
        >
          {/* Título Principal */}
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight text-gray-900 leading-tight mb-6">
            {currentLanguage === 'en' ? 'Create' : 'Crea'} {currentLanguage === 'en' ? 'content' : 'contenido'}{" "}
            <span className="bg-gradient-to-r from-[#3018ef] via-[#8b5cf6] to-[#dd3a5a] bg-clip-text text-transparent font-black">
              {rotatingWords[currentWordIndex].text}
            </span>
            {" "}
            <span className="text-gray-900">
              {rotatingWords[currentWordIndex].emoji}
            </span>
            {" "}{currentLanguage === 'en' ? 'with AI' : 'con IA'}
          </h1>
        </motion.div>

        <motion.div
          className="text-center mb-12 sm:mb-16"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 30 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.6 }}
        >
          <p className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-gray-800 mb-4 max-w-4xl mx-auto leading-tight">
            <strong>{currentLanguage === 'en' ? "The world's first virtual marketing agency" : "La primera agencia virtual de marketing del mundo"}</strong>
          </p>
          <p className="text-lg sm:text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium">
            {currentLanguage === 'en' ? 'Specialized AI • Real Results • Available 24/7' : 'IA especializada • Resultados reales • Disponible 24/7'}
          </p>
        </motion.div>

        <motion.div
          className="flex flex-col sm:flex-row justify-center gap-6 sm:gap-8 items-center"
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: 40 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.8 }}
        >
          {/* Botón Principal */}
          <motion.div
            whileHover={{
              scale: 1.05,
              y: -2
            }}
            whileTap={{ scale: 0.98 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            <Link href="/register">
              <Button
                variant="red"
                size="lg"
                className="relative overflow-hidden text-lg font-bold tracking-tight px-10 py-5 rounded-2xl shadow-2xl w-full sm:w-auto min-w-[220px] bg-gradient-to-r from-[#dd3a5a] to-[#ff6b8a] hover:from-[#c73650] hover:to-[#dd3a5a] border-0 text-white group"
              >
                <span className="relative z-10 flex items-center justify-center gap-2">
                  {currentLanguage === 'en' ? 'Make Marketing Effortless' : 'Haz Marketing Sin Esfuerzo'}
                  <span className="text-xl">→</span>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Button>
            </Link>
          </motion.div>

          {/* Botón Secundario */}
          <motion.div
            whileHover={{
              scale: 1.05,
              y: -2
            }}
            whileTap={{ scale: 0.98 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            <Link href="#demo">
              <Button
                variant="outline"
                size="lg"
                className="relative overflow-hidden text-lg font-bold tracking-tight px-10 py-5 rounded-2xl border-2 border-[#3018ef] text-[#3018ef] hover:bg-[#3018ef] hover:text-white transition-all duration-300 w-full sm:w-auto min-w-[220px] bg-white/80 backdrop-blur-sm shadow-xl group"
              >
                <span className="relative z-10 flex items-center justify-center gap-2">
                  💎 {currentLanguage === 'en' ? 'Help Me Sell More' : 'Ayúdame a Vender Más'}
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef]/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Button>
            </Link>
          </motion.div>
        </motion.div>
        </div>
      </div>


    </section>
  )
}
