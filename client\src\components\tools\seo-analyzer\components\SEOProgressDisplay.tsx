import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { X, Clock, Globe, FileText } from "lucide-react";
import { SEOProgressDisplayProps } from "../types/seo";
import { formatDuration } from "../utils/seo-helpers";

export const SEOProgressDisplay: React.FC<SEOProgressDisplayProps> = ({
  isLoading,
  progressLoading,
  persistentLoading,
  persistentProgress,
  onCancel,
}) => {
  // Don't render if no loading state
  if (!isLoading && !progressLoading && !persistentLoading && !persistentProgress) {
    return null;
  }

  // Simple loading for quick analysis
  if (isLoading && !persistentProgress) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <div className="text-center">
              <h3 className="text-lg font-semibold">Analizando página...</h3>
              <p className="text-muted-foreground">
                Esto tomará unos segundos
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Persistent analysis progress
  if (persistentProgress || persistentLoading) {
    const progress = persistentProgress?.progress || 0;
    const status = persistentProgress?.status || "running";
    const currentStep = persistentProgress?.current_step || "Iniciando análisis...";
    const pagesDiscovered = persistentProgress?.pages_discovered || 0;
    const pagesAnalyzed = persistentProgress?.pages_analyzed || 0;

    return (
      <Card className="w-full">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl">Análisis en progreso</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              className="text-red-600 hover:text-red-700"
            >
              <X className="h-4 w-4 mr-1" />
              Cancelar
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="font-medium">Progreso general</span>
              <span className="text-muted-foreground">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-3" />
          </div>

          {/* Current Status */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <div className="animate-pulse h-2 w-2 bg-blue-600 rounded-full"></div>
              <span className="font-medium">Estado actual:</span>
              <span className="text-muted-foreground">{currentStep}</span>
            </div>

            {/* Pages Info */}
            {pagesDiscovered > 0 && (
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium">Páginas encontradas</p>
                    <p className="text-lg font-bold text-blue-600">{pagesDiscovered}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-sm font-medium">Páginas analizadas</p>
                    <p className="text-lg font-bold text-green-600">{pagesAnalyzed}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Estimated Time */}
            {persistentProgress?.estimated_completion && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>
                  Tiempo estimado de finalización:{" "}
                  {new Date(persistentProgress.estimated_completion).toLocaleTimeString()}
                </span>
              </div>
            )}
          </div>

          {/* Status Messages */}
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-900 mb-2">
              💡 Análisis en segundo plano
            </h4>
            <p className="text-sm text-blue-700">
              Puedes cerrar esta página y volver más tarde. El análisis continuará
              ejecutándose en el servidor y podrás ver los resultados cuando esté
              completo.
            </p>
          </div>

          {/* Error State */}
          {status === "error" && persistentProgress?.error && (
            <div className="p-4 bg-red-50 rounded-lg border border-red-200">
              <h4 className="font-medium text-red-900 mb-2">Error en el análisis</h4>
              <p className="text-sm text-red-700">
                {persistentProgress.error.error}
              </p>
              {persistentProgress.error.details && (
                <p className="text-xs text-red-600 mt-1">
                  {persistentProgress.error.details}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return null;
};
