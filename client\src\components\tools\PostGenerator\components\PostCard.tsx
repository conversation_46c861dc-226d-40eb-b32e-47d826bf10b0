import React, { useState, useMemo, useCallback } from "react";
import { motion } from "framer-motion";
import { Edit3, Download, Share2, Instagram, Linkedin, Facebook, Twitter, Hash, MessageCircle, Copy } from "lucide-react";
import { Button } from "@/components/ui/button";
import { GeneratedPost } from "../PostGenerator";

interface PostCardProps {
  post: GeneratedPost;
  onEdit: () => void;
  onExport: () => void;
  onShare: () => void;
  onGenerateMore?: () => void;
}

// Static animation objects to prevent re-creation on each render
const CARD_HOVER_ANIMATION = { y: -2 };
const CARD_INITIAL_ANIMATION = { opacity: 0, y: 20 };
const CARD_ANIMATE_ANIMATION = { opacity: 1, y: 0 };

const PostCard: React.FC<PostCardProps> = ({
  post,
  onEdit,
  onExport,
  onShare,
  onGenerateMore,
}) => {
  // State for image loading error handling
  const [imageError, setImageError] = useState(false);

  // Memoized dimension calculations to prevent recalculation on every render
  const imageDimensions = useMemo(() => {
    const platformDimensions = {
      instagram: { width: 480, height: 480, aspectRatio: 1 },
      facebook: { width: 520, height: 273, aspectRatio: 1.9 },
      linkedin: { width: 520, height: 272, aspectRatio: 1.91 },
      x: { width: 520, height: 292, aspectRatio: 1.78 },
      twitter: { width: 520, height: 292, aspectRatio: 1.78 },
      "instagram stories": { width: 320, height: 568, aspectRatio: 0.56 }
    };

    const platformKey = post.platform.toLowerCase();
    let result = platformDimensions[platformKey] || platformDimensions.instagram;

    // Calculate based on metadata dimensions if available
    const dimensions = post.metadata?.dimensions;
    if (dimensions && dimensions.includes('x')) {
      try {
        const [width, height] = dimensions.split('x').map(Number);
        if (width && height) {
          const aspectRatio = width / height;
          let displayWidth, displayHeight;

          if (aspectRatio > 1.5) {
            displayWidth = 520;
            displayHeight = Math.round(displayWidth / aspectRatio);
          } else if (aspectRatio < 0.8) {
            displayHeight = 580;
            displayWidth = Math.round(displayHeight * aspectRatio);
          } else {
            displayWidth = 480;
            displayHeight = Math.round(displayWidth / aspectRatio);
          }

          result = { width: displayWidth, height: displayHeight, aspectRatio };
        }
      } catch (error) {
        // Silent fallback to default dimensions
      }
    }

    return result;
  }, [post.platform, post.metadata?.dimensions]);

  // Memoized image error handler
  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  // Reset image error when imageUrl changes
  React.useEffect(() => {
    setImageError(false);
  }, [post.imageUrl]);
  // Memoized platform icon to prevent re-creation
  const platformIcon = useMemo(() => {
    switch (post.platform.toLowerCase()) {
      case "instagram":
        return <Instagram className="w-4 h-4" />;
      case "linkedin":
        return <Linkedin className="w-4 h-4" />;
      case "facebook":
        return <Facebook className="w-4 h-4" />;
      case "twitter":
      case "x":
        return <Twitter className="w-4 h-4" />;
      default:
        return <Instagram className="w-4 h-4" />;
    }
  }, [post.platform]);

  // Memoized platform color
  const platformColor = useMemo(() => {
    switch (post.platform.toLowerCase()) {
      case "instagram":
        return "from-pink-500 to-purple-600";
      case "linkedin":
        return "from-blue-600 to-blue-700";
      case "facebook":
        return "from-blue-500 to-blue-600";
      case "twitter":
      case "x":
        return "from-gray-800 to-black";
      default:
        return "from-pink-500 to-purple-600";
    }
  }, [post.platform]);

  // Memoized template color
  const templateColor = useMemo(() => {
    switch (post.template.toLowerCase()) {
      case "informativo":
        return "bg-blue-100 text-blue-800";
      case "motivacional":
        return "bg-green-100 text-green-800";
      case "educativo":
        return "bg-purple-100 text-purple-800";
      case "entretenimiento":
        return "bg-orange-100 text-orange-800";
      case "promocional":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }, [post.template]);

  return (
    <motion.div
      className="w-full max-w-4xl mx-auto bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300"
      whileHover={CARD_HOVER_ANIMATION}
      initial={CARD_INITIAL_ANIMATION}
      animate={CARD_ANIMATE_ANIMATION}
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg bg-gradient-to-r ${platformColor} text-white`}>
              {platformIcon}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 capitalize">
                {post.platform}
              </h3>
              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${templateColor}`}>
                {post.template}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              onClick={onEdit}
              variant="outline"
              size="sm"
              className="flex items-center hover:bg-[#3018ef] hover:text-white transition-colors"
            >
              <Edit3 className="w-4 h-4 mr-1" />
              Editar
            </Button>
            <Button
              onClick={onExport}
              variant="outline"
              size="sm"
              className="flex items-center hover:bg-[#dd3a5a] hover:text-white transition-colors"
            >
              <Download className="w-4 h-4 mr-1" />
              Exportar
            </Button>
            <Button
              onClick={onShare}
              variant="outline"
              size="sm"
              className="flex items-center hover:bg-green-600 hover:text-white transition-colors"
            >
              <Share2 className="w-4 h-4 mr-1" />
              Compartir
            </Button>
            <Button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log("🚀 PostCard: 'Más como este' button clicked!");
                console.log("🔍 onGenerateMore function:", onGenerateMore);
                console.log("🔍 onGenerateMore exists:", !!onGenerateMore);
                if (onGenerateMore) {
                  try {
                    onGenerateMore();
                    console.log("✅ onGenerateMore executed successfully");
                  } catch (error) {
                    console.error("❌ Error executing onGenerateMore:", error);
                  }
                } else {
                  console.error("❌ onGenerateMore function is not defined!");
                  alert("Error: La función 'más como este' no está disponible");
                }
              }}
              variant="outline"
              size="sm"
              className="flex items-center hover:bg-purple-600 hover:text-white transition-colors border-purple-300 text-purple-700"
            >
              <Copy className="w-4 h-4 mr-1" />
              Más como este
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Text Content */}
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <MessageCircle className="w-4 h-4 mr-2" />
                Contenido del Post
              </h4>
              <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-[#3018ef]">
                <p className="text-gray-800 leading-relaxed whitespace-pre-wrap">
                  {post.content}
                </p>
              </div>
            </div>

            {/* Hashtags */}
            {post.hashtags && post.hashtags.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <Hash className="w-4 h-4 mr-2" />
                  Hashtags
                </h4>
                <div className="flex flex-wrap gap-2">
                  {post.hashtags.map((hashtag, index) => (
                    <span
                      key={index}
                      className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                    >
                      #{hashtag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* CTA */}
            {post.cta && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Call to Action
                </h4>
                <div 
                  className="inline-block px-4 py-2 rounded-lg text-white font-medium"
                  style={{ backgroundColor: post.metadata.brandColor }}
                >
                  {post.cta}
                </div>
              </div>
            )}
          </div>

          {/* Visual Preview */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Vista Previa Visual
            </h4>
            
            {/* Platform indicator */}
            <div className="mb-4 text-center">
              <span className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                {post.platform} • {post.metadata?.dimensions || `${imageDimensions.width}x${imageDimensions.height}`}
              </span>
            </div>

            {/* Post preview with optimized image handling */}
            <div className="flex justify-center">
              <div
                className="rounded-lg overflow-hidden border border-gray-200 shadow-sm relative"
                style={{
                  width: `${imageDimensions.width}px`,
                  height: `${imageDimensions.height}px`,
                }}
              >
                {post.imageUrl && post.imageUrl.trim() !== "" && !imageError ? (
                  <img
                    src={post.imageUrl}
                    alt={`${post.platform} post image`}
                    className="w-full h-full object-cover block"
                    onError={handleImageError}
                    loading="lazy"
                  />
                ) : (
                  <div
                    className="w-full h-full flex items-center justify-center text-white font-bold text-lg"
                    style={{
                      background: `linear-gradient(135deg, ${post.metadata.brandColor} 0%, ${post.metadata.brandColor}dd 100%)`
                    }}
                  >
                    {post.metadata.businessName}
                  </div>
                )}
              </div>
            </div>

              {/* Social media preview */}
              <div className="space-y-2 mt-4">
                <div className="flex items-center space-x-2">
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold"
                    style={{ backgroundColor: post.metadata.brandColor }}
                  >
                    {post.metadata.businessName.charAt(0)}
                  </div>
                  <span className="font-semibold text-gray-800 text-sm">
                    {post.metadata.businessName}
                  </span>
                </div>

                <p className="text-gray-700 text-sm line-clamp-3">
                  {post.content.substring(0, 120)}...
                </p>

                {post.hashtags && post.hashtags.length > 0 && (
                  <p className="text-blue-600 text-xs">
                    {post.hashtags.slice(0, 3).map(tag => `#${tag}`).join(' ')}
                  </p>
                )}
              </div>

            {/* Metadata */}
            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Plataforma:</span>
                <span className="font-medium text-gray-800 capitalize">{post.platform}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Dimensiones originales:</span>
                <span className="font-medium text-gray-800">
                  {post.metadata?.dimensions || 'No especificadas'} px
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Dimensiones de vista:</span>
                <span className="font-medium text-gray-800">
                  {imageDimensions.width}x{imageDimensions.height} px
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Proporción:</span>
                <span className="font-medium text-gray-800">
                  {imageDimensions.aspectRatio.toFixed(2)}:1
                  {imageDimensions.aspectRatio === 1 ? ' (Cuadrada)' :
                   imageDimensions.aspectRatio > 1 ? ' (Horizontal)' : ' (Vertical)'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Tema:</span>
                <span className="font-medium text-gray-800">{post.metadata.theme}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Color de marca:</span>
                <div className="flex items-center space-x-2">
                  <div
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: post.metadata.brandColor }}
                  />
                  <span className="font-medium text-gray-800">{post.metadata.brandColor}</span>
                </div>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Tipo de contenido:</span>
                <span className={`font-medium ${post.metadata.imageGenerated ? 'text-blue-600' : 'text-purple-600'}`}>
                  {post.metadata.imageGenerated ? '🖼️ Visual' : '📝 Texto'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default PostCard;
