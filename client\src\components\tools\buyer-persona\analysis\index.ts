/**
 * Analysis Components Index
 *
 * Exports all modular analysis components for buyer persona premium features.
 * Following SOLID principles and component separation.
 */

export { EmotionalAnalysis } from './emotional-analysis';
export { TimingAnalysis } from './timing-analysis';
export { LeadScoring } from './lead-scoring';
export { GeographicAnalysis } from './geographic-analysis';
export { ObjectionsAnalysis } from './objections-analysis';
export { DecisionInfluencers } from './decision-influencers';
export { ContentPreferences } from './content-preferences';
export { ConversationInsights } from './conversation-insights';

// Type definitions for analysis components
export interface AnalysisComponentProps {
  delay?: number;
}

export interface EmotionalAnalysisData {
  primary_emotions?: string[];
  stress_triggers?: string[];
  motivation_drivers?: string[];
  decision_making_style?: string;
  communication_tone_preference?: string;
  trust_building_factors?: string[];
  emotional_barriers?: string[];
  excitement_triggers?: string[];
}

export interface TimingAnalysisData {
  optimal_contact_windows?: {
    primary?: { day: string; time: string; probability: number };
    secondary?: { day: string; time: string; probability: number };
    tertiary?: { day: string; time: string; probability: number };
  };
  avoid_periods?: Array<{ period: string; reason: string }>;
  follow_up_cadence?: {
    initial_response_time?: string;
    follow_up_intervals?: string[];
    max_attempts?: number;
    escalation_timing?: string;
  };
  seasonal_patterns?: {
    high_activity_periods?: string[];
    low_activity_periods?: string[];
    budget_cycles?: string;
  };
  industry_timing?: {
    business_cycles?: string;
    decision_seasons?: string[];
    competitive_timing?: string;
  };
}

export interface LeadScoringData {
  overall_score?: number;
  scoring_factors?: {
    budget_fit?: number;
    authority_level?: number;
    need_urgency?: number;
    solution_fit?: number;
    timing_alignment?: number;
  };
  qualification_status?: string;
  next_best_action?: string;
}

export interface PurchaseProbabilityData {
  score?: number;
  timeline?: string;
  confidence_level?: string;
}
