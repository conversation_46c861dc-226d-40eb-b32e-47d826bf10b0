import { Link } from 'wouter'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Rocket, Clock, DollarSign } from 'lucide-react'
import { preciosEmma } from './CalculadoraLogic'
import { useLanguage } from '@/contexts/LanguageContext'

interface CTAFinalProps {
  moneda: 'MXN' | 'USD' | 'EUR'
  ahorroAnual: number
}

export function CTAFinal({ moneda, ahorroAnual }: CTAFinalProps) {
  const { t } = useLanguage()
  const formatCurrency = (amount: number) => {
    const validAmount = isNaN(amount) || amount === null || amount === undefined ? 0 : Math.round(amount)

    if (moneda === 'MXN') {
      return `$${validAmount.toLocaleString('es-MX')} MXN`
    } else if (moneda === 'USD') {
      return `$${validAmount.toLocaleString('en-US')} USD`
    } else if (moneda === 'EUR') {
      return `€${validAmount.toLocaleString('es-ES')} EUR`
    }

    return `${validAmount.toLocaleString()}`
  }

  const urgencias = t('calculadora.cta.urgency_messages')

  return (
    <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-3xl p-8 text-white shadow-2xl">
      <div className="text-center mb-8">
        <div className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <Rocket className="text-white" size={40} />
        </div>
        <h3 className="text-3xl font-bold mb-4">
          {t('calculadora.cta.title')} {formatCurrency(ahorroAnual)} {t('calculadora.cta.title_suffix')}
        </h3>
        <p className="text-xl opacity-90 mb-2">
          <span className="text-yellow-300">{t('calculadora.cta.competition_warning')}</span>
        </p>
        <p className="text-lg opacity-90 mb-6">
          {t('calculadora.cta.competition_question')}
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 mb-8">
        {/* Lo que obtienes AHORA */}
        <div className="bg-white/10 rounded-2xl p-6">
          <h4 className="text-lg font-bold mb-4 flex items-center gap-2">
            <Clock className="text-white" size={20} />
            {t('calculadora.cta.start_today')}
          </h4>
          <ul className="space-y-2 text-sm">
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              {t('calculadora.cta.immediate_access')}
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              {t('calculadora.cta.unlimited_posts')}
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              {t('calculadora.cta.ai_24_7')}
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              {t('calculadora.cta.no_contracts')}
            </li>
          </ul>
        </div>

        {/* Urgencia */}
        <div className="bg-white/10 rounded-2xl p-6">
          <h4 className="text-lg font-bold mb-4 flex items-center gap-2">
            <DollarSign className="text-white" size={20} />
            {t('calculadora.cta.why_wait')}
          </h4>
          <ul className="space-y-2 text-sm">
            {urgencias.map((urgencia, index) => (
              <li key={index} className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-300 rounded-full"></div>
                {urgencia}
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="text-center">
        <div className="mb-6">
          <div className="text-sm opacity-75 mb-2">{t('calculadora.cta.launch_price')}</div>
          <div className="text-4xl font-bold mb-2">
            {formatCurrency(preciosEmma[moneda].mensual)}{t('calculadora.cta.per_month')}
          </div>
          <div className="text-sm opacity-75">
            {t('calculadora.cta.vs_current')} {formatCurrency(ahorroAnual > 0 ? (ahorroAnual / 12) + preciosEmma[moneda].mensual : 5000)}{t('calculadora.cta.vs_current_suffix')}
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/dashboard">
            <Button 
              variant="white" 
              className="px-8 py-4 text-lg font-bold rounded-2xl hover:scale-105 transition-transform"
            >
              {t('calculadora.cta.start_now')}
            </Button>
          </Link>
          <Link href="/">
            <Button
              variant="outline"
              className="px-8 py-4 text-lg font-bold rounded-2xl border-white text-white hover:bg-white/10"
            >
              {t('calculadora.cta.learn_more')}
            </Button>
          </Link>
        </div>

        <div className="mt-6 text-sm opacity-75">
          {t('calculadora.cta.quick_setup')} • {t('calculadora.cta.cancel_anytime')} • {t('calculadora.cta.satisfaction_guarantee')}
        </div>
      </div>
    </div>
  )
}
