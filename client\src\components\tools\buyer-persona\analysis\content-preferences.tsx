"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, Video, Calendar, Zap, Clock, Target } from "lucide-react";

interface ContentPreferencesProps {
  contentData: {
    formats?: string[];
    topics?: string[];
    tone?: string;
    emotional_hooks?: string[];
    content_timing?: string;
  };
  delay?: number;
}

export function ContentPreferences({ contentData, delay = 0.7 }: ContentPreferencesProps) {
  if (!contentData || Object.keys(contentData).length === 0) {
    return null;
  }

  const getFormatIcon = (format: string) => {
    const formatLower = format.toLowerCase();
    if (formatLower.includes('video')) return <Video className="h-4 w-4" />;
    if (formatLower.includes('pdf') || formatLower.includes('documento')) return <FileText className="h-4 w-4" />;
    if (formatLower.includes('webinar') || formatLower.includes('evento')) return <Calendar className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const getToneColor = (tone: string) => {
    const toneLower = tone.toLowerCase();
    if (toneLower.includes('profesional')) return "bg-blue-100 text-blue-800";
    if (toneLower.includes('casual')) return "bg-green-100 text-green-800";
    if (toneLower.includes('técnico')) return "bg-purple-100 text-purple-800";
    if (toneLower.includes('consultivo')) return "bg-indigo-100 text-indigo-800";
    return "bg-gray-100 text-gray-800";
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay }}
    >
      <Card className="bg-gradient-to-br from-emerald-50 to-teal-100 border-emerald-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-emerald-800">
            <FileText className="h-5 w-5" />
            Preferencias de Contenido
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          
          {/* Formatos Preferidos */}
          {contentData.formats && contentData.formats.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Video className="h-4 w-4" />
                Formatos Preferidos
              </h4>
              <div className="grid grid-cols-2 gap-3">
                {contentData.formats.map((format, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                    {getFormatIcon(format)}
                    <span className="text-sm text-gray-700">{format}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Temas de Interés */}
          {contentData.topics && contentData.topics.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Target className="h-4 w-4" />
                Temas de Interés
              </h4>
              <div className="flex flex-wrap gap-2">
                {contentData.topics.map((topic, index) => (
                  <Badge key={index} className="bg-emerald-100 text-emerald-800">
                    {topic}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Tono de Comunicación */}
          {contentData.tone && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3">
                Tono de Comunicación
              </h4>
              <Badge className={getToneColor(contentData.tone)} variant="outline">
                {contentData.tone}
              </Badge>
            </div>
          )}

          {/* Ganchos Emocionales */}
          {contentData.emotional_hooks && contentData.emotional_hooks.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Ganchos Emocionales
              </h4>
              <div className="space-y-2">
                {contentData.emotional_hooks.map((hook, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">{hook}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Timing de Contenido */}
          {contentData.content_timing && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Timing Óptimo para Contenido
              </h4>
              <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                <p className="text-sm text-blue-700">{contentData.content_timing}</p>
              </div>
            </div>
          )}

          {/* Estrategia de Contenido */}
          <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg p-4 border border-emerald-200">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-4 w-4 text-emerald-600" />
              <span className="font-semibold text-emerald-800">Estrategia de Contenido:</span>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-emerald-700">
                <strong>Formatos prioritarios:</strong> {contentData.formats?.slice(0, 2).join(', ') || 'No especificado'}
              </p>
              <p className="text-sm text-emerald-700">
                <strong>Enfoque emocional:</strong> {contentData.emotional_hooks?.slice(0, 2).join(', ') || 'Profesional'}
              </p>
              <p className="text-sm text-emerald-700">
                <strong>Tono recomendado:</strong> {contentData.tone || 'Profesional y consultivo'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
