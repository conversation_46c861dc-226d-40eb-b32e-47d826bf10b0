/**
 * GoogleDocsEditor - Real Google Docs-style editor powered by Etherpad
 * Professional interface with Emma branding and real-time collaboration
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight,
  List, ListOrdered, Link, Type, Image, Wand2, Sparkles,
  MoreHorizontal, Undo, Redo, Printer, Share, Users, MessageCircle,
  History, Star, Folder, ChevronDown, Highlighter
} from 'lucide-react';
// import { etherpadService } from '../../../services/etherpadService'; // DISABLED - no external services

interface GoogleDocsEditorProps {
  content: string;
  onChange: (content: string) => void;
  onImageInsert?: () => void;
  projectId: string;
  className?: string;
  documentTitle?: string;
  onTitleChange?: (title: string) => void;
  collaborators?: Array<{
    id: string;
    name: string;
    avatar?: string;
    color: string;
  }>;
}

const GoogleDocsEditor: React.FC<GoogleDocsEditorProps> = ({
  content,
  onChange,
  onImageInsert,
  projectId,
  className = '',
  documentTitle = 'Documento sin título',
  onTitleChange,
  collaborators = []
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLInputElement>(null);

  // State management
  const [isReady, setIsReady] = useState(false);
  const [padId, setPadId] = useState<string>('');
  const [isConnected, setIsConnected] = useState(false);
  const [currentTitle, setCurrentTitle] = useState(documentTitle);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date>(new Date());
  const [isSaving, setIsSaving] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [selectedText, setSelectedText] = useState('');
  const [showToolbar, setShowToolbar] = useState(true);
  const [fontFamily, setFontFamily] = useState('Arial');
  const [fontSize, setFontSize] = useState('11');

  // Initialize project-specific pad
  useEffect(() => {
    initializePad();
  }, [projectId]);

  // Initialize Etherpad-style editor
  useEffect(() => {
    if (editorRef.current && padId) {
      initializeEditor();
    }
  }, [padId]);

  const initializePad = useCallback(async () => {
    try {
      const projectPadId = `emma-project-${projectId}`;
      setPadId(projectPadId);

      // Check if pad exists, if not create it - DISABLED
      // try {
      //   await etherpadService.getPadInfo(projectPadId);
      //   setIsConnected(true);
      // } catch (error) {
      //   // Pad doesn't exist, create it
      //   await etherpadService.createPad(projectPadId, projectId, content);
      //   setIsConnected(true);
      // }

      // Load existing content if available - DISABLED
      // if (!content) {
      //   const existingContent = await etherpadService.getPadHTML(projectPadId);
      //   if (existingContent && onChange) {
      //     onChange(existingContent);
      //   }
      // }

      // Just set as connected for now
      setIsConnected(true);
    } catch (error) {
      console.error('Failed to initialize pad:', error);
      setIsConnected(false);
    }
  }, [projectId, content, onChange]);

  const initializeEditor = useCallback(() => {
    if (!editorRef.current) return;

    // Create the editor container with Etherpad's structure
    const editorContainer = editorRef.current;
    editorContainer.innerHTML = `
      <div id="editorcontainer" class="emma-editor-container">
        <div id="editorloadingbox" class="emma-loading">
          <div class="loading-spinner"></div>
          <p>Cargando editor...</p>
        </div>
        <div class="emma-editor-content" contenteditable="true" spellcheck="false">
          ${content || '<p>Comienza a escribir tu contenido...</p>'}
        </div>
      </div>
    `;

    // Get the contenteditable element
    const contentElement = editorContainer.querySelector('.emma-editor-content') as HTMLElement;
    
    if (contentElement) {
      // Set up event listeners for content changes
      contentElement.addEventListener('input', handleContentChange);
      contentElement.addEventListener('keydown', handleKeyDown);
      contentElement.addEventListener('paste', handlePaste);
      contentElement.addEventListener('mouseup', handleSelection);
      contentElement.addEventListener('keyup', handleSelection);

      // Hide loading box
      const loadingBox = editorContainer.querySelector('#editorloadingbox') as HTMLElement;
      if (loadingBox) {
        setTimeout(() => {
          loadingBox.style.display = 'none';
          setIsReady(true);
        }, 500);
      }
    }
  }, [content]);

  const handleContentChange = useCallback(async (event: Event) => {
    const target = event.target as HTMLElement;
    const newContent = target.innerHTML;
    onChange(newContent);

    // Sync with Etherpad if connected - DISABLED
    // if (isConnected && padId) {
    //   try {
    //     await etherpadService.setPadHTML(padId, newContent);
    //   } catch (error) {
    //     console.error('Failed to sync content with Etherpad:', error);
    //   }
    // }
  }, [onChange, isConnected, padId]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Handle keyboard shortcuts
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'b':
          event.preventDefault();
          execCommand('bold');
          break;
        case 'i':
          event.preventDefault();
          execCommand('italic');
          break;
        case 'u':
          event.preventDefault();
          execCommand('underline');
          break;
        case 's':
          event.preventDefault();
          // Auto-save handled elsewhere
          break;
      }
    }
  }, []);

  const handlePaste = useCallback((event: ClipboardEvent) => {
    event.preventDefault();
    const text = event.clipboardData?.getData('text/plain') || '';
    execCommand('insertText', text);
  }, []);

  const handleSelection = useCallback(() => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString());
    } else {
      setSelectedText('');
    }
  }, []);

  const execCommand = useCallback((command: string, value?: string) => {
    document.execCommand(command, false, value);
    
    // Trigger content change
    const contentElement = editorRef.current?.querySelector('.emma-editor-content') as HTMLElement;
    if (contentElement) {
      onChange(contentElement.innerHTML);
    }
  }, [onChange]);

  const formatText = useCallback((format: string) => {
    execCommand(format);
  }, [execCommand]);

  const insertList = useCallback((ordered: boolean = false) => {
    execCommand(ordered ? 'insertOrderedList' : 'insertUnorderedList');
  }, [execCommand]);

  const alignText = useCallback((alignment: string) => {
    execCommand(`justify${alignment}`);
  }, [execCommand]);

  const insertLink = useCallback(() => {
    const url = prompt('Ingresa la URL:');
    if (url) {
      execCommand('createLink', url);
    }
  }, [execCommand]);

  return (
    <div className={`google-docs-editor ${className}`}>
      {/* Google Docs-style Header */}
      <div className="docs-header">
        {/* Top Menu Bar */}
        <div className="docs-menu-bar">
          <div className="flex items-center gap-4">
            {/* Emma Logo */}
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-lg flex items-center justify-center">
                <Sparkles size={16} className="text-white" />
              </div>
              <span className="font-bold text-gray-800">Emma Docs</span>
            </div>

            {/* Document Title */}
            <div className="flex items-center gap-2">
              {isEditingTitle ? (
                <input
                  ref={titleRef}
                  value={currentTitle}
                  onChange={(e) => setCurrentTitle(e.target.value)}
                  onBlur={() => {
                    setIsEditingTitle(false);
                    onTitleChange?.(currentTitle);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      setIsEditingTitle(false);
                      onTitleChange?.(currentTitle);
                    }
                  }}
                  className="text-lg font-medium text-gray-800 bg-transparent border-b-2 border-[#3018ef] outline-none px-2 py-1"
                  autoFocus
                />
              ) : (
                <h1
                  onClick={() => setIsEditingTitle(true)}
                  className="text-lg font-medium text-gray-800 cursor-pointer hover:bg-gray-100 px-2 py-1 rounded"
                >
                  {currentTitle}
                </h1>
              )}
              <Star size={16} className="text-gray-400 hover:text-yellow-500 cursor-pointer" />
              <Folder size={16} className="text-gray-400 hover:text-gray-600 cursor-pointer" />
            </div>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center gap-2">
            {/* Collaborators */}
            {collaborators.length > 0 && (
              <div className="flex items-center gap-1">
                {collaborators.slice(0, 3).map((collaborator) => (
                  <div
                    key={collaborator.id}
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium"
                    style={{ backgroundColor: collaborator.color }}
                    title={collaborator.name}
                  >
                    {collaborator.avatar ? (
                      <img src={collaborator.avatar} alt={collaborator.name} className="w-full h-full rounded-full" />
                    ) : (
                      collaborator.name.charAt(0).toUpperCase()
                    )}
                  </div>
                ))}
                {collaborators.length > 3 && (
                  <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 text-xs font-medium">
                    +{collaborators.length - 3}
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <button className="docs-action-btn">
              <MessageCircle size={16} />
            </button>
            <button className="docs-action-btn">
              <Users size={16} />
            </button>
            <button className="docs-action-btn bg-[#3018ef] text-white hover:bg-[#2614d4]">
              <Share size={16} />
              <span className="ml-1">Compartir</span>
            </button>
          </div>
        </div>

        {/* Menu Navigation */}
        <div className="docs-nav-menu">
          <div className="flex items-center gap-6">
            <button className="nav-menu-item">Archivo</button>
            <button className="nav-menu-item">Editar</button>
            <button className="nav-menu-item">Ver</button>
            <button className="nav-menu-item">Insertar</button>
            <button className="nav-menu-item">Formato</button>
            <button className="nav-menu-item">Herramientas</button>
            <button className="nav-menu-item">Emma AI</button>
            <button className="nav-menu-item">Ayuda</button>
          </div>
        </div>
      </div>

      {/* Google Docs-style Toolbar */}
      {showToolbar && (
        <motion.div
          className="docs-toolbar"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* First Row - Main Actions */}
          <div className="toolbar-row">
            <div className="toolbar-group">
              <button className="toolbar-btn" title="Deshacer (Ctrl+Z)">
                <Undo size={16} />
              </button>
              <button className="toolbar-btn" title="Rehacer (Ctrl+Y)">
                <Redo size={16} />
              </button>
              <button className="toolbar-btn" title="Imprimir (Ctrl+P)">
                <Printer size={16} />
              </button>
            </div>

            <div className="toolbar-divider" />

            <div className="toolbar-group">
              <select
                value={zoomLevel}
                onChange={(e) => setZoomLevel(Number(e.target.value))}
                className="zoom-select"
              >
                <option value={50}>50%</option>
                <option value={75}>75%</option>
                <option value={100}>100%</option>
                <option value={125}>125%</option>
                <option value={150}>150%</option>
              </select>
            </div>

            <div className="toolbar-divider" />

            <div className="toolbar-group">
              <select
                value={fontFamily}
                onChange={(e) => setFontFamily(e.target.value)}
                className="font-select"
              >
                <option value="Arial">Arial</option>
                <option value="Calibri">Calibri</option>
                <option value="Times New Roman">Times New Roman</option>
                <option value="Georgia">Georgia</option>
                <option value="Verdana">Verdana</option>
              </select>

              <select
                value={fontSize}
                onChange={(e) => setFontSize(e.target.value)}
                className="size-select"
              >
                <option value="8">8</option>
                <option value="9">9</option>
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12">12</option>
                <option value="14">14</option>
                <option value="16">16</option>
                <option value="18">18</option>
                <option value="24">24</option>
                <option value="36">36</option>
              </select>
            </div>

            <div className="toolbar-divider" />

            <div className="toolbar-group">
              <button
                onClick={() => formatText('bold')}
                className="toolbar-btn"
                title="Negrita (Ctrl+B)"
              >
                <Bold size={16} />
              </button>
              <button
                onClick={() => formatText('italic')}
                className="toolbar-btn"
                title="Cursiva (Ctrl+I)"
              >
                <Italic size={16} />
              </button>
              <button
                onClick={() => formatText('underline')}
                className="toolbar-btn"
                title="Subrayado (Ctrl+U)"
              >
                <Underline size={16} />
              </button>

              <div className="color-picker-group">
                <button className="toolbar-btn" title="Color de texto">
                  <Type size={16} />
                  <ChevronDown size={12} />
                </button>
                <button className="toolbar-btn" title="Color de resaltado">
                  <Highlighter size={16} />
                  <ChevronDown size={12} />
                </button>
              </div>
            </div>

            <div className="toolbar-divider" />

            <div className="toolbar-group">
              <button
                onClick={insertLink}
                className="toolbar-btn"
                title="Insertar enlace (Ctrl+K)"
              >
                <Link size={16} />
              </button>
              {onImageInsert && (
                <button
                  onClick={onImageInsert}
                  className="toolbar-btn emma-ai-btn"
                  title="Generar imagen con Emma AI"
                >
                  <Image size={16} />
                  <Wand2 size={12} className="ml-1" />
                </button>
              )}
              <button className="toolbar-btn" title="Más opciones">
                <MoreHorizontal size={16} />
              </button>
            </div>
          </div>

          {/* Second Row - Formatting */}
          <div className="toolbar-row">
            <div className="toolbar-group">
              <button
                onClick={() => alignText('Left')}
                className="toolbar-btn"
                title="Alinear a la izquierda (Ctrl+Shift+L)"
              >
                <AlignLeft size={16} />
              </button>
              <button
                onClick={() => alignText('Center')}
                className="toolbar-btn"
                title="Centrar (Ctrl+Shift+E)"
              >
                <AlignCenter size={16} />
              </button>
              <button
                onClick={() => alignText('Right')}
                className="toolbar-btn"
                title="Alinear a la derecha (Ctrl+Shift+R)"
              >
                <AlignRight size={16} />
              </button>
            </div>

            <div className="toolbar-divider" />

            <div className="toolbar-group">
              <button
                onClick={() => insertList(false)}
                className="toolbar-btn"
                title="Lista con viñetas (Ctrl+Shift+8)"
              >
                <List size={16} />
              </button>
              <button
                onClick={() => insertList(true)}
                className="toolbar-btn"
                title="Lista numerada (Ctrl+Shift+7)"
              >
                <ListOrdered size={16} />
              </button>
            </div>

            <div className="toolbar-divider" />

            <div className="toolbar-group">
              <button className="toolbar-btn" title="Disminuir sangría">
                <span className="text-sm">⇤</span>
              </button>
              <button className="toolbar-btn" title="Aumentar sangría">
                <span className="text-sm">⇥</span>
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Google Docs-style Document Area */}
      <div className="docs-document-area">
        <div className="docs-page-container" style={{ zoom: `${zoomLevel}%` }}>
          {/* Document Page */}
          <div className="docs-page">
            {/* Page Content */}
            <div
              ref={editorRef}
              className="docs-page-content"
              contentEditable={true}
              suppressContentEditableWarning={true}
              onInput={handleContentChange}
              onKeyDown={handleKeyDown}
              onPaste={handlePaste}
              onMouseUp={handleSelection}
              onKeyUp={handleSelection}
              style={{
                fontFamily: fontFamily,
                fontSize: `${fontSize}pt`,
                color: textColor,
                lineHeight: '1.5',
                minHeight: '11in', // Standard letter size height
                padding: '1in',    // Standard margins
                outline: 'none'
              }}
              dangerouslySetInnerHTML={{
                __html: content || '<p>Comienza a escribir tu documento...</p>'
              }}
            />
          </div>

          {/* Additional pages will be added dynamically */}
          {currentPage > 1 && (
            <div className="docs-page">
              <div className="docs-page-content" style={{
                fontFamily: fontFamily,
                fontSize: `${fontSize}pt`,
                color: textColor,
                lineHeight: '1.5',
                minHeight: '11in',
                padding: '1in'
              }}>
                {/* Overflow content from previous page */}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Google Docs-style Status Bar */}
      <div className="docs-status-bar">
        <div className="flex items-center gap-4">
          {/* Save Status */}
          <div className="flex items-center gap-2">
            {isSaving ? (
              <>
                <div className="w-3 h-3 border-2 border-[#3018ef] border-t-transparent rounded-full animate-spin"></div>
                <span className="text-sm text-gray-600">Guardando...</span>
              </>
            ) : (
              <>
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">
                  Guardado {lastSaved.toLocaleTimeString()}
                </span>
              </>
            )}
          </div>

          {/* Word Count */}
          <div className="text-sm text-gray-600">
            {wordCount} palabras
          </div>

          {/* Page Info */}
          <div className="text-sm text-gray-600">
            Página {currentPage}
          </div>

          {/* Connection Status */}
          {isConnected ? (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-green-700">Colaboración activa</span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span className="text-sm text-red-700">Sin conexión</span>
            </div>
          )}
        </div>

        {/* Right Side Status */}
        <div className="flex items-center gap-4">
          {/* Emma AI Indicator */}
          <div className="flex items-center gap-2">
            <Sparkles size={14} className="text-[#3018ef]" />
            <span className="text-sm font-medium bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
              Emma AI
            </span>
          </div>

          {/* Quick Actions */}
          <button
            onClick={() => setShowComments(!showComments)}
            className="status-action-btn"
            title="Comentarios"
          >
            <MessageCircle size={14} />
          </button>
          <button
            onClick={() => setShowHistory(!showHistory)}
            className="status-action-btn"
            title="Historial de versiones"
          >
            <History size={14} />
          </button>
        </div>
      </div>
    </div>
  );
};

// Export the Google Docs-style editor
export default GoogleDocsEditor;

// Also export with the original name for backward compatibility
export { GoogleDocsEditor as EtherpadEditor };
