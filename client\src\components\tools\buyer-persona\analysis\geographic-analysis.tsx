"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Globe, Clock, DollarSign, Users, TrendingUp, Shield, MessageCircle, Calendar } from "lucide-react";

interface GeographicAnalysisProps {
  geographicData: {
    regional_analysis?: {
      [region: string]: {
        cultural_context?: any;
        business_context?: any;
        timezone_info?: any;
        communication_preferences?: any;
        compliance_requirements?: any;
        economic_indicators?: any;
        localization_score?: number;
      };
    };
    comparative_analysis?: any;
    recommendations?: string[];
  };
  delay?: number;
}

export function GeographicAnalysis({ geographicData, delay = 0.5 }: GeographicAnalysisProps) {
  if (!geographicData || !geographicData.regional_analysis) {
    return null;
  }

  const regions = Object.keys(geographicData.regional_analysis);
  const primaryRegion = regions[0];
  const regionData = geographicData.regional_analysis[primaryRegion];

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600 bg-green-100";
    if (score >= 60) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getEffectivenessColor = (effectiveness: number) => {
    if (effectiveness >= 85) return "bg-green-500";
    if (effectiveness >= 70) return "bg-yellow-500";
    return "bg-red-500";
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay }}
    >
      <Card className="bg-gradient-to-br from-orange-50 to-red-100 border-orange-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-orange-800">
            <Globe className="h-5 w-5" />
            Análisis Geográfico y Cultural
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Región Principal y Score */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-2 flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Región Principal
              </h4>
              <p className="text-2xl font-bold text-orange-600">{primaryRegion}</p>
              {regionData?.localization_score && (
                <div className="mt-2">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-600">Score de Localización</span>
                    <Badge className={getScoreColor(regionData.localization_score)}>
                      {regionData.localization_score}/100
                    </Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-orange-400 to-orange-600 h-2 rounded-full"
                      style={{ width: `${regionData.localization_score}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            {/* Indicadores Económicos */}
            {regionData?.business_context?.economic_indicators && (
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Indicadores Económicos
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">PIB per cápita:</span>
                    <span className="text-sm font-medium">
                      {regionData.business_context.economic_indicators.gdp_per_capita}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Facilidad de negocio:</span>
                    <span className="text-sm font-medium">
                      #{regionData.business_context.economic_indicators.business_ease_rank}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Adopción digital:</span>
                    <Badge variant="outline" className="text-xs">
                      {regionData.business_context.economic_indicators.digital_adoption}
                    </Badge>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Contexto Cultural */}
          {regionData?.cultural_context && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Users className="h-4 w-4" />
                Contexto Cultural
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div>
                    <span className="text-sm text-gray-600 block">Cultura de Negocios:</span>
                    <p className="text-sm font-medium">{regionData.cultural_context.business_culture}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600 block">Estilo de Comunicación:</span>
                    <p className="text-sm font-medium">{regionData.cultural_context.communication_style}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600 block">Toma de Decisiones:</span>
                    <p className="text-sm font-medium">{regionData.cultural_context.decision_making}</p>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm text-gray-600 block">Construcción de Confianza:</span>
                    <p className="text-sm font-medium">{regionData.cultural_context.trust_building}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600 block">Valores Culturales:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {regionData.cultural_context.cultural_values && Array.isArray(regionData.cultural_context.cultural_values) ?
                        regionData.cultural_context.cultural_values.map((value: string, index: number) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {String(value)}
                          </Badge>
                        )) : null
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Canales de Comunicación */}
          {regionData?.communication_preferences && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <MessageCircle className="h-4 w-4" />
                Efectividad de Canales
              </h4>
              <div className="space-y-3">
                {Object.entries(regionData.communication_preferences.channel_effectiveness || {}).map(([channel, effectiveness]: [string, any]) => (
                  <div key={channel} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">{channel}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${getEffectivenessColor(effectiveness)}`}
                          style={{ width: `${effectiveness}%` }}
                        ></div>
                      </div>
                      <Badge variant="outline" className="text-xs min-w-[45px]">
                        {effectiveness}%
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>

              {regionData.communication_preferences.response_expectations && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-blue-800">
                    ⏱️ Expectativa de respuesta: {regionData.communication_preferences.response_expectations}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Información de Zona Horaria y Timing */}
          {regionData?.timezone_info && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Timing Óptimo
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-600 block mb-2">Horarios Óptimos de Contacto:</span>
                  <div className="space-y-1">
                    {regionData.timezone_info.optimal_contact_hours && Array.isArray(regionData.timezone_info.optimal_contact_hours) ?
                      regionData.timezone_info.optimal_contact_hours.map((hour: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs mr-1">
                          {String(hour)}
                        </Badge>
                      )) : null
                    }
                  </div>
                </div>
                <div>
                  <span className="text-sm text-gray-600 block mb-2">Mejores Días:</span>
                  <div className="space-y-1">
                    {regionData.timezone_info.optimal_days && Array.isArray(regionData.timezone_info.optimal_days) ?
                      regionData.timezone_info.optimal_days.map((day: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs mr-1">
                          {String(day)}
                        </Badge>
                      )) : null
                    }
                  </div>
                </div>
              </div>

              {regionData.timezone_info.avoid_times && (
                <div className="mt-4">
                  <span className="text-sm text-gray-600 block mb-2">⚠️ Evitar:</span>
                  <div className="space-y-1">
                    {Array.isArray(regionData.timezone_info.avoid_times) ?
                      regionData.timezone_info.avoid_times.map((time: string, index: number) => (
                        <Badge key={index} className="bg-red-100 text-red-800 text-xs mr-1">
                          {String(time)}
                        </Badge>
                      )) : null
                    }
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Contexto de Negocios */}
          {regionData?.business_context && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Contexto de Negocios
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div>
                    <span className="text-sm text-gray-600">Horario de oficina:</span>
                    <p className="text-sm font-medium">{regionData.business_context.business_hours}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">Consideraciones fiscales:</span>
                    <p className="text-sm font-medium">{regionData.business_context.tax_considerations}</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm text-gray-600">Métodos de pago preferidos:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {regionData.business_context.payment_methods && Array.isArray(regionData.business_context.payment_methods) ?
                        regionData.business_context.payment_methods.map((method: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {String(method)}
                          </Badge>
                        )) : null
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Compliance y Regulaciones */}
          {regionData?.compliance_requirements && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Compliance y Regulaciones
              </h4>
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-gray-600 block mb-1">Protección de Datos:</span>
                  <Badge className="bg-blue-100 text-blue-800">
                    {regionData.compliance_requirements.data_protection}
                  </Badge>
                </div>

                {regionData.compliance_requirements.privacy_requirements && (
                  <div>
                    <span className="text-sm text-gray-600 block mb-2">Requisitos de Privacidad:</span>
                    <div className="space-y-1">
                      {Array.isArray(regionData.compliance_requirements.privacy_requirements) ?
                        regionData.compliance_requirements.privacy_requirements.slice(0, 3).map((req: string, index: number) => (
                          <div key={index} className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                            <span className="text-xs text-gray-600">{String(req)}</span>
                          </div>
                        )) : null
                      }
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Recomendaciones */}
          {geographicData.recommendations && geographicData.recommendations.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Recomendaciones Estratégicas
              </h4>
              <div className="space-y-2">
                {geographicData.recommendations.slice(0, 4).map((recommendation: any, index: number) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                    <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                      {index + 1}
                    </div>
                    <div className="text-sm text-green-800 flex-1">
                      {typeof recommendation === 'string' ? (
                        <p>{recommendation}</p>
                      ) : typeof recommendation === 'object' && recommendation !== null ? (
                        <div className="space-y-1">
                          {recommendation.region && (
                            <p><span className="font-medium">Región:</span> {recommendation.region}</p>
                          )}
                          {recommendation.priority && (
                            <p><span className="font-medium">Prioridad:</span> {recommendation.priority}</p>
                          )}
                          {recommendation.quick_wins && Array.isArray(recommendation.quick_wins) && (
                            <div>
                              <span className="font-medium">Victorias rápidas:</span>
                              <ul className="list-disc list-inside ml-2 mt-1">
                                {recommendation.quick_wins.map((win: string, winIndex: number) => (
                                  <li key={winIndex} className="text-xs">{win}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                          {recommendation.implementation_steps && Array.isArray(recommendation.implementation_steps) && (
                            <div>
                              <span className="font-medium">Pasos de implementación:</span>
                              <ul className="list-decimal list-inside ml-2 mt-1">
                                {recommendation.implementation_steps.map((step: string, stepIndex: number) => (
                                  <li key={stepIndex} className="text-xs">{step}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                          {recommendation.success_metrics && Array.isArray(recommendation.success_metrics) && (
                            <div>
                              <span className="font-medium">Métricas de éxito:</span>
                              <ul className="list-disc list-inside ml-2 mt-1">
                                {recommendation.success_metrics.map((metric: string, metricIndex: number) => (
                                  <li key={metricIndex} className="text-xs">{metric}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      ) : (
                        <p>{String(recommendation)}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
