"use client"

import { useState, useEffect } from "react"
import { <PERSON> } from "wouter"
import { <PERSON>ap, DollarSign, TrendingUp, <PERSON>rkles, X, Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/contexts/LanguageContext"

interface SolutionFeature {
  icon: React.ReactNode
  title: string
  description: string
  benefit: string
  comparison: string
  color: string
}

// Función para obtener características traducidas
const getSolutionFeatures = (t: (key: string) => string): SolutionFeature[] => [
  {
    icon: <DollarSign className="w-8 h-8" />,
    title: t('landing.solution_feature_pricing_title'),
    description: t('landing.solution_feature_pricing_desc'),
    benefit: "$49-$199/mes",
    comparison: t('landing.solution_feature_pricing_comparison'),
    color: "from-[#3018ef] to-[#2614d4]"
  },
  {
    icon: <Zap className="w-8 h-8" />,
    title: t('landing.solution_feature_speed_title'),
    description: t('landing.solution_feature_speed_desc'),
    benefit: t('landing.solution_feature_speed_benefit'),
    comparison: t('landing.solution_feature_speed_comparison'),
    color: "from-[#dd3a5a] to-[#c73351]"
  },
  {
    icon: <TrendingUp className="w-8 h-8" />,
    title: t('landing.solution_feature_quality_title'),
    description: t('landing.solution_feature_quality_desc'),
    benefit: t('landing.solution_feature_quality_benefit'),
    comparison: t('landing.solution_feature_quality_comparison'),
    color: "from-[#3018ef] to-[#dd3a5a]"
  },
  {
    icon: <Sparkles className="w-8 h-8" />,
    title: t('landing.solution_feature_scalability_title'),
    description: t('landing.solution_feature_scalability_desc'),
    benefit: t('landing.solution_feature_scalability_benefit'),
    comparison: t('landing.solution_feature_scalability_comparison'),
    color: "from-[#dd3a5a] to-[#3018ef]"
  }
]

// Datos para la comparación de dos columnas - now using translations

export function SolutionSection() {
  const { t, currentLanguage } = useLanguage()
  const [isVisible, setIsVisible] = useState(false)


  // Obtener características traducidas
  const solutionFeatures = getSolutionFeatures(t)

  const problems = t('landing.problem_solution.problems') as unknown as string[]
  const solutions = t('landing.problem_solution.solutions') as unknown as string[]

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const section = document.getElementById('solution-section')
    if (section) observer.observe(section)

    return () => observer.disconnect()
  }, [])

  return (
    <section
      id="solution-section"
      className="py-20 sm:py-24 bg-white relative overflow-hidden"
    >
      {/* Clean Background */}
      <div className="absolute inset-0 bg-white" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-12">
          <div
            className={`transition-all duration-700 ease-out ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="inline-block bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm px-8 py-4 rounded-2xl border border-white/20 shadow-lg text-sm font-semibold text-gray-800 mb-8">
              <Sparkles className="w-4 h-4 mr-2 inline" />
              {t('landing.solution_badge')}
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
              <span dangerouslySetInnerHTML={{ __html: t('landing.solution_title') }} />
            </h2>

            <p className="text-xl sm:text-2xl font-medium text-gray-600 max-w-4xl mx-auto leading-relaxed">
              {t('landing.solution_description')}
            </p>
          </div>
        </div>

        {/* Solution Features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20 max-w-7xl mx-auto">
          {solutionFeatures.map((feature, index) => (
            <div
              key={index}
              className={`bg-white/80 backdrop-blur-sm p-10 rounded-3xl shadow-xl border border-gray-100 transition-all duration-500 hover:shadow-2xl hover:scale-105 ${
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
              }`}
              style={{
                transitionDelay: `${index * 100 + 200}ms`,
              }}
            >
              {/* Icon */}
              <div className="w-20 h-20 bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] rounded-2xl shadow-lg flex items-center justify-center mb-8">
                <div className="text-white">
                  {feature.icon}
                </div>
              </div>

              {/* Content */}
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                {feature.title}
              </h3>

              <p className="text-gray-600 mb-8 leading-relaxed text-lg">
                {feature.description}
              </p>

              {/* Benefits */}
              <div className="space-y-3">
                <div className="text-xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                  {feature.benefit}
                </div>
                <div className="text-sm text-gray-500 font-medium">
                  {feature.comparison}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Comparison: Two Columns Side by Side */}
        <div
          className={`transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '600ms' }}
        >
          {/* Header */}
          <div className="text-center mb-12">
            <h3 className="inline-block text-3xl sm:text-4xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent px-6 py-3 mb-6">
              {t('landing.problem_solution.title')}
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* Problemas */}
            <div
              className={`bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-red-100 p-8 h-full transition-all duration-700 ease-out ${
                isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'
              }`}
              style={{ transitionDelay: '700ms' }}
            >
              <div className="flex items-center mb-8">
                <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                  <X size={24} className="text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900">{t('landing.problem_solution.problems_title')}</h3>
              </div>

              <div className="space-y-4">
                {problems.map((problem, index) => (
                  <div
                    key={index}
                    className={`bg-red-50/50 backdrop-blur-sm rounded-2xl border border-red-200 p-4 flex items-start transition-all duration-500 hover:scale-105 ${
                      isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-5'
                    }`}
                    style={{ transitionDelay: `${800 + index * 100}ms` }}
                  >
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0 shadow-sm">
                      <X size={16} className="text-red-500" />
                    </div>
                    <p className="text-base font-medium text-gray-700">{problem}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Soluciones */}
            <div
              className={`bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-green-100 p-8 h-full transition-all duration-700 ease-out ${
                isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'
              }`}
              style={{ transitionDelay: '700ms' }}
            >
              <div className="flex items-center mb-8">
                <div className="w-12 h-12 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                  <Check size={24} className="text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900">{t('landing.problem_solution.solutions_title')}</h3>
              </div>

              <div className="space-y-4">
                {solutions.map((solution, index) => (
                  <div
                    key={index}
                    className={`bg-green-50/50 backdrop-blur-sm rounded-2xl border border-green-200 p-4 flex items-start transition-all duration-500 hover:scale-105 ${
                      isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-5'
                    }`}
                    style={{ transitionDelay: `${800 + index * 100}ms` }}
                  >
                    <div className="w-8 h-8 bg-[#3018ef]/10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 shadow-sm">
                      <Check size={16} className="text-[#3018ef]" />
                    </div>
                    <p className="text-base font-medium text-gray-700">{solution}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div
          className={`text-center mt-12 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '800ms' }}
        >
          <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-3xl p-12 text-white shadow-2xl">
            <h3 className="text-4xl font-bold mb-6">
              {t('landing.solution_cta_title')}
            </h3>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              {t('landing.solution_cta_description')}
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link href="/register">
                <Button variant="red" size="lg" className="px-10 py-4 text-lg font-bold">
                  {t('landing.get_started')}
                </Button>
              </Link>
              <Link href="#demo">
                <Button variant="white" size="lg" className="px-10 py-4 text-lg font-bold">
                  {t('landing.see_demo')}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
