import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "wouter";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  Facebook,
  Instagram,
  Linkedin,
  Moon,
  Send,
  Sun,
  Twitter,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function NewFooter() {
  const { t } = useLanguage();
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check if user has a saved preference, default to false (light mode)
    if (typeof window !== "undefined") {
      const savedMode = localStorage.getItem("darkMode");
      return savedMode === "true";
    }
    return false;
  });

  const currentYear = new Date().getFullYear();

  useEffect(() => {
    // Apply dark mode class
    if (isDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }

    // Save preference
    localStorage.setItem("darkMode", isDarkMode.toString());
  }, [isDarkMode]);

  return (
    <footer className="relative border-t bg-background text-foreground transition-colors duration-300">
      <div className="container mx-auto px-4 py-12 md:px-6 lg:px-8">
        <div className="grid gap-12 md:grid-cols-2 lg:grid-cols-4">
          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <Link
              href="/"
              className="text-2xl font-black bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-transparent bg-clip-text mb-4 inline-block"
            >
              {t('landing.footer.brand_name')}
            </Link>
            <p className="mb-6 text-muted-foreground">
              {t('landing.footer.description')}
            </p>
            <form className="relative">
              <Input
                type="email"
                placeholder={t('landing.footer.email_placeholder')}
                className="pr-12 backdrop-blur-sm border-2 border-black dark:border-white shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] dark:shadow-[2px_2px_0px_0px_rgba(255,255,255,0.9)]"
              />
              <Button
                type="submit"
                size="icon"
                className="absolute right-1 top-1 h-8 w-8 rounded-full bg-blue-500 text-white border-2 border-black dark:border-white shadow-[1px_1px_0px_0px_rgba(0,0,0,0.9)] dark:shadow-[1px_1px_0px_0px_rgba(255,255,255,0.9)] transition-transform hover:scale-105"
              >
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 10 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Send className="h-4 w-4" />
                </motion.div>
                <span className="sr-only">{t('landing.footer.subscribe')}</span>
              </Button>
            </form>
            <div className="absolute -right-4 top-0 h-24 w-24 rounded-full bg-blue-500/10 blur-2xl" />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            <h3 className="mb-4 text-lg font-bold">{t('landing.footer.quick_links')}</h3>
            <nav className="space-y-2 text-sm">
              {(t('landing.footer.navigation') as unknown as Array<{href: string, label: string}>).map((link, i) => (
                <motion.a
                  key={link.href}
                  href={link.href}
                  className="block transition-colors hover:text-blue-500"
                  whileHover={{ x: 5 }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 + i * 0.05 }}
                >
                  {link.label}
                </motion.a>
              ))}
            </nav>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <h3 className="mb-4 text-lg font-bold">Contáctanos</h3>
            <address className="space-y-2 text-sm not-italic">
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                Volcán 212, Lomas - Virreyes
              </motion.p>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                Lomas de Chapultepec, Miguel Hidalgo, 11000 Ciudad de México, CDMX
              </motion.p>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                Teléfono: (*************
              </motion.p>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="hover:text-blue-500"
              >
                Email: <EMAIL>
              </motion.p>
            </address>
          </motion.div>

          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <h3 className="mb-4 text-lg font-bold">Síguenos</h3>
            <div className="mb-6 flex space-x-4">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div
                      whileHover={{ y: -3, scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-full border-2 border-black dark:border-white shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] dark:shadow-[2px_2px_0px_0px_rgba(255,255,255,0.9)] hover:bg-blue-100 dark:hover:bg-blue-900"
                      >
                        <Facebook className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <span className="sr-only">Facebook</span>
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Síguenos en Facebook</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div
                      whileHover={{ y: -3, scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-full border-2 border-black dark:border-white shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] dark:shadow-[2px_2px_0px_0px_rgba(255,255,255,0.9)] hover:bg-blue-100 dark:hover:bg-blue-900"
                      >
                        <Twitter className="h-4 w-4 text-blue-400" />
                        <span className="sr-only">Twitter</span>
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Síguenos en Twitter</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div
                      whileHover={{ y: -3, scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-full border-2 border-black dark:border-white shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] dark:shadow-[2px_2px_0px_0px_rgba(255,255,255,0.9)] hover:bg-pink-100 dark:hover:bg-pink-900"
                      >
                        <Instagram className="h-4 w-4 text-pink-500 dark:text-pink-400" />
                        <span className="sr-only">Instagram</span>
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Síguenos en Instagram</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <motion.div
                      whileHover={{ y: -3, scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Button
                        variant="outline"
                        size="icon"
                        className="rounded-full border-2 border-black dark:border-white shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] dark:shadow-[2px_2px_0px_0px_rgba(255,255,255,0.9)] hover:bg-blue-100 dark:hover:bg-blue-900"
                      >
                        <Linkedin className="h-4 w-4 text-blue-700 dark:text-blue-500" />
                        <span className="sr-only">LinkedIn</span>
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Conéctate con nosotros en LinkedIn</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <motion.div
              className="flex items-center space-x-2"
              whileHover={{ scale: 1.05 }}
            >
              <Sun className="h-4 w-4 text-yellow-500" />
              <Switch
                id="dark-mode"
                checked={isDarkMode}
                onCheckedChange={setIsDarkMode}
                className="data-[state=checked]:bg-blue-500"
              />
              <Moon className="h-4 w-4 text-blue-700 dark:text-blue-300" />
              <Label htmlFor="dark-mode" className="sr-only">
                Cambiar modo oscuro
              </Label>
            </motion.div>
          </motion.div>
        </div>

        <motion.div
          className="mt-12 flex flex-col items-center justify-between gap-4 border-t pt-8 text-center md:flex-row"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <p className="text-sm text-muted-foreground">
            © {currentYear} MarketingIA. Todos los derechos reservados.
          </p>
          <nav className="flex gap-4 text-sm">
            {[
              { href: "#", label: "Política de Privacidad" },
              { href: "#", label: "Términos de Servicio" },
              { href: "#", label: "Configuración de Cookies" },
            ].map((link, i) => (
              <motion.a
                key={i}
                href={link.href}
                className="transition-colors hover:text-blue-500"
                whileHover={{ scale: 1.05 }}
              >
                {link.label}
              </motion.a>
            ))}
          </nav>
        </motion.div>
      </div>
    </footer>
  );
}
