import React, { useState } from "react";
import { motion } from "framer-motion";
import { 
  Wand2, 
  Target, 
  <PERSON>lette, 
  Zap, 
  T<PERSON>dingUp, 
  Eye,
  BarChart3,
  Lightbulb,
  Sparkles,
  RefreshCw
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface AdCreatorEnhancementsProps {
  platform: string;
  onPromptEnhance?: (enhancedPrompt: string) => void;
  onStyleApply?: (style: string) => void;
  currentPrompt?: string;
}

const AdCreatorEnhancements: React.FC<AdCreatorEnhancementsProps> = ({
  platform,
  onPromptEnhance,
  onStyleApply,
  currentPrompt = ""
}) => {
  const [creativityLevel, setCreativityLevel] = useState([70]);
  const [useAIOptimization, setUseAIOptimization] = useState(true);
  const [focusOnConversion, setFocusOnConversion] = useState(false);
  const [selectedStyle, setSelectedStyle] = useState<string | null>(null);

  // Estilos predefinidos por plataforma
  const platformStyles = {
    facebook: [
      { id: "modern", name: "Moderno", description: "Diseño limpio y contemporáneo", color: "bg-blue-500" },
      { id: "vibrant", name: "Vibrante", description: "Colores llamativos y energéticos", color: "bg-orange-500" },
      { id: "professional", name: "Profesional", description: "Corporativo y confiable", color: "bg-gray-600" },
      { id: "lifestyle", name: "Lifestyle", description: "Casual y aspiracional", color: "bg-green-500" }
    ],
    instagram: [
      { id: "aesthetic", name: "Estético", description: "Minimalista y elegante", color: "bg-pink-500" },
      { id: "trendy", name: "Trendy", description: "Siguiendo tendencias actuales", color: "bg-purple-500" },
      { id: "natural", name: "Natural", description: "Orgánico y auténtico", color: "bg-green-600" },
      { id: "bold", name: "Audaz", description: "Impactante y memorable", color: "bg-red-500" }
    ],
    linkedin: [
      { id: "corporate", name: "Corporativo", description: "Formal y profesional", color: "bg-blue-700" },
      { id: "innovative", name: "Innovador", description: "Tecnológico y avanzado", color: "bg-indigo-600" },
      { id: "trustworthy", name: "Confiable", description: "Serio y establecido", color: "bg-gray-700" },
      { id: "growth", name: "Crecimiento", description: "Dinámico y ambicioso", color: "bg-emerald-600" }
    ],
    youtube: [
      { id: "clickbait", name: "Llamativo", description: "Alto CTR garantizado", color: "bg-red-600" },
      { id: "educational", name: "Educativo", description: "Informativo y claro", color: "bg-blue-600" },
      { id: "entertainment", name: "Entretenimiento", description: "Divertido y engaging", color: "bg-yellow-500" },
      { id: "dramatic", name: "Dramático", description: "Emocional e impactante", color: "bg-purple-600" }
    ]
  };

  const currentStyles = platformStyles[platform as keyof typeof platformStyles] || platformStyles.facebook;

  // Optimizaciones inteligentes
  const intelligentOptimizations = [
    {
      id: "conversion",
      title: "Optimización para Conversión",
      description: "Añade elementos que aumentan la tasa de conversión",
      icon: Target,
      active: focusOnConversion
    },
    {
      id: "engagement",
      title: "Maximizar Engagement",
      description: "Optimiza para likes, comentarios y shares",
      icon: TrendingUp,
      active: true
    },
    {
      id: "brand_consistency",
      title: "Consistencia de Marca",
      description: "Mantiene coherencia visual con tu marca",
      icon: Eye,
      active: useAIOptimization
    }
  ];

  const handleStyleSelect = (style: any) => {
    setSelectedStyle(style.id);
    if (onStyleApply) {
      onStyleApply(style.id);
    }
  };

  const handlePromptEnhancement = () => {
    if (!currentPrompt || !onPromptEnhance) return;

    const enhancements = [];
    
    // Añadir estilo seleccionado
    if (selectedStyle) {
      const style = currentStyles.find(s => s.id === selectedStyle);
      if (style) {
        enhancements.push(`estilo ${style.name.toLowerCase()}`);
      }
    }

    // Añadir nivel de creatividad
    const creativity = creativityLevel[0];
    if (creativity > 80) {
      enhancements.push("muy creativo e innovador");
    } else if (creativity > 60) {
      enhancements.push("creativo y original");
    } else if (creativity > 40) {
      enhancements.push("equilibrado y profesional");
    } else {
      enhancements.push("conservador y tradicional");
    }

    // Añadir optimizaciones
    if (focusOnConversion) {
      enhancements.push("optimizado para conversión con call-to-action claro");
    }

    if (useAIOptimization) {
      enhancements.push("con elementos de alta calidad profesional");
    }

    // Construir prompt mejorado
    const enhancedPrompt = `${currentPrompt}, ${enhancements.join(", ")}, calidad de producción comercial, iluminación profesional`;
    
    onPromptEnhance(enhancedPrompt);
  };

  return (
    <div className="space-y-3">
      {/* Estilos Visuales - Compacto */}
      <div>
        <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
          <Palette className="w-4 h-4" />
          Estilos
        </h4>
        <div className="grid grid-cols-1 gap-2">
          {currentStyles.slice(0, 3).map((style) => (
            <motion.div
              key={style.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => handleStyleSelect(style)}
              className={`p-2 rounded border cursor-pointer transition-all ${
                selectedStyle === style.id
                  ? "border-[#3018ef] bg-[#3018ef]/5"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${style.color}`}></div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-xs truncate">{style.name}</h4>
                  <p className="text-xs text-gray-500 truncate">{style.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Configuración de IA - Compacto */}
      <div>
        <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
          <Wand2 className="w-4 h-4" />
          Configuración IA
        </h4>
        <div className="space-y-3">
          {/* Nivel de Creatividad */}
          <div>
            <Label className="text-xs font-medium">
              Creatividad: {creativityLevel[0]}%
            </Label>
            <Slider
              value={creativityLevel}
              onValueChange={setCreativityLevel}
              max={100}
              step={10}
              className="mt-1"
            />
          </div>

          {/* Switches de Optimización */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Optimización IA</Label>
              <Switch
                checked={useAIOptimization}
                onCheckedChange={setUseAIOptimization}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Enfoque Conversión</Label>
              <Switch
                checked={focusOnConversion}
                onCheckedChange={setFocusOnConversion}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Optimizaciones Activas - Compacto */}
      <div>
        <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
          <Zap className="w-4 h-4" />
          Optimizaciones
        </h4>
        <div className="space-y-1">
          {intelligentOptimizations.slice(0, 2).map((opt) => (
            <div
              key={opt.id}
              className={`flex items-center gap-2 p-2 rounded text-xs ${
                opt.active ? "bg-green-50 text-green-700" : "bg-gray-50 text-gray-500"
              }`}
            >
              <opt.icon className="w-3 h-3" />
              <span className="flex-1 truncate">{opt.title}</span>
              {opt.active && <span className="text-green-600">✓</span>}
            </div>
          ))}
        </div>
      </div>

      {/* Botón de Mejora */}
      <Button
        onClick={handlePromptEnhancement}
        className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:opacity-90 text-xs h-8"
        disabled={!currentPrompt}
      >
        <Sparkles className="w-3 h-3 mr-1" />
        Mejorar con IA
      </Button>
    </div>
  );
};

export default AdCreatorEnhancements;
