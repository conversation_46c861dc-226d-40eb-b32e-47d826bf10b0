import { useState, useEffect } from 'react';

export const useLoadingProgress = (isLoading: boolean, isGeneratingMore: boolean = false) => {
  const [loadingStage, setLoadingStage] = useState<string>("Iniciando generación...");
  const [loadingProgress, setLoadingProgress] = useState<number>(0);
  const [startTime, setStartTime] = useState<number>(Date.now());

  // Reset when starting new generation
  useEffect(() => {
    if (isLoading) {
      setStartTime(Date.now());
      setLoadingProgress(0);
      setLoadingStage(isGeneratingMore ? "Generando más posts..." : "Iniciando generación...");
    }
  }, [isLoading, isGeneratingMore]);

  // Progress tracking for content generation
  useEffect(() => {
    if (!isLoading) return;

    const progressInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const maxTime = 300000; // 5 minutes in milliseconds
      const progress = Math.min((elapsed / maxTime) * 100, 95); // Cap at 95% until completion

      setLoadingProgress(progress);

      // Update loading stage based on elapsed time
      if (elapsed < 10000) {
        setLoadingStage("Analizando tu marca y configuración...");
      } else if (elapsed < 30000) {
        setLoadingStage("Generando contenido personalizado...");
      } else if (elapsed < 60000) {
        setLoadingStage("Creando imágenes profesionales...");
      } else if (elapsed < 120000) {
        setLoadingStage("Procesando imágenes de alta calidad...");
      } else if (elapsed < 240000) {
        setLoadingStage("Finalizando generación de contenido...");
      } else {
        setLoadingStage("Últimos ajustes, casi listo...");
      }
    }, 1000);

    return () => clearInterval(progressInterval);
  }, [isLoading, startTime]);

  const setCompletionProgress = () => {
    setLoadingProgress(100);
    setLoadingStage("¡Generación completada!");
  };

  return {
    loadingStage,
    loadingProgress,
    startTime,
    setCompletionProgress
  };
};
