import { useState, useCallback } from "react";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { SEOAnalysisResult, SEOAnalysisRequest, AnalysisMode } from "../types/seo";

interface UseSEOAnalysisProps {
  url: string;
  analysisMode: AnalysisMode;
}

interface UseSEOAnalysisReturn {
  // Query state
  data: SEOAnalysisResult | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  
  // Manual analysis
  analyzeUrl: () => void;
  
  // UI state
  showPulsatingEffect: boolean;
  setShowPulsatingEffect: (show: boolean) => void;
}

export const useSEOAnalysis = ({ url, analysisMode }: UseSEOAnalysisProps): UseSEOAnalysisReturn => {
  const { toast } = useToast();
  const [showPulsatingEffect, setShowPulsatingEffect] = useState(false);
  const [shouldAnalyze, setShouldAnalyze] = useState(false);

  // React Query for SEO analysis
  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["seo-analysis", url, analysisMode],
    queryFn: async (): Promise<SEOAnalysisResult> => {
      if (!url) {
        throw new Error("URL is required");
      }

      console.log("Iniciando análisis SEO para:", url);
      setShowPulsatingEffect(true);

      const requestBody: SEOAnalysisRequest = {
        url: url.trim(),
        mode: analysisMode,
      };

      console.log("Enviando solicitud:", requestBody);

      const response = await fetch("/api/seo/analyze", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API Error:", errorData);
        throw new Error(errorData.detail || "Failed to analyze URL");
      }

      const jsonData = await response.json();
      console.log("Datos recibidos:", jsonData);
      console.log("🏆 Achievements en respuesta:", jsonData.achievements);
      console.log("🏆 Achievements length:", jsonData.achievements?.length);

      // Check if the response contains an error (even with 200 status)
      if (jsonData.status === "error") {
        console.error("Error en el análisis:", jsonData.error_message);
        throw new Error(
          jsonData.error_message || "Error al procesar el análisis SEO",
        );
      }

      if (!response.ok) {
        console.error("Error HTTP:", response.status, jsonData);
        throw new Error(
          jsonData.error_message || "Error al procesar el análisis SEO",
        );
      }

      console.log("Análisis completado exitosamente:", jsonData.status);

      // Disable pulsating effect
      setShowPulsatingEffect(false);

      return jsonData;
    },
    enabled: shouldAnalyze && !!url,
    retry: 1,
    refetchOnWindowFocus: false,
  });

  const analyzeUrl = useCallback(() => {
    if (!url) {
      toast({
        title: "Error",
        description: "Por favor, ingresa una URL válida",
        variant: "destructive",
      });
      return;
    }

    console.log("Iniciando análisis manual para:", url);
    setShouldAnalyze(true);
    refetch();
  }, [url, refetch, toast]);

  return {
    data,
    isLoading,
    isError,
    error,
    analyzeUrl,
    showPulsatingEffect,
    setShowPulsatingEffect,
  };
};
