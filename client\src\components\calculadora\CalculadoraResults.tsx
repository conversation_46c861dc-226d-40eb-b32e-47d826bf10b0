
import { TrendingUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Link } from 'wouter'
import { useLanguage } from '@/contexts/LanguageContext'

interface CalculadoraResults {
  ahorroMensual: number
  ahorroAnual: number
  costoEmmaAnual: number
  roiPorcentaje: number
  serviciosIncluidos: number
  eficienciaGanada: number
}

interface CalculadoraResultsProps {
  results: CalculadoraResults
  moneda: 'MXN' | 'USD' | 'EUR'
  gastoMensual: number
  serviciosActuales: string[]
  limitacionesActuales: string
}

export function CalculadoraResults({ results, moneda, gastoMensual, serviciosActuales, limitacionesActuales }: CalculadoraResultsProps) {
  const { t } = useLanguage()
  const formatCurrency = (amount: number) => {
    // Asegurar que amount es un número válido
    const validAmount = isNaN(amount) || amount === null || amount === undefined ? 0 : Math.round(amount)

    // Formateo específico por moneda
    if (moneda === 'MXN') {
      return `$${validAmount.toLocaleString('es-MX')} MXN`
    } else if (moneda === 'USD') {
      return `$${validAmount.toLocaleString('en-US')} USD`
    } else if (moneda === 'EUR') {
      return `€${validAmount.toLocaleString('es-ES')} EUR`
    }

    return `${validAmount.toLocaleString()}`
  }

  if (gastoMensual === 0) {
    return (
      <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <TrendingUp className="text-gray-400" size={32} />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            {t('calculadora.results.enter_monthly_spend')}
          </h3>
          <p className="text-gray-600">
            {t('calculadora.results.to_calculate_savings')}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-200">
      <div className="flex items-center gap-3 mb-8">
        <div className="w-10 h-10 bg-[#dd3a5a] rounded-xl flex items-center justify-center">
          <TrendingUp className="text-white" size={20} />
        </div>
        <div>
          <h3 className="text-xl font-bold text-gray-900">{t('calculadora.results.title')}</h3>
          <p className="text-gray-600">{t('calculadora.results.auto_calculated')}</p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Ahorro Principal */}
        <div className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 rounded-2xl p-6">
          <div className="text-center">
            <div className="text-sm text-gray-600 mb-1">{t('calculadora.results.total_annual_savings')}</div>
            <div className="text-4xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-2">
              {formatCurrency(results.ahorroAnual)}
            </div>
            <div className="text-sm text-gray-600">
              {formatCurrency(results.ahorroMensual)} {t('calculadora.results.per_month')}
            </div>
          </div>
        </div>

        {/* ROI */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-green-50 rounded-2xl p-4 text-center">
            <div className="text-sm text-green-600 mb-1">ROI</div>
            <div className="text-2xl font-bold text-green-700">
              {Math.round(results.roiPorcentaje)}%
            </div>
          </div>
          <div className="bg-blue-50 rounded-2xl p-4 text-center">
            <div className="text-sm text-blue-600 mb-1">{t('calculadora.results.services_included')}</div>
            <div className="text-2xl font-bold text-blue-700">
              {results.serviciosIncluidos}
            </div>
          </div>
        </div>

        {/* Comparación Persuasiva */}
        <div className="space-y-4">
          <h4 className="font-bold text-gray-900 text-center mb-3">
            {t('calculadora.results.comparison_title')}
          </h4>

          {/* Posts/Contenido */}
          <div className="bg-gradient-to-r from-red-50 to-green-50 rounded-xl p-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-sm text-red-600 font-medium">{t('calculadora.results.currently')}</div>
                <div className="text-lg font-bold text-red-700">{t('calculadora.results.posts_current')}</div>
                <div className="text-xs text-red-600">{t('calculadora.results.posts_current_desc')}</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-green-600 font-medium">{t('calculadora.results.with_emma')}</div>
                <div className="text-lg font-bold text-green-700">{t('calculadora.results.posts_emma')}</div>
                <div className="text-xs text-green-600">{t('calculadora.results.posts_emma_desc')}</div>
              </div>
            </div>
          </div>

          {/* Servicios */}
          <div className="bg-gradient-to-r from-red-50 to-green-50 rounded-xl p-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-sm text-red-600 font-medium">{t('calculadora.results.current_services')}</div>
                <div className="text-lg font-bold text-red-700">
                  {t('calculadora.results.current_services_count')}
                </div>
                <div className="text-xs text-red-600">{t('calculadora.results.services_current_desc')}</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-green-600 font-medium">{t('calculadora.results.with_emma')}</div>
                <div className="text-lg font-bold text-green-700">{t('calculadora.results.services_emma')}</div>
                <div className="text-xs text-green-600">{t('calculadora.results.services_emma_desc')}</div>
              </div>
            </div>
          </div>

          {/* Tiempo de respuesta */}
          <div className="bg-gradient-to-r from-red-50 to-green-50 rounded-xl p-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-sm text-red-600 font-medium">{t('calculadora.results.time_current')}</div>
                <div className="text-lg font-bold text-red-700">{t('calculadora.results.time_current_value')}</div>
                <div className="text-xs text-red-600">{t('calculadora.results.time_current_desc')}</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-green-600 font-medium">{t('calculadora.results.with_emma')}</div>
                <div className="text-lg font-bold text-green-700">{t('calculadora.results.time_emma')}</div>
                <div className="text-xs text-green-600">{t('calculadora.results.time_emma_desc')}</div>
              </div>
            </div>
          </div>

          {limitacionesActuales && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <div className="text-sm font-medium text-red-800 mb-2">
                {t('calculadora.results.current_limitations')}
              </div>
              <div className="text-sm text-red-700 italic">
                "{limitacionesActuales}"
              </div>
              <div className="text-sm font-bold text-green-700 mt-2">
                {t('calculadora.results.emma_eliminates_limitations')}
              </div>
            </div>
          )}
        </div>

        {/* CTA */}
        <div className="pt-4">
          <Link href="/dashboard">
            <Button 
              variant="blue" 
              className="w-full py-4 text-lg font-bold rounded-2xl"
            >
              {t('calculadora.results.cta_button')}
            </Button>
          </Link>
          <div className="text-center mt-3">
            <div className="text-sm text-gray-600">
              {t('calculadora.results.emma_cost_annual')} {formatCurrency(results.costoEmmaAnual)}{t('calculadora.results.per_year_suffix')}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
