"use client"

import { motion } from "framer-motion"
import { useLanguage } from "@/contexts/LanguageContext"

export function WhatIsEmmaSection() {
  const { t, currentLanguage } = useLanguage()

  return (
    <section className="py-16 sm:py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Subtle background elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 right-20 w-32 h-32 bg-[#3018ef] rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 bg-[#dd3a5a] rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="w-full">

          <motion.div
            className="text-left"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            {/* Main Title - Horizontal, Stretched */}
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-gray-900 mb-8 leading-none tracking-tight w-full">
              {currentLanguage === 'en'
                ? 'The marketing system behind brands that execute'
                : 'El sistema de marketing detrás de marcas que ejecutan'}
            </h2>

            {/* Content - Below Title, Left Aligned */}
            <div className="max-w-3xl text-base sm:text-lg text-gray-600 leading-relaxed space-y-4">
              <p>
                <strong className="text-gray-900">
                  {currentLanguage === 'en'
                    ? 'Turn ideas into real campaigns in minutes'
                    : 'Convierte ideas en campañas reales en minutos'}
                </strong> — {currentLanguage === 'en'
                  ? 'no freelancers, no chaos, no friction.'
                  : 'sin freelancers, sin caos, sin fricción.'}
              </p>
              <p>
                {currentLanguage === 'en'
                  ? 'Write, design, launch and optimize from one place, with '
                  : 'Escribe, diseña, lanza y optimiza desde un solo lugar, con '}
                <span className="text-[#3018ef] font-semibold">
                  {currentLanguage === 'en' ? 'intelligent agents' : 'agentes inteligentes'}
                </span>
                {currentLanguage === 'en' ? ' that work with you.' : ' que trabajan contigo.'}
              </p>
              <p>
                <span className="text-[#dd3a5a] font-semibold">
                  {currentLanguage === 'en' ? 'Do it yourself' : 'Hazlo tú'}
                </span>
                {currentLanguage === 'en' ? ' or let ' : ' o deja que '}
                <span className="text-[#3018ef] font-semibold">
                  {currentLanguage === 'en' ? 'Emma execute it for you' : 'Emma lo ejecute por ti'}
                </span>.
              </p>

              {/* Bottom Line */}
              <p className="text-lg sm:text-xl font-bold text-gray-900 pt-6 border-t border-gray-200 mt-8">
                {currentLanguage === 'en'
                  ? 'Automate the heavy. Accelerate the important. Scale without stopping.'
                  : 'Automatiza lo pesado. Acelera lo importante. Escala sin frenar.'}
              </p>
            </div>

          </motion.div>

          {/* Decoración elegante - Formas geométricas flotantes */}
          <div className="absolute top-32 right-4 hidden lg:block">
            <div className="relative w-64 h-64">

              {/* Forma principal - Hexágono con gradiente */}
              <motion.div
                className="absolute top-8 left-8 w-20 h-20 bg-gradient-to-br from-[#3018ef]/20 to-[#dd3a5a]/20 backdrop-blur-sm"
                style={{
                  clipPath: "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)"
                }}
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />

              {/* Círculo con borde animado */}
              <motion.div
                className="absolute top-4 right-4 w-16 h-16 rounded-full border-2 border-[#3018ef]/30"
                animate={{
                  rotate: [0, -360],
                  borderColor: ["#3018ef30", "#dd3a5a30", "#3018ef30"]
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "linear"
                }}
              >
                <div className="absolute inset-2 rounded-full bg-gradient-to-br from-white/80 to-gray-100/80 backdrop-blur-sm"></div>
              </motion.div>

              {/* Triángulo flotante */}
              <motion.div
                className="absolute bottom-12 left-4 w-12 h-12 bg-gradient-to-br from-[#dd3a5a]/25 to-[#3018ef]/25"
                style={{
                  clipPath: "polygon(50% 0%, 0% 100%, 100% 100%)"
                }}
                animate={{
                  y: [0, -8, 0],
                  rotate: [0, 10, -10, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />

              {/* Cuadrado con rotación */}
              <motion.div
                className="absolute bottom-4 right-8 w-10 h-10 bg-gradient-to-br from-[#3018ef]/15 to-transparent backdrop-blur-sm rounded-lg"
                animate={{
                  rotate: [0, 45, 90, 135, 180, 225, 270, 315, 360],
                  scale: [1, 0.8, 1]
                }}
                transition={{
                  duration: 10,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />

              {/* Líneas conectoras animadas */}
              <svg className="absolute inset-0 w-full h-full" viewBox="0 0 256 256">
                <motion.path
                  d="M 48 48 Q 96 32 144 48 T 144 96"
                  stroke="url(#gradient1)"
                  strokeWidth="1"
                  fill="none"
                  strokeDasharray="4 4"
                  animate={{
                    strokeDashoffset: [0, -8]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
                <motion.path
                  d="M 32 128 Q 96 112 160 128"
                  stroke="url(#gradient2)"
                  strokeWidth="1"
                  fill="none"
                  strokeDasharray="3 3"
                  animate={{
                    strokeDashoffset: [0, 6]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
                <defs>
                  <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#3018ef" stopOpacity="0.3" />
                    <stop offset="100%" stopColor="#dd3a5a" stopOpacity="0.3" />
                  </linearGradient>
                  <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#dd3a5a" stopOpacity="0.2" />
                    <stop offset="100%" stopColor="#3018ef" stopOpacity="0.2" />
                  </linearGradient>
                </defs>
              </svg>

              {/* Partículas pequeñas */}
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-[#3018ef] rounded-full"
                  style={{
                    left: `${20 + i * 25}%`,
                    top: `${30 + (i % 3) * 20}%`
                  }}
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0, 1, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.3,
                    ease: "easeInOut"
                  }}
                />
              ))}

            </div>
          </div>

        </div>
      </div>
    </section>
  )
}
