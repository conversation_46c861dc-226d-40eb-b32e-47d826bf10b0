import { SelectOption, Country } from './types';

export const INDUSTRIES: SelectOption[] = [
  { id: "tech", name: "Tecnología", icon: "💻", color: "bg-blue-50 border-blue-200 text-blue-700" },
  { id: "ecommerce", name: "E-commerce", icon: "🛒", color: "bg-green-50 border-green-200 text-green-700" },
  { id: "education", name: "Educación", icon: "🎓", color: "bg-purple-50 border-purple-200 text-purple-700" },
  { id: "health", name: "Salud", icon: "🏥", color: "bg-red-50 border-red-200 text-red-700" },
  { id: "finance", name: "Finanzas", icon: "💰", color: "bg-yellow-50 border-yellow-200 text-yellow-700" },
  { id: "food", name: "Alimentación", icon: "🍕", color: "bg-orange-50 border-orange-200 text-orange-700" },
  { id: "real-estate", name: "Inmobiliaria", icon: "🏠", color: "bg-indigo-50 border-indigo-200 text-indigo-700" },
  { id: "services", name: "Servicios", icon: "💼", color: "bg-gray-50 border-gray-200 text-gray-700" },
];

export const PRODUCT_TYPES: SelectOption[] = [
  { id: "physical", name: "Producto Físico", icon: "📦", description: "Artículos tangibles" },
  { id: "software", name: "Software/SaaS", icon: "💻", description: "Aplicaciones y plataformas" },
  { id: "service", name: "Servicio", icon: "🤝", description: "Consultoría y servicios" },
  { id: "course", name: "Curso/Educación", icon: "🎓", description: "Formación y educación" },
  { id: "content", name: "Contenido Digital", icon: "📱", description: "Ebooks, videos, etc." },
];

export const PRICE_RANGES: SelectOption[] = [
  { id: "free", name: "Gratis", range: "$0", color: "bg-gray-50" },
  { id: "low", name: "Económico", range: "$1 - $50", color: "bg-green-50" },
  { id: "medium", name: "Medio", range: "$51 - $500", color: "bg-blue-50" },
  { id: "high", name: "Premium", range: "$501 - $2,000", color: "bg-purple-50" },
  { id: "enterprise", name: "Enterprise", range: "$2,000+", color: "bg-orange-50" },
];

export const GEOGRAPHIC_SCOPES: SelectOption[] = [
  { id: "local", name: "Local", icon: "🏠", description: "Ciudad o región específica" },
  { id: "national", name: "Nacional", icon: "🏳️", description: "Todo el país" },
  { id: "international", name: "Internacional", icon: "🌍", description: "Múltiples países" },
  { id: "online", name: "Online Global", icon: "💻", description: "Sin límites geográficos" },
];

export const SALES_CHANNELS: SelectOption[] = [
  { id: "physical", name: "Tienda Física", icon: "🏪" },
  { id: "ecommerce", name: "E-commerce", icon: "🛒" },
  { id: "social", name: "Redes Sociales", icon: "📱" },
  { id: "marketplace", name: "Marketplace", icon: "🏬" },
  { id: "b2b", name: "Venta Directa B2B", icon: "🤝" },
  { id: "phone", name: "Teléfono/Call Center", icon: "📞" },
];

export const AUDIENCE_KNOWLEDGE: SelectOption[] = [
  { id: "clear", name: "Tengo claro mi público", icon: "🎯", color: "bg-green-50 border-green-200 text-green-700" },
  { id: "general", name: "Tengo una idea general", icon: "💭", color: "bg-blue-50 border-blue-200 text-blue-700" },
  { id: "unsure", name: "No estoy seguro", icon: "🤔", color: "bg-yellow-50 border-yellow-200 text-yellow-700" },
  { id: "explore", name: "Quiero explorar nuevos segmentos", icon: "🔍", color: "bg-purple-50 border-purple-200 text-purple-700" },
];

export const BUSINESS_SIZES: SelectOption[] = [
  { id: "b2c-students", name: "Estudiantes", icon: "🎓", type: "B2C" },
  { id: "b2c-young", name: "Profesionales Jóvenes", icon: "👨‍💼", type: "B2C" },
  { id: "b2c-families", name: "Familias", icon: "👨‍👩‍👧‍👦", type: "B2C" },
  { id: "b2c-seniors", name: "Jubilados", icon: "👴", type: "B2C" },
  { id: "freelancers", name: "Freelancers", icon: "💻", type: "B2B" },
  { id: "startups", name: "Startups (1-10)", icon: "🚀", type: "B2B" },
  { id: "sme", name: "PYMES (11-50)", icon: "🏢", type: "B2B" },
  { id: "medium", name: "Medianas (51-200)", icon: "🏬", type: "B2B" },
  { id: "enterprise", name: "Corporaciones (200+)", icon: "🏛️", type: "B2B" },
];

export const URGENCY_LEVELS: SelectOption[] = [
  { id: "critical", name: "Crítico - Lo necesitan YA", icon: "🔥", color: "bg-red-50 border-red-200 text-red-700" },
  { id: "important", name: "Importante - Pueden esperar", icon: "⚡", color: "bg-orange-50 border-orange-200 text-orange-700" },
  { id: "nice", name: "Nice-to-have", icon: "✨", color: "bg-blue-50 border-blue-200 text-blue-700" },
  { id: "future", name: "Prevención/Mejora futura", icon: "🔮", color: "bg-purple-50 border-purple-200 text-purple-700" },
];

export const DECISION_MAKERS: SelectOption[] = [
  { id: "individual", name: "Decisión Individual", icon: "👤", type: "B2C" },
  { id: "couple", name: "Decisión en Pareja", icon: "👫", type: "B2C" },
  { id: "family", name: "Decisión Familiar", icon: "👨‍👩‍👧‍👦", type: "B2C" },
  { id: "ceo", name: "CEO/Fundador", icon: "👑", type: "B2B" },
  { id: "manager", name: "Manager/Director", icon: "👨‍💼", type: "B2B" },
  { id: "team", name: "Equipo de Compras", icon: "👥", type: "B2B" },
  { id: "committee", name: "Comité de Decisión", icon: "🏛️", type: "B2B" },
];

// Comprehensive country list organized by regions
export const COUNTRIES: Country[] = [
  // Spanish-speaking Americas
  { code: 'ES', name: 'España', flag: '🇪🇸', region: 'Europa' },
  { code: 'MX', name: 'México', flag: '🇲🇽', region: 'América Latina' },
  { code: 'AR', name: 'Argentina', flag: '🇦🇷', region: 'América Latina' },
  { code: 'CO', name: 'Colombia', flag: '🇨🇴', region: 'América Latina' },
  { code: 'CL', name: 'Chile', flag: '🇨🇱', region: 'América Latina' },
  { code: 'PE', name: 'Perú', flag: '🇵🇪', region: 'América Latina' },
  { code: 'VE', name: 'Venezuela', flag: '🇻🇪', region: 'América Latina' },
  { code: 'EC', name: 'Ecuador', flag: '🇪🇨', region: 'América Latina' },
  { code: 'UY', name: 'Uruguay', flag: '🇺🇾', region: 'América Latina' },
  { code: 'PY', name: 'Paraguay', flag: '🇵🇾', region: 'América Latina' },
  { code: 'BO', name: 'Bolivia', flag: '🇧🇴', region: 'América Latina' },
  { code: 'GT', name: 'Guatemala', flag: '🇬🇹', region: 'América Latina' },
  { code: 'CR', name: 'Costa Rica', flag: '🇨🇷', region: 'América Latina' },
  { code: 'PA', name: 'Panamá', flag: '🇵🇦', region: 'América Latina' },
  { code: 'DO', name: 'República Dominicana', flag: '🇩🇴', region: 'América Latina' },
  { code: 'CU', name: 'Cuba', flag: '🇨🇺', region: 'América Latina' },
  { code: 'HN', name: 'Honduras', flag: '🇭🇳', region: 'América Latina' },
  { code: 'NI', name: 'Nicaragua', flag: '🇳🇮', region: 'América Latina' },
  { code: 'SV', name: 'El Salvador', flag: '🇸🇻', region: 'América Latina' },

  // North America
  { code: 'US', name: 'Estados Unidos', flag: '🇺🇸', region: 'América del Norte' },
  { code: 'CA', name: 'Canadá', flag: '🇨🇦', region: 'América del Norte' },

  // Brazil and Portuguese-speaking
  { code: 'BR', name: 'Brasil', flag: '🇧🇷', region: 'América Latina' },
  { code: 'PT', name: 'Portugal', flag: '🇵🇹', region: 'Europa' },

  // Major European Countries
  { code: 'FR', name: 'Francia', flag: '🇫🇷', region: 'Europa' },
  { code: 'DE', name: 'Alemania', flag: '🇩🇪', region: 'Europa' },
  { code: 'IT', name: 'Italia', flag: '🇮🇹', region: 'Europa' },
  { code: 'GB', name: 'Reino Unido', flag: '🇬🇧', region: 'Europa' },
  { code: 'NL', name: 'Países Bajos', flag: '🇳🇱', region: 'Europa' },
  { code: 'BE', name: 'Bélgica', flag: '🇧🇪', region: 'Europa' },
  { code: 'CH', name: 'Suiza', flag: '🇨🇭', region: 'Europa' },
  { code: 'AT', name: 'Austria', flag: '🇦🇹', region: 'Europa' },
  { code: 'SE', name: 'Suecia', flag: '🇸🇪', region: 'Europa' },
  { code: 'NO', name: 'Noruega', flag: '🇳🇴', region: 'Europa' },
  { code: 'DK', name: 'Dinamarca', flag: '🇩🇰', region: 'Europa' },
  { code: 'FI', name: 'Finlandia', flag: '🇫🇮', region: 'Europa' },
  { code: 'PL', name: 'Polonia', flag: '🇵🇱', region: 'Europa' },
  { code: 'CZ', name: 'República Checa', flag: '🇨🇿', region: 'Europa' },
  { code: 'HU', name: 'Hungría', flag: '🇭🇺', region: 'Europa' },
  { code: 'GR', name: 'Grecia', flag: '🇬🇷', region: 'Europa' },
  { code: 'IE', name: 'Irlanda', flag: '🇮🇪', region: 'Europa' },

  // Asia-Pacific
  { code: 'JP', name: 'Japón', flag: '🇯🇵', region: 'Asia-Pacífico' },
  { code: 'KR', name: 'Corea del Sur', flag: '🇰🇷', region: 'Asia-Pacífico' },
  { code: 'CN', name: 'China', flag: '🇨🇳', region: 'Asia-Pacífico' },
  { code: 'IN', name: 'India', flag: '🇮🇳', region: 'Asia-Pacífico' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺', region: 'Asia-Pacífico' },
  { code: 'NZ', name: 'Nueva Zelanda', flag: '🇳🇿', region: 'Asia-Pacífico' },
  { code: 'SG', name: 'Singapur', flag: '🇸🇬', region: 'Asia-Pacífico' },
  { code: 'TH', name: 'Tailandia', flag: '🇹🇭', region: 'Asia-Pacífico' },
  { code: 'MY', name: 'Malasia', flag: '🇲🇾', region: 'Asia-Pacífico' },
  { code: 'ID', name: 'Indonesia', flag: '🇮🇩', region: 'Asia-Pacífico' },
  { code: 'PH', name: 'Filipinas', flag: '🇵🇭', region: 'Asia-Pacífico' },
  { code: 'VN', name: 'Vietnam', flag: '🇻🇳', region: 'Asia-Pacífico' },

  // Middle East & Africa
  { code: 'AE', name: 'Emiratos Árabes Unidos', flag: '🇦🇪', region: 'Medio Oriente' },
  { code: 'SA', name: 'Arabia Saudí', flag: '🇸🇦', region: 'Medio Oriente' },
  { code: 'IL', name: 'Israel', flag: '🇮🇱', region: 'Medio Oriente' },
  { code: 'TR', name: 'Turquía', flag: '🇹🇷', region: 'Medio Oriente' },
  { code: 'ZA', name: 'Sudáfrica', flag: '🇿🇦', region: 'África' },
  { code: 'EG', name: 'Egipto', flag: '🇪🇬', region: 'África' },
  { code: 'NG', name: 'Nigeria', flag: '🇳🇬', region: 'África' },
  { code: 'KE', name: 'Kenia', flag: '🇰🇪', region: 'África' },

  // Eastern Europe & Russia
  { code: 'RU', name: 'Rusia', flag: '🇷🇺', region: 'Europa del Este' },
  { code: 'UA', name: 'Ucrania', flag: '🇺🇦', region: 'Europa del Este' },
  { code: 'RO', name: 'Rumania', flag: '🇷🇴', region: 'Europa del Este' },
  { code: 'BG', name: 'Bulgaria', flag: '🇧🇬', region: 'Europa del Este' },

  // Other
  { code: 'OTHER', name: 'Otro país', flag: '🌍', region: 'Otros' },
];

// Helper function to get countries by region
export const getCountriesByRegion = () => {
  const regions: Record<string, Country[]> = {};
  COUNTRIES.forEach(country => {
    if (!regions[country.region]) {
      regions[country.region] = [];
    }
    regions[country.region].push(country);
  });
  return regions;
};
