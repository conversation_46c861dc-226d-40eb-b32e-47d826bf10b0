"""
SAIO/GEO Intelligence Engine for Emma Studio
Based on user's research: AI search engines favor structured Q&A content, lists, 
E-E-A-T principles, fresh content with dates, multimedia elements, and authoritative sources
"""

import re
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from bs4 import BeautifulSoup
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

@dataclass
class SAIOAnalysisResult:
    """SAIO analysis results based on AI search optimization research"""
    saio_score: float
    qa_optimization: Dict[str, any]
    list_optimization: Dict[str, any]
    eat_compliance: Dict[str, any]
    freshness_score: float
    multimedia_score: float
    source_authority: Dict[str, any]
    ai_readiness: float
    recommendations: List[Dict[str, str]]

class SAIOIntelligenceEngine:
    """
    SAIO/GEO Intelligence Engine
    Implements AI search optimization based on proven research data
    """
    
    def __init__(self):
        # Question patterns for Q&A detection
        self.question_patterns = [
            r'\b(?:qué|cómo|cuándo|dónde|por qué|quién|cuál|cuáles)\b.*\?',
            r'\b(?:what|how|when|where|why|who|which)\b.*\?',
            r'^\s*¿.*\?',  # Spanish questions
            r'^\s*\d+\.\s*¿.*\?',  # Numbered questions
        ]
        
        # List patterns
        self.list_patterns = [
            r'^\s*[-•*]\s+',  # Bullet points
            r'^\s*\d+\.\s+',  # Numbered lists
            r'<ul>|<ol>',     # HTML lists
            r'<li>',          # List items
        ]
        
        # E-E-A-T indicators
        self.expertise_indicators = [
            'experto', 'especialista', 'profesional', 'certificado', 'años de experiencia',
            'expert', 'specialist', 'professional', 'certified', 'years of experience'
        ]
        
        self.authority_indicators = [
            'fuente', 'estudio', 'investigación', 'universidad', 'instituto',
            'source', 'study', 'research', 'university', 'institute', 'published'
        ]
        
        # Multimedia indicators
        self.multimedia_patterns = [
            r'<img\s+[^>]*src=',
            r'<video\s+[^>]*src=',
            r'<iframe\s+[^>]*src=',
            r'\[imagen\]|\[video\]|\[gráfico\]',
            r'\[image\]|\[video\]|\[chart\]'
        ]
    
    def analyze_saio_optimization(self, content: str, metadata: Dict = None) -> SAIOAnalysisResult:
        """
        Analyze content for SAIO/GEO optimization based on research data
        
        Args:
            content: HTML or plain text content
            metadata: Additional metadata (publish date, author, etc.)
            
        Returns:
            SAIOAnalysisResult with AI search optimization metrics
        """
        try:
            # Parse content
            soup = BeautifulSoup(content, 'html.parser')
            text = soup.get_text()
            
            # Analyze Q&A optimization
            qa_analysis = self._analyze_qa_structure(content, text)
            
            # Analyze list optimization
            list_analysis = self._analyze_list_structure(content, text)
            
            # Analyze E-E-A-T compliance
            eat_analysis = self._analyze_eat_compliance(content, text, metadata)
            
            # Analyze content freshness
            freshness_score = self._analyze_freshness(content, metadata)
            
            # Analyze multimedia elements
            multimedia_score = self._analyze_multimedia(content)
            
            # Analyze source authority
            source_analysis = self._analyze_source_authority(content, text)
            
            # Calculate overall SAIO score
            saio_score = self._calculate_saio_score(
                qa_analysis, list_analysis, eat_analysis, 
                freshness_score, multimedia_score, source_analysis
            )
            
            # Calculate AI readiness
            ai_readiness = self._calculate_ai_readiness(
                qa_analysis, list_analysis, multimedia_score
            )
            
            # Generate recommendations
            recommendations = self._generate_saio_recommendations(
                qa_analysis, list_analysis, eat_analysis,
                freshness_score, multimedia_score, source_analysis
            )
            
            return SAIOAnalysisResult(
                saio_score=saio_score,
                qa_optimization=qa_analysis,
                list_optimization=list_analysis,
                eat_compliance=eat_analysis,
                freshness_score=freshness_score,
                multimedia_score=multimedia_score,
                source_authority=source_analysis,
                ai_readiness=ai_readiness,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"SAIO analysis failed: {str(e)}")
            raise
    
    def _analyze_qa_structure(self, content: str, text: str) -> Dict[str, any]:
        """Analyze Q&A structure - AI search engines favor this format"""
        questions = []
        answers = []
        
        # Find questions using patterns
        for pattern in self.question_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            questions.extend(matches)
        
        # Look for FAQ sections
        faq_sections = re.findall(r'(?:faq|preguntas frecuentes|frequently asked)', 
                                 content, re.IGNORECASE)
        
        # Analyze question-answer pairs
        qa_pairs = self._extract_qa_pairs(content)
        
        # Calculate Q&A score
        qa_score = 0
        if questions:
            qa_score += min(len(questions) * 10, 40)  # Up to 40 points for questions
        if qa_pairs:
            qa_score += min(len(qa_pairs) * 15, 40)  # Up to 40 points for Q&A pairs
        if faq_sections:
            qa_score += 20  # Bonus for FAQ sections
        
        return {
            'score': min(qa_score, 100),
            'questions_found': len(questions),
            'qa_pairs': len(qa_pairs),
            'has_faq_section': len(faq_sections) > 0,
            'questions': questions[:5]  # First 5 questions for review
        }
    
    def _analyze_list_structure(self, content: str, text: str) -> Dict[str, any]:
        """Analyze list structure - AI search engines prefer structured lists"""
        bullet_lists = len(re.findall(r'^\s*[-•*]\s+', text, re.MULTILINE))
        numbered_lists = len(re.findall(r'^\s*\d+\.\s+', text, re.MULTILINE))
        html_lists = len(re.findall(r'<(?:ul|ol)>', content, re.IGNORECASE))
        
        # Calculate list score
        list_score = 0
        total_lists = bullet_lists + numbered_lists + html_lists
        
        if total_lists >= 3:
            list_score = 100
        elif total_lists >= 2:
            list_score = 80
        elif total_lists >= 1:
            list_score = 60
        else:
            list_score = 0
        
        return {
            'score': list_score,
            'bullet_lists': bullet_lists,
            'numbered_lists': numbered_lists,
            'html_lists': html_lists,
            'total_lists': total_lists,
            'has_structured_content': total_lists > 0
        }
    
    def _analyze_eat_compliance(self, content: str, text: str, metadata: Dict = None) -> Dict[str, any]:
        """Analyze E-E-A-T compliance (Experience, Expertise, Authoritativeness, Trust)"""
        expertise_score = 0
        authority_score = 0
        trust_score = 0
        
        # Check for expertise indicators
        for indicator in self.expertise_indicators:
            if indicator.lower() in text.lower():
                expertise_score += 10
        
        # Check for authority indicators
        for indicator in self.authority_indicators:
            if indicator.lower() in text.lower():
                authority_score += 10
        
        # Check for trust signals
        trust_signals = [
            'https://',  # Secure links
            'fuente:',   # Source citations
            'referencia', # References
            'estudio de', # Study references
        ]
        
        for signal in trust_signals:
            if signal.lower() in content.lower():
                trust_score += 15
        
        # Author information
        has_author = bool(metadata and metadata.get('author'))
        if has_author:
            trust_score += 20
        
        # Publication date
        has_date = bool(metadata and metadata.get('publish_date'))
        if has_date:
            trust_score += 10
        
        # Calculate overall E-A-T score
        eat_score = (min(expertise_score, 100) + min(authority_score, 100) + min(trust_score, 100)) / 3
        
        return {
            'score': eat_score,
            'expertise_score': min(expertise_score, 100),
            'authority_score': min(authority_score, 100),
            'trust_score': min(trust_score, 100),
            'has_author': has_author,
            'has_date': has_date
        }
    
    def _analyze_freshness(self, content: str, metadata: Dict = None) -> float:
        """Analyze content freshness - AI search engines favor recent content"""
        if not metadata or not metadata.get('publish_date'):
            # Look for dates in content
            date_patterns = [
                r'\b\d{1,2}/\d{1,2}/\d{4}\b',  # MM/DD/YYYY
                r'\b\d{4}-\d{2}-\d{2}\b',      # YYYY-MM-DD
                r'\b(?:enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)\s+\d{4}\b'
            ]
            
            for pattern in date_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    return 70  # Moderate freshness if date found in content
            
            return 30  # Low freshness if no date found
        
        try:
            publish_date = datetime.fromisoformat(metadata['publish_date'].replace('Z', '+00:00'))
            days_old = (datetime.now() - publish_date).days
            
            if days_old <= 30:
                return 100  # Very fresh
            elif days_old <= 90:
                return 80   # Fresh
            elif days_old <= 180:
                return 60   # Moderately fresh
            elif days_old <= 365:
                return 40   # Aging
            else:
                return 20   # Old content
                
        except:
            return 50  # Default moderate score
    
    def _analyze_multimedia(self, content: str) -> float:
        """Analyze multimedia elements - AI search engines favor rich content"""
        multimedia_count = 0
        
        for pattern in self.multimedia_patterns:
            multimedia_count += len(re.findall(pattern, content, re.IGNORECASE))
        
        # Calculate multimedia score
        if multimedia_count >= 5:
            return 100
        elif multimedia_count >= 3:
            return 80
        elif multimedia_count >= 2:
            return 60
        elif multimedia_count >= 1:
            return 40
        else:
            return 0
    
    def _analyze_source_authority(self, content: str, text: str) -> Dict[str, any]:
        """Analyze source authority and citations"""
        # Find external links
        external_links = re.findall(r'https?://[^\s<>"]+', content)
        
        # Analyze link authority (simplified)
        authoritative_domains = [
            'wikipedia.org', 'gov', 'edu', 'org',
            'pubmed.ncbi.nlm.nih.gov', 'scholar.google.com'
        ]
        
        authoritative_links = 0
        for link in external_links:
            domain = urlparse(link).netloc.lower()
            if any(auth_domain in domain for auth_domain in authoritative_domains):
                authoritative_links += 1
        
        # Calculate authority score
        authority_score = 0
        if authoritative_links >= 3:
            authority_score = 100
        elif authoritative_links >= 2:
            authority_score = 80
        elif authoritative_links >= 1:
            authority_score = 60
        else:
            authority_score = 20 if external_links else 0
        
        return {
            'score': authority_score,
            'total_links': len(external_links),
            'authoritative_links': authoritative_links,
            'has_citations': authoritative_links > 0
        }
    
    def _extract_qa_pairs(self, content: str) -> List[Tuple[str, str]]:
        """Extract question-answer pairs from content"""
        # Simplified Q&A extraction
        qa_pairs = []
        
        # Look for FAQ-style Q&A
        faq_pattern = r'(?:P:|Q:|Pregunta:)\s*([^?]+\?)\s*(?:R:|A:|Respuesta:)\s*([^P^Q^R^A]+)'
        matches = re.findall(faq_pattern, content, re.IGNORECASE | re.DOTALL)
        
        for question, answer in matches:
            qa_pairs.append((question.strip(), answer.strip()))
        
        return qa_pairs
    
    def _calculate_saio_score(self, qa_analysis: Dict, list_analysis: Dict, 
                             eat_analysis: Dict, freshness: float, 
                             multimedia: float, source_analysis: Dict) -> float:
        """Calculate overall SAIO score with weighted factors"""
        weights = {
            'qa': 0.25,      # 25% - Q&A structure is critical for AI
            'lists': 0.20,   # 20% - Structured content
            'eat': 0.20,     # 20% - E-E-A-T compliance
            'freshness': 0.15, # 15% - Content freshness
            'multimedia': 0.10, # 10% - Rich media
            'sources': 0.10   # 10% - Source authority
        }
        
        score = (
            qa_analysis['score'] * weights['qa'] +
            list_analysis['score'] * weights['lists'] +
            eat_analysis['score'] * weights['eat'] +
            freshness * weights['freshness'] +
            multimedia * weights['multimedia'] +
            source_analysis['score'] * weights['sources']
        )
        
        return min(score, 100.0)
    
    def _calculate_ai_readiness(self, qa_analysis: Dict, list_analysis: Dict, 
                               multimedia: float) -> float:
        """Calculate how ready content is for AI search engines"""
        # AI search engines particularly favor Q&A and structured content
        ai_score = (
            qa_analysis['score'] * 0.5 +  # 50% weight for Q&A
            list_analysis['score'] * 0.3 + # 30% weight for lists
            multimedia * 0.2               # 20% weight for multimedia
        )
        
        return min(ai_score, 100.0)
    
    def _generate_saio_recommendations(self, qa_analysis: Dict, list_analysis: Dict,
                                      eat_analysis: Dict, freshness: float,
                                      multimedia: float, source_analysis: Dict) -> List[Dict[str, str]]:
        """Generate actionable SAIO optimization recommendations"""
        recommendations = []
        
        # Q&A recommendations
        if qa_analysis['score'] < 60:
            recommendations.append({
                'category': 'qa',
                'priority': 'high',
                'title': 'Añadir estructura de preguntas y respuestas',
                'description': 'Los motores de búsqueda IA favorecen contenido en formato Q&A',
                'action': 'Incluye 3-5 preguntas frecuentes con respuestas detalladas'
            })
        
        # List recommendations
        if list_analysis['score'] < 60:
            recommendations.append({
                'category': 'structure',
                'priority': 'high',
                'title': 'Mejorar estructura con listas',
                'description': 'El contenido estructurado mejora la comprensión de IA',
                'action': 'Añade listas numeradas o con viñetas para organizar información'
            })
        
        # E-E-A-T recommendations
        if eat_analysis['score'] < 70:
            recommendations.append({
                'category': 'authority',
                'priority': 'medium',
                'title': 'Fortalecer autoridad y experiencia',
                'description': 'Mejora la credibilidad del contenido',
                'action': 'Añade información del autor, fuentes y referencias autoritativas'
            })
        
        # Freshness recommendations
        if freshness < 60:
            recommendations.append({
                'category': 'freshness',
                'priority': 'medium',
                'title': 'Actualizar contenido',
                'description': 'El contenido fresco tiene mejor rendimiento en IA',
                'action': 'Añade fecha de publicación y actualiza información regularmente'
            })
        
        # Multimedia recommendations
        if multimedia < 50:
            recommendations.append({
                'category': 'multimedia',
                'priority': 'medium',
                'title': 'Enriquecer con elementos multimedia',
                'description': 'Los elementos visuales mejoran la experiencia y comprensión',
                'action': 'Incluye imágenes, gráficos o videos relevantes'
            })
        
        return recommendations

# Singleton instance
saio_intelligence_engine = SAIOIntelligenceEngine()
