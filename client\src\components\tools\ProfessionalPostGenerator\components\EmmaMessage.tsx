import React from "react";

interface EmmaMessageProps {
  message: string;
  color?: "blue" | "purple" | "green";
}

const EmmaMessage: React.FC<EmmaMessageProps> = ({ 
  message, 
  color = "blue" 
}) => {
  const colorClasses = {
    blue: "bg-blue-50 border-[#3018ef] text-[#3018ef]",
    purple: "bg-purple-50 border-purple-500 text-purple-600",
    green: "bg-green-50 border-green-500 text-green-600",
  };

  return (
    <div className={`${colorClasses[color].split(' ')[0]} border-l-4 ${colorClasses[color].split(' ')[1]} p-4 mb-6`}>
      <p className="text-sm text-gray-700">
        <span className={`font-medium ${colorClasses[color].split(' ')[2]}`}>Emma:</span> {message}
      </p>
    </div>
  );
};

export default EmmaMessage;
