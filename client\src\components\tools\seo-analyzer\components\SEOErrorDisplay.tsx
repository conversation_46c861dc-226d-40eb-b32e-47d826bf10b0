import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { SEOErrorDisplayProps } from "../types/seo";

export const SEOErrorDisplay: React.FC<SEOErrorDisplayProps> = ({
  error,
  progressError,
  persistentError,
  isError,
}) => {
  // Don't render if no errors
  if (!error && !progressError && !persistentError && !isError) {
    return null;
  }

  const getErrorMessage = (): string => {
    if (error instanceof Error) {
      return error.message;
    }
    
    return (
      progressError ||
      persistentError ||
      "Ocurrió un error al analizar la URL. Por favor, verifica que la URL sea accesible e intenta nuevamente."
    );
  };

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <Alert variant="destructive" className="border-red-300 bg-red-50">
          <AlertCircle className="h-5 w-5" />
          <AlertTitle className="text-lg font-semibold">
            Error en el análisis
          </AlertTitle>
          <AlertDescription className="mt-2">
            {getErrorMessage()}
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};
