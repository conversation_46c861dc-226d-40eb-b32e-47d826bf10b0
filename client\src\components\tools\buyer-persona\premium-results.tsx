"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Brain, MessageCircle, Globe, Star, TrendingUp, Clock, DollarSign, Target, Users, MapPin, Download } from "lucide-react";
import { EmotionalAnalysis } from "./analysis/emotional-analysis";
import { TimingAnalysis } from "./analysis/timing-analysis";
import { LeadScoring } from "./analysis/lead-scoring";
import { GeographicAnalysis } from "./analysis/geographic-analysis";
import { ObjectionsAnalysis } from "./analysis/objections-analysis";
import { DecisionInfluencers } from "./analysis/decision-influencers";
import { ContentPreferences } from "./analysis/content-preferences";
import { ConversationInsights } from "./analysis/conversation-insights";


interface PremiumResultsProps {
  premiumData: any;
  personaName: string;
  onOpenConversationSimulator?: () => void;
}

export function PremiumResults({ premiumData, personaName, onOpenConversationSimulator }: PremiumResultsProps) {
  console.log("PremiumResults received data:", premiumData);

  if (!premiumData || Object.keys(premiumData).length === 0) {
    return null;
  }

  const downloadAvatar = () => {
    if (!premiumData.avatars?.avatar_base64 && !premiumData.avatars?.avatar_url) return;

    try {
      // Determinar el formato de imagen
      const imageUrl = premiumData.avatars.avatar_url || `data:image/png;base64,${premiumData.avatars.avatar_base64}`;
      const isWebp = imageUrl.includes('data:image/webp');
      const extension = isWebp ? 'webp' : 'png';

      // Crear un enlace de descarga
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = `avatar-${personaName.replace(/\s+/g, '-').toLowerCase()}.${extension}`;
      link.target = '_blank'; // Abrir en nueva pestaña como fallback
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Mostrar mensaje de éxito
      console.log(`Avatar descargado: avatar-${personaName.replace(/\s+/g, '-').toLowerCase()}.${extension}`);
    } catch (error) {
      console.error('Error downloading avatar:', error);
      // Fallback: abrir imagen en nueva pestaña
      const imageUrl = premiumData.avatars.avatar_url || `data:image/png;base64,${premiumData.avatars.avatar_base64}`;
      window.open(imageUrl, '_blank');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-12"
    >
      <div className="text-center mb-12">
        <h3 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-4">
          🚀 Análisis Premium con IA
        </h3>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Insights avanzados y estrategias personalizadas para <span className="font-semibold text-purple-700">{personaName}</span>
        </p>
      </div>

      {/* SECTION 1: VISUAL IDENTITY & AVATAR */}
      {premiumData.avatars && (premiumData.avatars.avatar_base64 || premiumData.avatars.avatar_url) && (
        <section className="mb-16">
          <div className="text-center mb-8">
            <h4 className="text-2xl font-bold text-gray-800 mb-2 flex items-center justify-center gap-3">
              <User className="h-6 w-6 text-blue-600" />
              Identidad Visual
            </h4>
            <p className="text-gray-600">Avatar profesional generado con IA para representar a tu buyer persona</p>
          </div>

          <div className="max-w-md mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 text-center">
                <CardContent className="p-8">
                  <div className="relative inline-block mb-6">
                    <img
                      src={premiumData.avatars.avatar_url || `data:image/png;base64,${premiumData.avatars.avatar_base64}`}
                      alt={`Avatar de ${personaName}`}
                      className="w-48 h-48 rounded-full mx-auto shadow-xl border-4 border-white object-cover"
                    />
                    <div className="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-2">
                      <Star className="h-4 w-4" />
                    </div>
                  </div>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 mb-4">
                    {premiumData.avatars.style || 'Profesional'} • {premiumData.avatars.characteristics?.gender || 'Persona'}
                  </Badge>
                  <p className="text-sm text-gray-600 mb-4">
                    Avatar profesional generado con IA
                  </p>
                  <button
                    onClick={downloadAvatar}
                    className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                  >
                    <Download className="h-4 w-4" />
                    Descargar Avatar
                  </button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </section>
      )}

      {/* SECTION 2: BEHAVIORAL INSIGHTS */}
      <section className="mb-16">
        <div className="text-center mb-8">
          <h4 className="text-2xl font-bold text-gray-800 mb-2 flex items-center justify-center gap-3">
            <Brain className="h-6 w-6 text-purple-600" />
            Análisis Comportamental
          </h4>
          <p className="text-gray-600">Insights psicológicos y patrones de comportamiento de compra</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Análisis Emocional */}
          {premiumData.behavior?.predictions?.emotional_analysis && (
            <EmotionalAnalysis
              emotionalData={premiumData.behavior.predictions.emotional_analysis}
              delay={0.1}
            />
          )}

          {/* Lead Scoring y Predicción de Comportamiento */}
          {premiumData.behavior && (
            <LeadScoring
              leadData={premiumData.behavior.predictions?.lead_quality_score}
              purchaseProbability={premiumData.behavior.predictions?.purchase_probability}
              delay={0.2}
            />
          )}

          {/* Timing Inteligente */}
          {premiumData.behavior?.predictions?.intelligent_timing && (
            <TimingAnalysis
              timingData={premiumData.behavior.predictions.intelligent_timing}
              delay={0.3}
            />
          )}

          {/* Análisis de Objeciones */}
          {premiumData.behavior?.predictions?.likely_objections && (
            <ObjectionsAnalysis
              objections={premiumData.behavior.predictions.likely_objections}
              delay={0.4}
            />
          )}
        </div>
      </section>

      {/* SECTION 3: SALES & CONVERSION STRATEGY */}
      <section className="mb-16">
        <div className="text-center mb-8">
          <h4 className="text-2xl font-bold text-gray-800 mb-2 flex items-center justify-center gap-3">
            <Target className="h-6 w-6 text-green-600" />
            Estrategia de Ventas y Conversión
          </h4>
          <p className="text-gray-600">Tácticas optimizadas para maximizar conversiones y cerrar ventas</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Canales de Conversión */}
          {premiumData.behavior?.predictions?.conversion_channels && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-800">
                    <Target className="h-5 w-5" />
                    Canales de Conversión Óptimos
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {premiumData.behavior.predictions.conversion_channels.slice(0, 3).map((channel: any, index: number) => (
                      <div key={index} className="p-4 bg-white rounded-lg shadow-sm border border-green-100">
                        <div className="flex items-center justify-between mb-3">
                          <span className="font-semibold text-gray-800">{channel.channel}</span>
                          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">
                            {channel.effectiveness}% efectividad
                          </Badge>
                        </div>
                        {channel.emotional_appeal && (
                          <p className="text-sm text-gray-600 mb-2">
                            🎯 <strong>Apela a:</strong> {channel.emotional_appeal}
                          </p>
                        )}
                        {channel.optimal_message_type && (
                          <p className="text-sm text-green-700">
                            💬 <strong>Mensaje óptimo:</strong> {channel.optimal_message_type}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Análisis de Precio */}
          {premiumData.behavior?.predictions?.price_sensitivity && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 border-orange-200 h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-800">
                    <DollarSign className="h-5 w-5" />
                    Estrategia de Precios
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-white rounded-lg p-4 border border-orange-100">
                    <span className="text-sm text-gray-600 block mb-1">Rango dispuesto a pagar:</span>
                    <p className="text-2xl font-bold text-green-600">
                      {premiumData.behavior.predictions.price_sensitivity.willing_to_pay}
                    </p>
                  </div>

                  {premiumData.behavior.predictions.price_sensitivity.price_anchoring_strategy && (
                    <div className="p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                      <p className="text-sm font-semibold text-yellow-800 mb-2">💡 Estrategia de Anclaje:</p>
                      <p className="text-sm text-yellow-700">
                        {premiumData.behavior.predictions.price_sensitivity.price_anchoring_strategy}
                      </p>
                    </div>
                  )}

                  {premiumData.behavior.predictions.price_sensitivity.budget_decision_process && (
                    <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                      <p className="text-sm font-semibold text-blue-800 mb-2">🏢 Proceso de Decisión:</p>
                      <p className="text-sm text-blue-700">
                        {premiumData.behavior.predictions.price_sensitivity.budget_decision_process}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Influenciadores de Decisión */}
          {premiumData.behavior?.predictions?.decision_influencers && (
            <DecisionInfluencers
              influencers={premiumData.behavior.predictions.decision_influencers}
              delay={0.3}
            />
          )}

          {/* Preferencias de Contenido */}
          {premiumData.behavior?.predictions?.content_preferences && (
            <ContentPreferences
              contentData={premiumData.behavior.predictions.content_preferences}
              delay={0.4}
            />
          )}
        </div>
      </section>

      {/* SECTION 4: GEOGRAPHIC & MARKET INSIGHTS */}
      {premiumData.geographic && (
        <section className="mb-16">
          <div className="text-center mb-8">
            <h4 className="text-2xl font-bold text-gray-800 mb-2 flex items-center justify-center gap-3">
              <Globe className="h-6 w-6 text-blue-600" />
              Análisis Geográfico y de Mercado
            </h4>
            <p className="text-gray-600">Insights de ubicación, mercado local y preferencias regionales</p>
          </div>

          <div className="max-w-4xl mx-auto">
            <GeographicAnalysis
              geographicData={premiumData.geographic}
              delay={0.1}
            />
          </div>
        </section>
      )}

      {/* SECTION 5: CONVERSATION & INTERACTION TOOLS */}
      <section className="mb-16">
        <div className="text-center mb-8">
          <h4 className="text-2xl font-bold text-gray-800 mb-2 flex items-center justify-center gap-3">
            <MessageCircle className="h-6 w-6 text-green-600" />
            Herramientas de Conversación
          </h4>
          <p className="text-gray-600">Simuladores y análisis para mejorar tus interacciones de ventas</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Conversation Preview */}
          {premiumData.conversation && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-800">
                    <MessageCircle className="h-5 w-5" />
                    Vista Previa de Conversación
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-white rounded-lg p-4">
                    <div className="space-y-3">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-600 mb-1">
                            <strong>{personaName}:</strong>
                          </p>
                          <p className="text-sm bg-gray-100 rounded-lg p-3">
                            {premiumData.conversation.conversation_preview ||
                             "Hola, estoy interesado en conocer más sobre su producto..."}
                          </p>
                        </div>
                      </div>

                      <div className="text-center">
                        <Badge className="bg-green-100 text-green-800">
                          Conversación Simulada Disponible
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Insights de Conversación */}
          {(premiumData.conversation?.insights || premiumData.conversation?.analytics) && (
            <ConversationInsights
              insights={premiumData.conversation?.insights}
              analytics={premiumData.conversation?.analytics}
              delay={0.2}
            />
          )}

        </div>

        {/* Interactive Conversation Simulator - Full Width */}
        <div className="mt-12">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
              <CardHeader className="text-center">
                <CardTitle className="flex items-center justify-center gap-2 text-green-800 text-xl">
                  <MessageCircle className="h-6 w-6" />
                  Simulador de Conversación Interactivo
                </CardTitle>
                <p className="text-green-700 mt-2">Practica tu conversación de ventas con IA avanzada</p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-white rounded-lg p-8 text-center">
                  <div className="mb-6">
                    <MessageCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-gray-800 mb-3">
                      Entrena con {personaName}
                    </h4>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                      Chatea en tiempo real usando IA avanzada. Perfecto para entrenar tu pitch de ventas,
                      manejar objeciones y mejorar tus habilidades de conversación.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600 mb-2">🎯</div>
                      <h5 className="font-semibold text-gray-800 mb-1">Respuestas Realistas</h5>
                      <p className="text-sm text-gray-600">Basadas en el perfil psicológico de la persona</p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600 mb-2">📊</div>
                      <h5 className="font-semibold text-gray-800 mb-1">Análisis en Tiempo Real</h5>
                      <p className="text-sm text-gray-600">Feedback instantáneo sobre tu desempeño</p>
                    </div>
                    <div className="p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600 mb-2">🚀</div>
                      <h5 className="font-semibold text-gray-800 mb-1">Mejora tus Ventas</h5>
                      <p className="text-sm text-gray-600">Practica hasta perfeccionar tu técnica</p>
                    </div>
                  </div>

                  <button
                    onClick={() => {
                      if (onOpenConversationSimulator) {
                        onOpenConversationSimulator();
                      } else {
                        // Fallback to old behavior if callback not provided
                        const personaId = personaName.toLowerCase().replace(/\s+/g, '-');
                        const url = `/dashboard/herramientas/buyer-persona-generator/simulador/${personaId}`;
                        console.log("🔗 Opening conversation simulator (fallback):", url);
                        window.open(url, '_blank');
                      }
                    }}
                    className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors flex items-center gap-3 mx-auto shadow-lg hover:shadow-xl"
                  >
                    <MessageCircle className="h-5 w-5" />
                    Iniciar Conversación Interactiva
                  </button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>


    </motion.div>
  );
}
