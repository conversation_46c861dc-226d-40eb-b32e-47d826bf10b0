"use client"

import { motion } from "framer-motion"
import { useLanguage } from "@/contexts/LanguageContext"

export function BigIdeaSection() {
  const { t } = useLanguage()

  return (
    <section className="py-12 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #3018ef 2px, transparent 2px),
                           radial-gradient(circle at 75% 75%, #dd3a5a 2px, transparent 2px)`,
          backgroundSize: '50px 50px'
        }} />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-6xl mx-auto">
          
          {/* Main Title - MÁS COMPACTO */}
          <motion.div
            className="text-center mb-10"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-black text-gray-900 mb-4">
              🔍 <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                La Gran Idea
              </span>
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Las agencias tradicionales te dan <strong>personas</strong>.<br />
              Emma AI te da un <strong>equipo completo de marketing construido con agentes IA</strong>.
            </p>
          </motion.div>

          {/* Comparison Grid - MÁS COMPACTO */}
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            
            {/* Traditional Agencies */}
            <motion.div
              className="bg-red-50 border-2 border-red-200 rounded-3xl p-6 relative"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <div className="absolute -top-4 left-8">
                <span className="bg-red-500 text-white px-4 py-2 rounded-full font-bold text-sm">
                  ❌ Agencias Tradicionales
                </span>
              </div>
              
              <div className="mt-4 space-y-4">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">👥</span>
                  <span className="text-gray-700">Te dan personas (que necesitan vacaciones)</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-2xl">⏰</span>
                  <span className="text-gray-700">Horarios de oficina (9-5)</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-2xl">📝</span>
                  <span className="text-gray-700">Contratos de 12-24 meses</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-2xl">💸</span>
                  <span className="text-gray-700">$10,000-15,000/mes</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-2xl">🐌</span>
                  <span className="text-gray-700">Setup de 2-4 semanas</span>
                </div>
              </div>
            </motion.div>

            {/* Emma AI */}
            <motion.div
              className="bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-[#3018ef]/30 rounded-3xl p-6 relative"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="absolute -top-4 left-8">
                <span className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white px-4 py-2 rounded-full font-bold text-sm">
                  ✅ Emma AI
                </span>
              </div>
              
              <div className="mt-4 space-y-4">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">🤖</span>
                  <span className="text-gray-700">Equipo de agentes IA especializados</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-2xl">🌙</span>
                  <span className="text-gray-700">Disponible 24/7</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-2xl">🆓</span>
                  <span className="text-gray-700">Sin contratos</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-2xl">💎</span>
                  <span className="text-gray-700">$49-199/mes (95% más barato)</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-2xl">⚡</span>
                  <span className="text-gray-700">Setup en 5 minutos</span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Analogy Section - MÁS COMPACTO */}
          <motion.div
            className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 sm:p-8 shadow-xl border border-gray-200/50 text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              🧠 Analogía que lo explica todo:
            </h3>
            <div className="text-base sm:text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto">
              <p className="mb-4">
                <strong>Emma es como ChatGPT — pero para marketing.</strong>
              </p>
              <p className="mb-4">
                En lugar de preguntarle a un asistente por todo, Emma te da un <strong>equipo de agentes expertos</strong>, 
                cada uno enfocado en una tarea específica — <strong>y hablan entre ellos</strong>.
              </p>
              <div className="flex flex-wrap justify-center gap-3 mt-8">
                <span className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 text-gray-700 px-4 py-2 rounded-full border border-[#3018ef]/20 font-semibold">
                  📝 Agente de Copy
                </span>
                <span className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 text-gray-700 px-4 py-2 rounded-full border border-[#3018ef]/20 font-semibold">
                  🎨 Agente de Diseño
                </span>
                <span className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 text-gray-700 px-4 py-2 rounded-full border border-[#3018ef]/20 font-semibold">
                  📊 Agente de SEO
                </span>
                <span className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 text-gray-700 px-4 py-2 rounded-full border border-[#3018ef]/20 font-semibold">
                  📈 Agente de Analytics
                </span>
              </div>
            </div>
          </motion.div>

        </div>
      </div>
    </section>
  )
}
