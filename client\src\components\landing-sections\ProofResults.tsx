"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import {
  Image as ImageIcon,
  FileText,
  TrendingUp,
  Users,
  Palette,
  Search,
  MessageSquare,
  Target,
  Clock,
  Globe,
  Shield,
  Brain,
  Zap
} from "lucide-react"
import { useLanguage } from "@/contexts/LanguageContext"

// Función para obtener capacidades de Emma traducidas
const getEmmaCapabilities = (t: (key: string) => string) => [
  {
    icon: <ImageIcon className="w-8 h-8" />,
    value: "∞",
    label: t('landing.proof_infinite_creative'),
    description: t('landing.proof_infinite_creative_desc'),
    color: "text-[#3018ef]",
    bgColor: "bg-[#3018ef]/10"
  },
  {
    icon: <Clock className="w-8 h-8" />,
    value: "24/7",
    label: t('landing.proof_never_stops'),
    description: t('landing.proof_never_stops_desc'),
    color: "text-[#dd3a5a]",
    bgColor: "bg-[#dd3a5a]/10"
  },
  {
    icon: <Users className="w-8 h-8" />,
    value: "8",
    label: t('landing.proof_multi_agent'),
    description: t('landing.proof_multi_agent_desc'),
    color: "text-green-600",
    bgColor: "bg-green-100"
  },
  {
    icon: <TrendingUp className="w-8 h-8" />,
    value: "Real-Time",
    label: t('landing.proof_instant_optimization'),
    description: t('landing.proof_instant_optimization_desc'),
    color: "text-[#3018ef]",
    bgColor: "bg-[#3018ef]/10"
  },
  {
    icon: <Globe className="w-8 h-8" />,
    value: "Global",
    label: t('landing.proof_worldwide_scale'),
    description: t('landing.proof_worldwide_scale_desc'),
    color: "text-[#dd3a5a]",
    bgColor: "bg-[#dd3a5a]/10"
  },
  {
    icon: <Shield className="w-8 h-8" />,
    value: "99.7%",
    label: t('landing.proof_zero_errors'),
    description: t('landing.proof_zero_errors_desc'),
    color: "text-[#3018ef]",
    bgColor: "bg-[#3018ef]/10"
  },
  {
    icon: <Brain className="w-8 h-8" />,
    value: "Predictive",
    label: t('landing.proof_sees_future'),
    description: t('landing.proof_sees_future_desc'),
    color: "text-[#dd3a5a]",
    bgColor: "bg-[#dd3a5a]/10"
  },
  {
    icon: <Zap className="w-8 h-8" />,
    value: "Auto",
    label: t('landing.proof_autonomous_growth'),
    description: t('landing.proof_autonomous_growth_desc'),
    color: "text-purple-600",
    bgColor: "bg-purple-100"
  }
]

export function ProofResults() {
  const { t, currentLanguage } = useLanguage()
  const [isVisible, setIsVisible] = useState(false)

  // Obtener capacidades de Emma traducidas
  const emmaCapabilities = getEmmaCapabilities(t)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const section = document.getElementById('proof-section')
    if (section) observer.observe(section)

    return () => observer.disconnect()
  }, [])

  return (
    <section
      id="proof-section"
      className="py-20 sm:py-24 bg-white relative overflow-hidden"
    >
      {/* Clean Background */}
      <div className="absolute inset-0 bg-white" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-12">
          <div
            className={`transition-all duration-700 ease-out ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="inline-block bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm px-8 py-4 rounded-2xl border border-white/20 shadow-lg text-sm font-semibold text-gray-800 mb-8">
              {t('landing.proof_badge')}
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">
              <span dangerouslySetInnerHTML={{ __html: t('landing.proof_title') }} />
            </h2>

            <p className="text-xl sm:text-2xl font-medium text-gray-600 max-w-4xl mx-auto leading-relaxed">
              {t('landing.proof_description')}
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div
          className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '400ms' }}
        >
          {emmaCapabilities.map((stat, index) => (
            <motion.div
              key={index}
              className="text-center p-8 bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl hover:scale-105 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full ${stat.bgColor} mb-4`}>
                <div className={stat.color}>
                  {stat.icon}
                </div>
              </div>

              <div className="text-3xl font-bold text-gray-900 mb-2">
                {stat.value}
              </div>

              <div className="text-lg font-semibold text-gray-800 mb-1">
                {stat.label}
              </div>

              <div className="text-sm text-gray-600">
                {stat.description}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div
          className={`mt-16 text-center transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '600ms' }}
        >
          <div className="inline-flex items-center px-6 py-3 rounded-full text-sm font-semibold bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 text-[#3018ef] mb-4">
            <TrendingUp className="w-4 h-4 mr-2" />
            {t('landing.proof_cta_badge')}
          </div>
        </div>
      </div>
    </section>
  )
}
