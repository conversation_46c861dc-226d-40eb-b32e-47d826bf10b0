/**
 * Advanced Formatting Toolbar Styles
 * Professional Google Docs-style toolbar with Emma branding
 */

.advanced-formatting-toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e8eaed;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: 'Roboto', 'Arial', sans-serif;
  font-size: 14px;
  flex-wrap: wrap;
  position: relative;
  z-index: 10;
}

/* Toolbar Groups */
.toolbar-group {
  display: flex;
  align-items: center;
  gap: 2px;
  position: relative;
}

.toolbar-separator {
  width: 1px;
  height: 24px;
  background: #e8eaed;
  margin: 0 4px;
}

/* Toolbar Buttons */
.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #5f6368;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.toolbar-btn:hover {
  background: #f1f3f4;
  color: #202124;
}

.toolbar-btn.active {
  background: #e8f0fe;
  color: #1a73e8;
}

.toolbar-btn:active {
  background: #d2e3fc;
}

/* Dropdown Buttons */
.toolbar-dropdown {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  border: 1px solid #dadce0;
  border-radius: 4px;
  background: white;
  color: #202124;
  cursor: pointer;
  font-size: 13px;
  min-width: 80px;
  transition: all 0.2s ease;
}

.toolbar-dropdown:hover {
  border-color: #1a73e8;
  box-shadow: 0 1px 6px rgba(32, 33, 36, 0.28);
}

.toolbar-dropdown.size-dropdown {
  min-width: 50px;
}

.dropdown-text {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Dropdown Menus */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #dadce0;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
}

.font-dropdown {
  min-width: 180px;
}

.size-dropdown-menu {
  min-width: 80px;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #202124;
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  transition: background 0.2s ease;
}

.dropdown-item:hover {
  background: #f1f3f4;
}

.dropdown-item.active {
  background: #e8f0fe;
  color: #1a73e8;
}

/* Color Picker */
.color-picker {
  min-width: 280px;
  padding: 16px;
}

.color-section {
  margin-bottom: 16px;
}

.color-section:last-child {
  margin-bottom: 0;
}

.color-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 500;
  color: #5f6368;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 4px;
}

.color-swatch {
  width: 20px;
  height: 20px;
  border: 1px solid #dadce0;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.color-swatch:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.color-swatch[style*="ffffff"] {
  border-color: #dadce0;
}

/* Color Button */
.color-btn {
  flex-direction: column;
  gap: 2px;
  padding: 4px;
}

.color-indicator {
  width: 20px;
  height: 3px;
  border-radius: 1px;
  border: 1px solid #dadce0;
}

/* Emma Branding */
.advanced-formatting-toolbar .toolbar-btn.active {
  background: linear-gradient(135deg, rgba(48, 24, 239, 0.1) 0%, rgba(221, 58, 90, 0.1) 100%);
  color: #3018ef;
  border: 1px solid rgba(48, 24, 239, 0.2);
}

.advanced-formatting-toolbar .toolbar-dropdown:focus,
.advanced-formatting-toolbar .toolbar-dropdown:hover {
  border-color: #3018ef;
  box-shadow: 0 1px 6px rgba(48, 24, 239, 0.28);
}

.advanced-formatting-toolbar .dropdown-item.active {
  background: linear-gradient(135deg, rgba(48, 24, 239, 0.1) 0%, rgba(221, 58, 90, 0.1) 100%);
  color: #3018ef;
}

/* Responsive Design */
@media (max-width: 768px) {
  .advanced-formatting-toolbar {
    padding: 6px 12px;
    gap: 2px;
  }
  
  .toolbar-btn {
    width: 28px;
    height: 28px;
  }
  
  .toolbar-dropdown {
    padding: 4px 6px;
    font-size: 12px;
    min-width: 60px;
  }
  
  .dropdown-menu {
    min-width: 160px;
  }
  
  .color-picker {
    min-width: 240px;
    padding: 12px;
  }
  
  .color-grid {
    grid-template-columns: repeat(8, 1fr);
  }
}

/* Scrollbar Styling */
.dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: #f1f3f4;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: #dadce0;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #bdc1c6;
}

/* Animation */
.dropdown-menu {
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus States */
.toolbar-btn:focus,
.toolbar-dropdown:focus {
  outline: 2px solid #4285f4;
  outline-offset: 2px;
}

.dropdown-item:focus {
  background: #e8f0fe;
  outline: none;
}

/* Loading State */
.toolbar-btn.loading {
  opacity: 0.6;
  cursor: not-allowed;
}

.toolbar-btn.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid #dadce0;
  border-top: 2px solid #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tooltip Enhancement */
.toolbar-btn[title]:hover::before {
  content: attr(title);
  position: absolute;
  bottom: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 1001;
  pointer-events: none;
}

.toolbar-btn[title]:hover::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgba(0, 0, 0, 0.8);
  z-index: 1001;
  pointer-events: none;
}
