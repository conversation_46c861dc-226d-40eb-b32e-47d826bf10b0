import { useState, useCallback, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { SEOAnalysisResult, SEOProgressData, AnalysisMode } from "../types/seo";

interface UsePersistentAnalysisProps {
  url: string;
  analysisMode: AnalysisMode;
  onAnalysisComplete: (result: SEOAnalysisResult) => void;
  onAnalysisError: (error: string) => void;
}

interface UsePersistentAnalysisReturn {
  // State
  persistentAnalysisId: string | null;
  persistentProgress: SEOProgressData | null;
  persistentLoading: boolean;
  persistentError: string | null;
  progressLoading: boolean;
  progressError: string | null;
  
  // Actions
  startPersistentAnalysis: () => Promise<string | null>;
  cancelPersistentAnalysis: () => void;
  clearPersistentAnalysis: () => void;
}

export const usePersistentAnalysis = ({
  url,
  analysisMode,
  onAnalysisComplete,
  onAnalysisError,
}: UsePersistentAnalysisProps): UsePersistentAnalysisReturn => {
  const { toast } = useToast();
  
  // State
  const [persistentAnalysisId, setPersistentAnalysisId] = useState<string | null>(null);
  const [persistentProgress, setPersistentProgress] = useState<SEOProgressData | null>(null);
  const [persistentLoading, setPersistentLoading] = useState(false);
  const [persistentError, setPersistentError] = useState<string | null>(null);
  const [progressLoading, setProgressLoading] = useState(false);
  const [progressError, setProgressError] = useState<string | null>(null);

  // Start persistent analysis
  const startPersistentAnalysis = useCallback(async (): Promise<string | null> => {
    if (!url) {
      toast({
        title: "Error",
        description: "Por favor, ingresa una URL válida",
        variant: "destructive",
      });
      return null;
    }

    try {
      setPersistentLoading(true);
      setPersistentError(null);

      console.log("Starting persistent analysis for:", url);

      const response = await fetch("/api/seo/analyze-website", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: url.trim(),
          mode: analysisMode,
          enable_progress: true,
        }),
      });

      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API Error:", errorData);
        throw new Error(errorData.detail || "Failed to start analysis");
      }

      const data = await response.json();
      console.log("Response data:", data);

      if (data.status === "started") {
        setPersistentAnalysisId(data.analysis_id);
        toast({
          title: "Análisis iniciado",
          description: "El análisis se ejecuta en segundo plano. Puedes cerrar esta página y volver más tarde.",
          duration: 5000,
        });
        return data.analysis_id;
      } else {
        throw new Error(data.error_message || "Failed to start analysis");
      }
    } catch (err) {
      console.error("Error starting persistent analysis:", err);
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setPersistentError(errorMessage);
      onAnalysisError(errorMessage);
      return null;
    } finally {
      setPersistentLoading(false);
    }
  }, [url, analysisMode, toast, onAnalysisError]);

  // Poll progress
  const pollProgress = useCallback(async (analysisId: string) => {
    try {
      setProgressLoading(true);
      setProgressError(null);

      const response = await fetch(`/api/seo/progress/${analysisId}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch progress");
      }

      const progressData = await response.json();
      console.log("Progress data:", progressData);
      setPersistentProgress(progressData);

      // Handle completion
      if (progressData.status === "complete" && progressData.result) {
        onAnalysisComplete(progressData.result);
        toast({
          title: "Análisis completado",
          description: "El análisis SEO se ha completado exitosamente",
        });
      }

      // Handle errors
      if (progressData.status === "error") {
        const errorMessage = progressData.error?.error || "Analysis failed";
        setPersistentError(errorMessage);
        onAnalysisError(errorMessage);
      }

      return progressData;
    } catch (err) {
      console.error("Error polling progress:", err);
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setProgressError(errorMessage);
      throw err;
    } finally {
      setProgressLoading(false);
    }
  }, [onAnalysisComplete, onAnalysisError, toast]);

  // Cancel analysis
  const cancelPersistentAnalysis = useCallback(() => {
    if (persistentAnalysisId) {
      // TODO: Implement cancel API call
      console.log("Cancelling analysis:", persistentAnalysisId);
      setPersistentAnalysisId(null);
      setPersistentProgress(null);
      setPersistentError(null);
      toast({
        title: "Análisis cancelado",
        description: "El análisis ha sido cancelado",
      });
    }
  }, [persistentAnalysisId, toast]);

  // Clear analysis
  const clearPersistentAnalysis = useCallback(() => {
    setPersistentAnalysisId(null);
    setPersistentProgress(null);
    setPersistentError(null);
    setProgressError(null);
  }, []);

  // Auto-poll progress when analysis ID is set
  useEffect(() => {
    if (!persistentAnalysisId) return;

    const interval = setInterval(() => {
      pollProgress(persistentAnalysisId);
    }, 2000); // Poll every 2 seconds

    // Initial poll
    pollProgress(persistentAnalysisId);

    return () => clearInterval(interval);
  }, [persistentAnalysisId, pollProgress]);

  return {
    persistentAnalysisId,
    persistentProgress,
    persistentLoading,
    persistentError,
    progressLoading,
    progressError,
    startPersistentAnalysis,
    cancelPersistentAnalysis,
    clearPersistentAnalysis,
  };
};
