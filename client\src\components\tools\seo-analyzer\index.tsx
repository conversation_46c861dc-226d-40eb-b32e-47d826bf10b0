import React from "react";
import { SEOErrorBoundary } from "./components/SEOErrorBoundary";
import { SEOAnalyzerMain } from "./SEOAnalyzerMain";

/**
 * SEO Analyzer - Refactored Component
 * 
 * This is the main entry point for the SEO Analyzer tool.
 * The component has been refactored from a single 1,300+ line file
 * into smaller, focused components following React best practices.
 * 
 * Features:
 * - Single page analysis (fast, 5-10 seconds)
 * - Exhaustive website analysis (30-60 minutes, persistent)
 * - Real-time progress tracking
 * - AI-enhanced recommendations
 * - Comprehensive SEO auditing
 * - Error boundaries for better error handling
 * - TypeScript interfaces for type safety
 * - Custom hooks for reusable logic
 * - Utility functions for common operations
 */
export const SEOAnalyzer: React.FC = () => {
  return (
    <SEOErrorBoundary>
      <SEOAnalyzerMain />
    </SEOErrorBoundary>
  );
};

export default SEOAnalyzer;

// Re-export types for external use
export type {
  SEOAnalysisResult,
  SEOProgressData,
  SEORecommendation,
  AnalysisMode,
} from "./types/seo";

// Re-export utilities for external use
export {
  calculateSEOScore,
  getSEOScoreColor,
  formatSEOCheckKey,
  copyToClipboard,
} from "./utils/seo-helpers";
