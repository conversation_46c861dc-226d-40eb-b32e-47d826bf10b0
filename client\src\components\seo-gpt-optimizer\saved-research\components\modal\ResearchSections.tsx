/**
 * SEO & GPT Optimizer™ - Research Sections
 * Container component for all research sections
 */

import React from 'react';
import { SavedResearch } from '../../../../../hooks/seo-gpt-optimizer/useSavedResearch';

import IntentAnalysisSection from './sections/IntentAnalysisSection';
import FrequentQuestionsSection from './sections/FrequentQuestionsSection';
import ContentOpportunitiesSection from './sections/ContentOpportunitiesSection';
import GoogleResultsSection from './sections/GoogleResultsSection';
import SocialInsightsSection from './sections/SocialInsightsSection';
import GPTReferenceSection from './sections/GPTReferenceSection';
import ResearchSummarySection from './sections/ResearchSummarySection';
import QualityMetricsSection from './sections/QualityMetricsSection';

interface ResearchSectionsProps {
  research: SavedResearch;
}

const ResearchSections: React.FC<ResearchSectionsProps> = ({ research }) => {
  const results = research.results as any;

  return (
    <div className="space-y-6">
      {/* Intent Analysis */}
      <IntentAnalysisSection data={results?.intent_analysis} />

      {/* Frequent Questions */}
      <FrequentQuestionsSection data={results?.entities_and_questions} />

      {/* Content Opportunities */}
      <ContentOpportunitiesSection data={results?.content_opportunities} />

      {/* Google Results */}
      <GoogleResultsSection data={results?.google_results} />

      {/* Social Insights */}
      <SocialInsightsSection data={results?.social_insights} />

      {/* GPT Reference */}
      <GPTReferenceSection data={results?.gpt_reference} />

      {/* Research Summary */}
      <ResearchSummarySection data={results?.research_summary} />

      {/* Quality Metrics */}
      <QualityMetricsSection data={results?.research_quality_metrics} />
    </div>
  );
};

export default ResearchSections;
