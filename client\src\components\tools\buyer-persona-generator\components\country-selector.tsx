/**
 * Enhanced Country selector component for buyer persona generation
 * Features: Search, regional grouping, improved UX
 */

import { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Globe, X, Search, ChevronDown, MapPin } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { COUNTRIES, getCountriesByRegion } from "@/components/tools/buyer-persona/smart-form/constants";
import { Country } from "@/components/tools/buyer-persona/smart-form/types";

interface CountrySelectorProps {
  selectedCountries: string[];
  onChange: (countries: string[]) => void;
  label?: string;
  description?: string;
  maxSelections?: number;
}

export function CountrySelector({
  selectedCountries,
  onChange,
  label = "Países objetivo",
  description = "Selecciona uno o más países donde se enfocarán los buyer personas",
  maxSelections = 5
}: CountrySelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Memoized country data
  const countriesByRegion = useMemo(() => getCountriesByRegion(), []);

  // Filter countries based on search query
  const filteredCountries = useMemo(() => {
    if (!searchQuery.trim()) return COUNTRIES;

    const query = searchQuery.toLowerCase();
    return COUNTRIES.filter(country =>
      country.name.toLowerCase().includes(query) ||
      country.code.toLowerCase().includes(query) ||
      country.region.toLowerCase().includes(query)
    );
  }, [searchQuery]);

  const handleCountryToggle = (countryCode: string) => {
    if (selectedCountries.includes(countryCode)) {
      // Remove country
      onChange(selectedCountries.filter(code => code !== countryCode));
    } else {
      // Add country (if under limit)
      if (selectedCountries.length < maxSelections) {
        onChange([...selectedCountries, countryCode]);
      }
    }
  };

  const getCountryByCode = (code: string) => {
    return COUNTRIES.find(country => country.code === code);
  };

  const selectedCountryObjects = selectedCountries
    .map(code => getCountryByCode(code))
    .filter(Boolean) as Country[];

  const handleRemoveCountry = (countryCode: string) => {
    onChange(selectedCountries.filter(code => code !== countryCode));
  };

  return (
    <div className="space-y-4">
      <div>
        <FormLabel className="text-sm font-medium text-gray-700 flex items-center gap-2">
          <Globe className="h-4 w-4" />
          {label}
          {selectedCountries.length > 0 && (
            <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
              {selectedCountries.length} seleccionado{selectedCountries.length > 1 ? 's' : ''}
            </Badge>
          )}
        </FormLabel>
        {description && (
          <p className="text-sm text-gray-500 mt-1">{description}</p>
        )}
      </div>

      {/* Selected Countries Display */}
      <AnimatePresence>
        {selectedCountries.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="flex flex-wrap gap-2"
          >
            {selectedCountryObjects.map((country) => (
              <motion.div
                key={country.code}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-2 bg-gradient-to-r from-purple-100 to-blue-100 text-purple-800 px-3 py-2 rounded-full text-sm shadow-sm border border-purple-200"
              >
                <span className="text-base">{country.flag}</span>
                <span className="font-medium">{country.name}</span>
                <span className="text-xs text-purple-600 bg-white/50 px-1.5 py-0.5 rounded-full">
                  {country.region}
                </span>
                <button
                  onClick={() => handleRemoveCountry(country.code)}
                  className="ml-1 hover:bg-purple-200 rounded-full p-1 transition-colors"
                  title={`Eliminar ${country.name}`}
                >
                  <X className="h-3 w-3" />
                </button>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Modern Country Selector with Search */}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between h-12 text-left font-normal border-2 border-dashed border-gray-300 hover:border-purple-400 hover:bg-purple-50 transition-colors"
          >
            <div className="flex items-center gap-2">
              <Globe className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">
                {selectedCountries.length > 0
                  ? `${selectedCountries.length} país${selectedCountries.length > 1 ? 'es' : ''} seleccionado${selectedCountries.length > 1 ? 's' : ''}`
                  : "Seleccionar países..."
                }
              </span>
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <div className="flex items-center border-b px-3">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
              <CommandInput
                placeholder="Buscar países..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
            <CommandList className="max-h-[300px] overflow-y-auto">
              <CommandEmpty>No se encontraron países.</CommandEmpty>

              {/* Group countries by region */}
              {Object.entries(countriesByRegion).map(([region, countries]) => {
                const filteredRegionCountries = countries.filter(country =>
                  filteredCountries.includes(country)
                );

                if (filteredRegionCountries.length === 0) return null;

                return (
                  <CommandGroup key={region} heading={region}>
                    {filteredRegionCountries.map((country) => {
                      const isSelected = selectedCountries.includes(country.code);
                      const isDisabled = !isSelected && selectedCountries.length >= maxSelections;

                      return (
                        <CommandItem
                          key={country.code}
                          value={`${country.name} ${country.code} ${country.region}`}
                          onSelect={() => {
                            if (!isDisabled) {
                              handleCountryToggle(country.code);
                            }
                          }}
                          disabled={isDisabled}
                          className={`flex items-center gap-3 px-3 py-2 cursor-pointer ${
                            isSelected ? 'bg-purple-50 text-purple-700' : ''
                          } ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          <span className="text-lg">{country.flag}</span>
                          <div className="flex-1">
                            <div className="font-medium">{country.name}</div>
                            <div className="text-xs text-gray-500">{country.region}</div>
                          </div>
                          {isSelected && (
                            <div className="w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                              <div className="w-2 h-2 bg-white rounded-full" />
                            </div>
                          )}
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                );
              })}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selection Limit Warning */}
      {selectedCountries.length >= maxSelections && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 text-sm text-amber-700 bg-gradient-to-r from-amber-50 to-orange-50 p-3 rounded-lg border border-amber-200"
        >
          <MapPin className="h-4 w-4" />
          <span>
            Máximo {maxSelections} países seleccionados. Elimina uno para agregar otro.
          </span>
        </motion.div>
      )}

      {/* Quick Selection Hints */}
      {selectedCountries.length === 0 && (
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
          💡 <strong>Consejo:</strong> Seleccionar países específicos ayudará a Emma a crear buyer personas más precisos y culturalmente relevantes.
        </div>
      )}
    </div>
  );
}
