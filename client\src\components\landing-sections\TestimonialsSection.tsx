"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Star, Quote } from "lucide-react"
import { TestimonialsColumn } from "@/components/ui/testimonials-column"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/contexts/LanguageContext"

// Testimonials now use translations with static images
const testimonialImages = [
  "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face"
]

// Removed static column definitions - will be calculated in component

export function TestimonialsSection() {
  const { t, currentLanguage } = useLanguage()
  const [isVisible, setIsVisible] = useState(false)

  const testimonialsData = t('landing.testimonials.testimonials_list') as unknown as Array<{text: string, name: string, role: string}>
  const testimonials = testimonialsData.map((testimonial, index) => ({
    ...testimonial,
    image: testimonialImages[index] || testimonialImages[0]
  }))

  const firstColumn = testimonials.slice(0, 3)
  const secondColumn = testimonials.slice(3, 6)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    const section = document.getElementById('testimonials-section')
    if (section) observer.observe(section)

    return () => observer.disconnect()
  }, [])

  return (
    <section
      id="testimonials-section"
      className="py-20 sm:py-24 bg-white relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-[#3018ef]/5 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-[#dd3a5a]/5 rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <div
            className={`transition-all duration-700 ease-out ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 text-[#3018ef] mb-6">
              <Quote className="w-4 h-4 mr-2" />
              {currentLanguage === 'en'
                ? 'Real Testimonials from Real Clients'
                : 'Testimonios Reales de Clientes Reales'
              }
            </div>

            <h2 className="text-3xl sm:text-5xl md:text-7xl lg:text-8xl font-calendas text-gray-900 mb-6">
              {currentLanguage === 'en'
                ? <>What our <span className="text-[#3018ef]">clients</span> say about Emma Studio</>
                : <>Lo que dicen nuestros <span className="text-[#3018ef]">clientes</span> sobre Emma Studio</>
              }
            </h2>

            <p className="text-sm sm:text-lg md:text-xl lg:text-2xl font-overusedGrotesk text-gray-600 max-w-3xl mx-auto">
              {currentLanguage === 'en'
                ? 'Over 1,000 companies have transformed their marketing with Emma Studio. These are some of their most impactful testimonials.'
                : 'Más de 1,000 empresas han transformado su marketing con Emma Studio. Estos son algunos de sus testimonios más impactantes.'
              }
            </p>
          </div>
        </div>

        {/* Testimonials Grid - Full Width */}
        <div
          className={`transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '400ms' }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 w-full">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl hover:scale-105 transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="flex items-center mb-6">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-16 h-16 rounded-full border-2 border-gray-200 object-cover mr-4 shadow-lg"
                  />
                  <div>
                    <h4 className="font-bold text-gray-900 text-lg">{testimonial.name}</h4>
                    <p className="text-[#3018ef] text-sm font-semibold">{testimonial.role}</p>
                  </div>
                </div>
                <div className="relative mb-6">
                  <Quote className="absolute -top-3 -left-3 w-8 h-8 text-[#3018ef] opacity-30" />
                  <p className="text-gray-700 leading-relaxed pl-6 font-medium text-lg">
                    "{testimonial.text}"
                  </p>
                </div>
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div
          className={`mt-16 grid md:grid-cols-4 gap-6 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '600ms' }}
        >
          {[
            {
              value: "4.9★",
              label: currentLanguage === 'en' ? "Average Rating" : "Calificación Promedio",
              icon: "⭐"
            },
            {
              value: "1,000+",
              label: currentLanguage === 'en' ? "Satisfied Clients" : "Clientes Satisfechos",
              icon: "😊"
            },
            {
              value: "300%",
              label: currentLanguage === 'en' ? "Average ROI Increase" : "Aumento ROI Promedio",
              icon: "📈"
            },
            {
              value: "95%",
              label: currentLanguage === 'en' ? "Retention Rate" : "Tasa de Retención",
              icon: "🎯"
            }
          ].map((stat, index) => (
            <div
              key={index}
              className="text-center p-8 bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl hover:scale-105 transition-all duration-300"
            >
              <div className="text-4xl mb-4">{stat.icon}</div>
              <div className="text-3xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-2">{stat.value}</div>
              <div className="text-sm font-semibold text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div
          className={`text-center mt-16 transition-all duration-700 ease-out ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '800ms' }}
        >
          <div className="bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-12 max-w-3xl mx-auto">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              {currentLanguage === 'en'
                ? 'Want to be the next success story?'
                : '¿Quieres ser el próximo caso de éxito?'
              }
            </h3>
            <p className="text-gray-600 mb-8 font-medium text-lg">
              {t('landing.testimonials.join_companies')}
            </p>

            <div className="flex items-center justify-center mb-8">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
              <span className="ml-3 text-base font-semibold text-gray-600">4.9/5 {t('landing.testimonials.rating_text')}</span>
            </div>

            <Button
              variant="emma_red"
              size="lg"
              onClick={() => window.open('/auth', '_blank')}
              className="px-10 py-4 text-lg font-bold"
            >
              {t('landing.testimonials.cta_button')}
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
