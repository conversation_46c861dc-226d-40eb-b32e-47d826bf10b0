/**
 * Grid view for displaying buyer personas with structured layout
 */

import { motion } from "framer-motion";
import { BuyerPersona, PremiumFeatureData } from "../types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  User, 
  MapPin, 
  GraduationCap, 
  DollarSign, 
  Briefcase, 
  Target, 
  AlertTriangle,
  MessageCircle,
  Heart
} from "lucide-react";

interface PersonaGridViewProps {
  personas: BuyerPersona[];
  premiumData?: PremiumFeatureData | null;
  onOpenConversationSimulator?: (personaIndex: number) => void;
}

export function PersonaGridView({ 
  personas, 
  premiumData, 
  onOpenConversationSimulator 
}: PersonaGridViewProps) {
  
  const getAvatarImageUrl = (persona: BuyerPersona) => {
    // First priority: Auto-generated avatar from persona generation
    if (persona.avatar_url) {
      return persona.avatar_url;
    }

    // Second priority: Premium avatar if available
    if (premiumData?.avatars?.avatar_url) {
      return premiumData.avatars.avatar_url;
    }
    if (premiumData?.avatars?.avatar_base64) {
      return `data:image/png;base64,${premiumData.avatars.avatar_base64}`;
    }

    // Fallback to stock image
    return "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-2">
          Buyer Personas Generados
        </h2>
        <p className="text-gray-600">
          Vista estructurada de tus buyer personas con información organizada
        </p>
      </div>

      {/* Grid Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {personas.map((persona, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="h-full bg-white/95 backdrop-blur-xl border border-[#3018ef]/20 shadow-lg hover:shadow-xl transition-all duration-300">
              {/* Header with Avatar */}
              <CardHeader className="bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 border-b border-[#3018ef]/10">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <img
                      src={getAvatarImageUrl(persona)}
                      alt={`Avatar de ${persona.name}`}
                      className="w-16 h-16 rounded-full object-cover border-2 border-[#3018ef]/20"
                    />
                    {persona.avatar_url && (
                      <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg text-[#3018ef] mb-1">
                      {persona.name}
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      {persona.age} años • {persona.gender}
                    </p>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="p-6 space-y-4">
                {/* Basic Info */}
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-[#3018ef]" />
                    <span className="text-gray-700 truncate">{persona.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <GraduationCap className="h-4 w-4 text-[#dd3a5a]" />
                    <span className="text-gray-700 truncate">{persona.education}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    <span className="text-gray-700 truncate">{persona.income_level}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Heart className="h-4 w-4 text-pink-500" />
                    <span className="text-gray-700 truncate">{persona.marital_status}</span>
                  </div>
                </div>

                {/* Job Info */}
                <div className="bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Briefcase className="h-4 w-4 text-[#3018ef]" />
                    <span className="font-medium text-gray-800">Trabajo</span>
                  </div>
                  <p className="text-sm text-gray-700 font-medium">{persona.job.title}</p>
                  <p className="text-xs text-gray-600">{persona.job.industry}</p>
                </div>

                {/* Goals */}
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-gray-800 text-sm">Objetivos</span>
                  </div>
                  <div className="space-y-1">
                    {persona.goals.slice(0, 2).map((goal, goalIndex) => (
                      <Badge 
                        key={goalIndex} 
                        variant="outline" 
                        className="text-xs bg-green-50 text-green-700 border-green-200"
                      >
                        {goal}
                      </Badge>
                    ))}
                    {persona.goals.length > 2 && (
                      <Badge variant="outline" className="text-xs bg-gray-50 text-gray-600">
                        +{persona.goals.length - 2} más
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Challenges */}
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-4 w-4 text-orange-500" />
                    <span className="font-medium text-gray-800 text-sm">Desafíos</span>
                  </div>
                  <div className="space-y-1">
                    {persona.challenges.slice(0, 2).map((challenge, challengeIndex) => (
                      <Badge 
                        key={challengeIndex} 
                        variant="outline" 
                        className="text-xs bg-orange-50 text-orange-700 border-orange-200"
                      >
                        {challenge}
                      </Badge>
                    ))}
                    {persona.challenges.length > 2 && (
                      <Badge variant="outline" className="text-xs bg-gray-50 text-gray-600">
                        +{persona.challenges.length - 2} más
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Action Button */}
                {onOpenConversationSimulator && (
                  <Button
                    onClick={() => onOpenConversationSimulator(index)}
                    className="w-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#3018ef]/90 hover:to-[#dd3a5a]/90 text-white text-sm"
                    size="sm"
                  >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Simular Conversación
                  </Button>
                )}
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Summary */}
      <div className="text-center mt-8">
        <p className="text-sm text-gray-500 bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 px-4 py-2 rounded-lg inline-block border border-[#3018ef]/10">
          💡 {personas.length} buyer persona{personas.length !== 1 ? 's' : ''} generado{personas.length !== 1 ? 's' : ''} con IA
        </p>
      </div>
    </div>
  );
}
