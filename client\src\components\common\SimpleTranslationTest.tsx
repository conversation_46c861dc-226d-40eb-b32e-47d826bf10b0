import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

const SimpleTranslationTest: React.FC = () => {
  const { t, currentLanguage, changeLanguage } = useLanguage();

  const handleLanguageChange = () => {
    const newLang = currentLanguage === 'es' ? 'en' : 'es';
    console.log('Button clicked! Changing from', currentLanguage, 'to', newLang);
    changeLanguage(newLang);
  };

  return (
    <div className="p-4 border border-gray-300 rounded-lg bg-gray-50">
      <h3 className="text-lg font-bold mb-2">Translation Test</h3>
      
      <div className="mb-4">
        <p><strong>Current Language:</strong> {currentLanguage}</p>
        <p><strong>Test Translation:</strong> {t('common.loading')}</p>
        <p><strong>Navigation Home:</strong> {t('navigation.home')}</p>
        <p><strong>Emma Brand:</strong> {t('emma.brand_name')}</p>
      </div>
      
      <button 
        onClick={handleLanguageChange}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Switch to {currentLanguage === 'es' ? 'English' : 'Español'}
      </button>
      
      <div className="mt-4 text-xs text-gray-600">
        <p>Debug info:</p>
        <p>Current lang: {currentLanguage}</p>
        <p>Translation function working: {typeof t === 'function' ? 'Yes' : 'No'}</p>
      </div>
    </div>
  );
};

export default SimpleTranslationTest;
