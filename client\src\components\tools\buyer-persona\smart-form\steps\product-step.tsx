"use client";

import { motion } from "framer-motion";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { PRODUCT_TYPES } from '../constants';
import { SmartFormData } from '../types';

interface ProductStepProps {
  formData: SmartFormData;
  updateFormData: (field: keyof SmartFormData, value: string) => void;
}

export function ProductStep({ formData, updateFormData }: ProductStepProps) {
  return (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-medium text-gray-700 mb-4 block">
          Tipo de producto o servicio
        </Label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {PRODUCT_TYPES.map((type) => (
            <motion.div
              key={type.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <button
                onClick={() => updateFormData("product_type", type.id)}
                className={`
                  w-full p-4 rounded-lg border-2 text-left transition-all
                  ${formData.product_type === type.id
                    ? 'border-blue-500 bg-blue-50 shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }
                `}
              >
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{type.icon}</span>
                  <div>
                    <div className="font-medium text-gray-900">{type.name}</div>
                    <div className="text-sm text-gray-500">{type.description}</div>
                  </div>
                </div>
              </button>
            </motion.div>
          ))}
        </div>
      </div>

      {formData.product_type && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-3"
        >
          <Label htmlFor="product_name" className="text-base font-medium text-gray-700">
            Nombre de tu {PRODUCT_TYPES.find(p => p.id === formData.product_type)?.name.toLowerCase()}
          </Label>
          <Input
            id="product_name"
            placeholder="Ej: Plataforma de gestión de proyectos"
            value={formData.product_name}
            onChange={(e) => updateFormData("product_name", e.target.value)}
            className="text-lg py-3 border-2 focus:border-blue-500"
          />
        </motion.div>
      )}
    </div>
  );
}
