"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Calendar, AlertTriangle, TrendingUp } from "lucide-react";

interface TimingAnalysisProps {
  timingData: {
    optimal_contact_windows?: {
      primary?: { day: string; time: string; probability: number };
      secondary?: { day: string; time: string; probability: number };
      tertiary?: { day: string; time: string; probability: number };
    };
    avoid_periods?: Array<{ period: string; reason: string }>;
    follow_up_cadence?: {
      initial_response_time?: string;
      follow_up_intervals?: string[];
      max_attempts?: number;
      escalation_timing?: string;
    };
    seasonal_patterns?: {
      high_activity_periods?: string[];
      low_activity_periods?: string[];
      budget_cycles?: string;
    };
    industry_timing?: {
      business_cycles?: string;
      decision_seasons?: string[];
      competitive_timing?: string;
    };
  };
  delay?: number;
}

export function TimingAnalysis({ timingData, delay = 0.3 }: TimingAnalysisProps) {
  if (!timingData || Object.keys(timingData).length === 0) {
    return null;
  }

  const getEffectivenessColor = (probability: number) => {
    if (probability >= 80) return "bg-green-100 text-green-800";
    if (probability >= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay }}
    >
      <Card className="bg-gradient-to-br from-indigo-50 to-blue-100 border-indigo-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-indigo-800">
            <Clock className="h-5 w-5" />
            Timing Inteligente
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Ventanas Óptimas de Contacto */}
          {timingData.optimal_contact_windows && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Mejores Momentos de Contacto
              </h4>
              <div className="space-y-3">
                {Object.entries(timingData.optimal_contact_windows).map(([key, window]: [string, any]) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <span className="font-medium text-sm capitalize text-indigo-700">
                        {key === 'primary' ? '🥇 Primario' : key === 'secondary' ? '🥈 Secundario' : '🥉 Terciario'}:
                      </span>
                      <div className="text-sm text-gray-600 mt-1">
                        <span className="font-medium">{window.day}</span> a las <span className="font-medium">{window.time}</span>
                      </div>
                    </div>
                    <Badge className={getEffectivenessColor(window.probability)}>
                      {window.probability}%
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Cadencia de Seguimiento */}
          {timingData.follow_up_cadence && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Estrategia de Seguimiento
              </h4>
              <div className="space-y-3">
                {timingData.follow_up_cadence.initial_response_time && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Tiempo de respuesta inicial:</span>
                    <Badge variant="outline" className="text-sm font-medium">
                      {timingData.follow_up_cadence.initial_response_time}
                    </Badge>
                  </div>
                )}
                
                {timingData.follow_up_cadence.max_attempts && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Máximo intentos:</span>
                    <Badge variant="outline" className="text-sm font-medium">
                      {timingData.follow_up_cadence.max_attempts}
                    </Badge>
                  </div>
                )}

                {timingData.follow_up_cadence.escalation_timing && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Escalación:</span>
                    <Badge variant="outline" className="text-sm font-medium">
                      {timingData.follow_up_cadence.escalation_timing}
                    </Badge>
                  </div>
                )}

                {timingData.follow_up_cadence.follow_up_intervals && (
                  <div className="mt-3">
                    <span className="text-sm text-gray-600 block mb-2">Intervalos de seguimiento:</span>
                    <div className="flex flex-wrap gap-2">
                      {timingData.follow_up_cadence.follow_up_intervals.map((interval: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {index + 1}. {interval}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Períodos a Evitar */}
          {timingData.avoid_periods && timingData.avoid_periods.length > 0 && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                Períodos a Evitar
              </h4>
              <div className="space-y-2">
                {timingData.avoid_periods.map((period: any, index: number) => (
                  <div key={index} className="flex items-start gap-3 p-2 bg-orange-50 rounded">
                    <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <span className="text-sm font-medium text-orange-800">{period.period}</span>
                      <p className="text-xs text-orange-600 mt-1">{period.reason}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Patrones Estacionales */}
          {timingData.seasonal_patterns && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-blue-500" />
                Patrones Estacionales
              </h4>
              <div className="space-y-3">
                {timingData.seasonal_patterns.high_activity_periods && (
                  <div>
                    <span className="text-sm text-gray-600 block mb-2">🔥 Alta actividad:</span>
                    <div className="flex flex-wrap gap-2">
                      {timingData.seasonal_patterns.high_activity_periods.map((period: string, index: number) => (
                        <Badge key={index} className="bg-green-100 text-green-800 text-xs">
                          {period}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {timingData.seasonal_patterns.low_activity_periods && (
                  <div>
                    <span className="text-sm text-gray-600 block mb-2">❄️ Baja actividad:</span>
                    <div className="flex flex-wrap gap-2">
                      {timingData.seasonal_patterns.low_activity_periods.map((period: string, index: number) => (
                        <Badge key={index} className="bg-blue-100 text-blue-800 text-xs">
                          {period}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {timingData.seasonal_patterns.budget_cycles && (
                  <div className="mt-3 p-3 bg-blue-50 rounded">
                    <span className="text-sm font-medium text-blue-800 block mb-1">💰 Ciclos de Presupuesto:</span>
                    <p className="text-xs text-blue-600">{timingData.seasonal_patterns.budget_cycles}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Timing de Industria */}
          {timingData.industry_timing && (
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-purple-500" />
                Timing de Industria
              </h4>
              <div className="space-y-3">
                {timingData.industry_timing.business_cycles && (
                  <div className="p-3 bg-purple-50 rounded">
                    <span className="text-sm font-medium text-purple-800 block mb-1">🔄 Ciclos de Negocio:</span>
                    <p className="text-xs text-purple-600">{timingData.industry_timing.business_cycles}</p>
                  </div>
                )}

                {timingData.industry_timing.decision_seasons && (
                  <div>
                    <span className="text-sm text-gray-600 block mb-2">🎯 Temporadas de Decisión:</span>
                    <div className="flex flex-wrap gap-2">
                      {timingData.industry_timing.decision_seasons.map((season: string, index: number) => (
                        <Badge key={index} className="bg-purple-100 text-purple-800 text-xs">
                          {season}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {timingData.industry_timing.competitive_timing && (
                  <div className="p-3 bg-gray-50 rounded">
                    <span className="text-sm font-medium text-gray-700 block mb-1">⚔️ Timing Competitivo:</span>
                    <p className="text-xs text-gray-600">{timingData.industry_timing.competitive_timing}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
