/**
 * TypeScript interfaces for Buyer Persona Generator
 */

export interface JobInfo {
  title: string;
  company_size: string;
  industry: string;
  responsibilities: string[];
}

export interface BuyingProcess {
  research_methods: string[];
  decision_factors: string[];
  timeline: string;
}

export interface BuyerPersona {
  name: string;
  age: number;
  gender: string;
  location: string;
  education: string;
  income_level: string;
  marital_status: string;
  job: JobInfo;
  personal_background: string;
  goals: string[];
  challenges: string[];
  buying_process: BuyingProcess;
  objections: string[];
  communication_channels: string[];
  influences: string[];
  quotes: string[];
  typical_day: string;
  brand_affinities: string[];
  avatar_description: string;

  // Avatar fields (optional, generated automatically)
  avatar_url?: string;
  avatar_id?: string;
}

export interface MarketingRecommendation {
  persona_name: string;
  content_types: string[];
  messaging_tips: string[];
  channels: string[];
  content_topics: string[];
}

export interface GenerationResult {
  status: string;
  buyer_personas: BuyerPersona[];
  marketing_recommendations: MarketingRecommendation[];
  general_insights: string[];
  timestamp: string;
  request_id: string;
  error_message?: string;
  original_product_description?: string;
  generation_timestamp?: number;
}

export interface FormData {
  product_description: string;
  industry?: string;
  target_market?: string;
  business_goals?: string;
  competitors?: string;
  num_personas?: number;
  target_countries?: string[]; // Array of country codes
}

export interface PremiumFeatureData {
  avatars?: {
    status: string;
    avatar_id?: string;
    avatar_url?: string;
    avatar_base64?: string;
    style?: string;
    characteristics?: {
      gender: string;
      age: number;
      ethnicity: string;
      style: string;
      description: string;
    };
    metadata?: {
      generated_at: string;
      resolution: string;
      format: string;
    };
  };
  behavior?: {
    status: string;
    predictions?: {
      purchase_probability?: number;
      lead_quality_score?: number;
      conversion_channels?: Array<{
        channel: string;
        effectiveness: number;
        emotional_appeal?: string;
        optimal_message_type?: string;
      }>;
      price_sensitivity?: {
        willing_to_pay: string;
        price_anchoring_strategy?: string;
        budget_decision_process?: string;
      };
      emotional_analysis?: any;
      intelligent_timing?: any;
      likely_objections?: any;
      decision_influencers?: any;
      content_preferences?: any;
    };
  };
  conversation?: {
    status: string;
    conversation_id?: string;
    conversation_preview?: string;
    insights?: any;
    analytics?: any;
  };
  geographic?: {
    status: string;
    analysis?: any;
  };
}

export interface ConversationMessage {
  id: string;
  sender: "user" | "persona";
  message: string;
  timestamp: string;
  persona_state?: {
    interest_level: number;
    trust_level: number;
    urgency_level: number;
  };
}

export interface PersonaState {
  interest_level: number;
  trust_level: number;
  urgency_level: number;
}

export interface ConversationData {
  name: string;
  age: number;
  job: JobInfo;
  goals: string[];
  challenges: string[];
  personal_background?: string;
  influences?: string[];
  objections?: string[];
  communication_channels?: string[];
  avatar_url?: string;
  product_context?: string;
}

export type ViewMode = "form" | "generating" | "results";

export type PremiumFeatureType = "avatars" | "behavior" | "conversation" | "geographic";

export interface ApiError {
  detail: string;
  status_code?: number;
}

export interface LoadingState {
  isLoading: boolean;
  currentStage: number;
  progressValue: number;
  message?: string;
}
